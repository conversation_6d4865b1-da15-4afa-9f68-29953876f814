import { Brackets } from "typeorm";
import * as Sentry from "@sentry/node";
import dbConnection from "../../db/dbConnection";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { ResponseObject } from "../../interface/commonInterface";
import JobApplicationsModel, {
  ApplicationRankStatus,
  Status,
} from "../../schema/s9-innerview/job_applications";
import {
  CANDIDATE_APPLICATION_MSG,
  DEFAULT_LIMIT,
} from "../../utils/constants";
import AuthServices from "../auth/services";
import ApplicantAdditionalInfoModel from "../../schema/s9-innerview/applicant_additional_info";
import { ApplicantAdditionalInfo } from "../resumeScreen/interface";
import InterviewModel from "../../schema/s9-innerview/interview";
// eslint-disable-next-line import/prefer-default-export

export class CandidateApplicationService {
  static async getAllCandidates(
    orgId: number,
    jobId: number,
    isActive?: boolean,
    searchStr: string = "",
    offset: number = 0,
    limit: number = DEFAULT_LIMIT
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const candidatesRepo = dataSource.getRepository(CandidatesModel);

      const query = candidatesRepo
        .createQueryBuilder("candidate")
        .innerJoin(
          "job_applications",
          "job_application",
          "job_application.candidate_id = candidate.id"
        )
        .where("candidate.orgId = :orgId", { orgId });

      if (jobId) {
        query.andWhere("job_application.job_id = :jobId", { jobId });
      }

      if (isActive === undefined) {
        query.andWhere(
          new Brackets((qb) => {
            qb.where("job_application.is_active IS NULL").orWhere(
              "job_application.is_active = :isActive",
              { isActive: true }
            );
          })
        );
      } else {
        query.andWhere("job_application.is_active = :isActive", { isActive });
      }

      if (searchStr.trim().length > 0) {
        query.andWhere("LOWER(candidate.name) LIKE LOWER(:searchStr)", {
          searchStr: `%${searchStr.trim()}%`,
        });
      }

      const data = await query
        .andWhere("job_application.isTopApplication = false")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .offset(offset)
        .limit(limit)
        .select([
          "candidate.id AS candidateId",
          "candidate.name AS candidateName",
          "job_application.id AS applicationId",
          "job_application.status AS applicationStatus",
          "job_application.source AS applicationSource",
          "job_application.created_ts AS applicationCreatedTs",
          "job_application.updated_ts AS applicationUpdatedTs",
          "job_application.is_active AS isActive",
          "job_application.job_id AS job_id",
          "job_application.hiring_manager_id AS hiring_manager_id",
          "job_application.hiring_manager_reason AS hiringManagerReason",
          "job_application.applicationRankStatus AS applicationRankStatus",
          "job_application.ai_reason AS aiReason",
          "job_application.ai_decision AS aiDecision",
          "JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS atsScore",
        ])
        .getRawMany();
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.candidates_fetched,
        data,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_all_candidates_failed,
        error: error.message,
      };
    }
  }

  static async archiveActiveApplication(
    applicationId: number,
    orgId: number,
    status: boolean,
    reason?: string
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const jobApplication = await jobAppRepo
        .createQueryBuilder("job_application")
        .innerJoinAndSelect("job_application.candidate", "candidate")
        .where("job_application.id = :applicationId", { applicationId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }

      jobApplication.isActive = status;
      jobApplication.isTopApplication = false; // Reset top application status when archiving
      jobApplication.applicationRankStatus = ApplicationRankStatus.NO_CHANGES;
      jobApplication.updatedTs = new Date();

      if (status === false && typeof reason === "string") {
        jobApplication.hiringManagerReason = reason.trim();
      } else if (status === true) {
        jobApplication.hiringManagerReason = null;
      }

      await jobAppRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error: error.message,
      };
    }
  }

  // get top candidates

  static getTopCandidates = async (orgId: number, jobId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const query = jobApplicationRepo
        .createQueryBuilder("job_application")
        .innerJoin("job_application.candidate", "candidate")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("job_application.jobId = :jobId", { jobId })
        .andWhere("job_application.isActive = true")
        .andWhere("job_application.isTopApplication = true")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .select([
          "candidate.name AS candidateName",
          "job_application.id AS applicationId",
          "job_application.applicationRankStatus AS applicationRankStatus",
          "candidate.id AS candidateId",
          "job_application.status AS applicationStatus",
          "job_application.source AS applicationSource",
          "job_application.created_ts AS applicationCreatedTs",
          "job_application.updated_ts AS applicationUpdatedTs",
          "job_application.hiring_manager_reason AS hiringManagerReason",
          "job_application.ai_reason AS aiReason",
          "job_application.ai_decision AS aiDecision",
          "job_application.job_id AS job_id",
          "job_application.isTopApplication AS isTopApplication",
          "JSON_UNQUOTE(JSON_EXTRACT(job_application.ats_score, '$.total_ats_score')) AS atsScore",
        ]);

      const data = await query.getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.top_candidates_retrieved,
        data,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_top_candidates_failed,
        error: error.message,
      };
    }
  };

  // promote or demote candidates

  static promoteDemoteCandidate = async (
    candidateId: number,
    applicationId: number,
    action: ApplicationRankStatus.PROMOTED | ApplicationRankStatus.DEMOTED
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const application = await jobAppRepo.findOne({
        where: {
          id: applicationId,
          candidateId,
        },
      });

      if (!application) {
        throw new Error(
          CANDIDATE_APPLICATION_MSG.candidate_application_not_found
        );
      }

      const isTopApplication = action === ApplicationRankStatus.PROMOTED;
      const applicationRankStatus = isTopApplication
        ? ApplicationRankStatus.PROMOTED
        : ApplicationRankStatus.DEMOTED;

      application.isTopApplication = isTopApplication;
      application.applicationRankStatus = applicationRankStatus;

      await jobAppRepo.save(application);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_failed,
      };
    } catch (error: any) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_failed,
        error: error.message,
      };
    }
  };

  static getCandidateDetails = async (
    candidateId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const interviewRepo = dataSource.getRepository(InterviewModel);

      const query = dataSource
        .getRepository(CandidatesModel)
        .createQueryBuilder("candidate")
        .leftJoinAndSelect(
          "job_applications",
          "jobApplication",
          "jobApplication.candidateId = candidate.id"
        )
        .leftJoinAndSelect(
          "jobApplication.job",
          "job",
          "job.id = jobApplication.jobId"
        )

        .leftJoinAndSelect(
          "job.department",
          "department",
          "department.id = job.departmentId"
        )
        // .leftJoinAndSelect(
        //   "jobApplication.statusHistory",
        //   "statusHistory"
        // )
        .where("candidate.id = :candidateId", { candidateId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .select([
          "candidate.name as candidateName",
          "jobApplication.id as jobApplicationId",
          "job.title as jobTitle",
          "jobApplication.status as status",
          "jobApplication.resume_file as resumeLink",
          "jobApplication.hiring_manager_id as hiringManagerId",
          "candidate.imageUrl AS imageUrl",
          "department.name AS department",
        ]);
      // .setParameter('clearedStatus', Status.APPROVED);

      const result = await query.getRawOne();

      const interviewInfo = await interviewRepo.findOne({
        where: {
          jobApplicationId: result.jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });
      console.log("interviewInfo==========>", interviewInfo);
      const interviewerInfo = await AuthServices.getUserByUserId(
        result.hiringManagerId
      );

      if (!result) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found,
        };
      }

      return {
        success: true,
        data: {
          ...result,
          interviewerName: `${interviewerInfo?.first_name} ${interviewerInfo?.last_name}`,
          roundNumber: interviewInfo?.roundNumber,
        },
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.fetch_candidate_details_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static addApplicantAdditionalInfo = async (
    orgId: number,
    body: ApplicantAdditionalInfo
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobApplication = await dataSource
        .getRepository(JobApplicationsModel)
        .findOne({
          where: {
            id: body.applicationId,
          },
        });

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }
      // Step 1: Check if candidate exists for given orgId and candidateId
      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: {
            id: jobApplication.candidateId,
            orgId,
          },
        });

      if (!candidate) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found_for_org,
        };
      }

      // Step 2: Save additional info
      const repo = dataSource.getRepository(ApplicantAdditionalInfoModel);

      const newInfo = new ApplicantAdditionalInfoModel();
      newInfo.description = body.description;
      newInfo.images = body.images ? { urls: [body.images] } : null;

      const savedInfo = await repo.save(newInfo);
      console.log("savedInfo", savedInfo);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.additional_info_saved,
        data: savedInfo,
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.save_additional_info_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static updateJobApplicationStatus = async (
    jobApplicationId: number,
    status: string
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      // Check if job application exists
      const jobApplication = await jobApplicationRepo.findOne({
        where: { id: jobApplicationId },
      });

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_application_not_found,
        };
      }

      // Update the status
      // Convert string to Status enum value
      jobApplication.status = status as Status;

      // Save the updated job application
      const updatedJobApplication =
        await jobApplicationRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
        data: updatedJobApplication,
      };
    } catch (error: unknown) {
      Sentry.captureException(error);
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };
}

export default CandidateApplicationService;

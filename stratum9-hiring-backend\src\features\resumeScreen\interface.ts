export interface CandidateData {
  name: string;
  email: string;
  gender: string;
  additional_details: string;
  resume_file: string; // link
  resume_text: string;
  assessment_file: string; // link
  assessment_text: string;
  imageUrl: string;
}

export interface ManualCandidateUploadRequest {
  organization_id: number;
  hiring_manager_id: number;
  job_id: number;
  candidates: CandidateData[];
}

export interface WeightedCriterion {
  score: number;
  weight_percentage: number;
  out_of: number;
  details: string;
}

export interface ATSAnalysisResponse {
  total_ats_score: number;
  ai_decision: string; // 'Approved' | 'Rejected'
  ai_reason: string;
  weighted_criteria: {
    skills_match: WeightedCriterion;
    experience_relevance: WeightedCriterion;
    education_qualifications: WeightedCriterion;
    keywords_match: WeightedCriterion;
    role_responsibilities: WeightedCriterion;
  };
}

export interface GetAllPendingJobApplicationsRequest {
  job_id: number;
  organization_id: number;
  hiring_manager_id: number;
  limit: number;
  offset: number;
  status: string;
}

export interface PendingJobApplication {
  application_id: number;
  job_id: number;
  hiring_manager_id: number;
  candidate_id: number;
  candidate_name: string;
  ai_decision: string;
  ai_reason: string;
  created_ts: Date;
}

export interface ChangeApplicationStatusRequest {
  job_id: number;
  candidate_id: number;
  hiring_manager_id: number;
  org_id: number;
  status: string; // 'Approved', 'Rejected', or 'On-Hold'
  hiring_manager_reason: string;
}

export interface ApplicantAdditionalInfo {
  applicationId: number;
  description: string;
  images: string; // Optional, since the column is nullable
}

export interface CandidateUploadResult {
  success: boolean;
  candidateData: CandidateData;
  error?: string;
  candidateId?: number;
  jobApplicationId?: number;
}

export interface ManualCandidateUploadResponse {
  success: boolean;
  message: string;
  results: CandidateUploadResult[];
}

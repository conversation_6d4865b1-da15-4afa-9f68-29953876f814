import OpenAI from "openai";
import envConfig from "../../config/envConfig";
import dbConnection from "../../db/dbConnection";
import { ATS_SYSTEM_PROMPT } from "../../prompts";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import JobApplicationsModel, {
  ApplicationRankStatus,
  Status,
} from "../../schema/s9-innerview/job_applications";
import JobApplicationStatusHistoryModel from "../../schema/s9-innerview/job_application_status_history";
import {
  API_RESPONSE_MSG,
  CANDIDATE_AVATAR_URL,
  DEFAULT_LIMIT,
  GPT_MODEL,
} from "../../utils/constants";
import {
  ManualCandidateUploadRequest,
  ATSAnalysisResponse,
  GetAllPendingJobApplicationsRequest,
  ChangeApplicationStatusRequest,
  CandidateUploadResult,
  ManualCandidateUploadResponse,
} from "./interface";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import InterviewModel from "../../schema/s9-innerview/interview";
import {
  ATSAnalysisResult,
  IJobApplication,
} from "../../interface/commonInterface";
import { downloadAndParsePdfFromS3 } from "../../utils/fileUpload";

const CONFIG = envConfig();

/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// Define enums to match the ones in schema files
enum Gender {
  MALE = "Male",
  FEMALE = "Female",
}

enum Source {
  FIVERR = "Fiverr",
  LINKEDIN = "LinkedIn",
  INDEED = "Indeed",
  OTHER = "Other",
}

enum AIDecision {
  APPROVED = "Approved",
  REJECTED = "Rejected",
}

/**
 * ResumeScreen Services class for handling resume screening operations
 * Implements services for candidate uploads and applications
 */

export class ResumeScreenServices {
  private static OPENAI_API_KEY = CONFIG.openai_api_key;

  // Initialize OpenAI client
  private static openAiClient = new OpenAI({
    apiKey: this.OPENAI_API_KEY,
  });
  /**
   * Upload candidates manually and create job applications
   * @param data The candidate data including organization, job, and candidate details
   * @returns Success/failure response with candidates data
   */

  public static async manualCandidateUpload(
    data: ManualCandidateUploadRequest
  ): Promise<ManualCandidateUploadResponse> {
    const dataSource = await dbConnection.getS9InnerviewDataSource();
    const candidatesRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(CandidatesModel);
    const jobApplicationsRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(JobApplicationsModel);
    const jobsRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

    const job = await jobsRepo.findOne({
      where: {
        id: data.job_id,
        orgId: data.organization_id,
      },
    });

    if (!job) {
      return {
        success: false,
        message: "Job not found or unauthorized access",
        results: [],
      };
    }

    const results: CandidateUploadResult[] = [];
    let totalSuccessful = 0;
    let totalFailed = 0;

    /* eslint-disable no-restricted-syntax, no-await-in-loop */
    // Process candidates sequentially to handle duplicates properly
    for (const candidateData of data.candidates) {
      try {
        // Check if candidate with this email already exists
        const existingCandidate = await candidatesRepo.findOne({
          where: { email: candidateData.email.toLocaleLowerCase(), orgId: data.organization_id },
        });

        /* eslint-disable no-plusplus,no-continue,@typescript-eslint/no-shadow */
        if (existingCandidate && existingCandidate?.id) {
          // Case 2: Candidate email already exists
          console.log(`Existing candidate found for email: ${candidateData.email}`);

          // Check if candidate already applied for this job
          const existingApplication = await jobApplicationsRepo.findOne({
            where: {
              candidateId: existingCandidate.id,
              jobId: data.job_id,
              // isActive: true,
            },
          });

          if (existingApplication && existingApplication?.id) {
            // Candidate already exists for this job
            results.push({
              success: false,
              candidateData,
              error: "Candidate already exists for this job",
            });
            totalFailed++;
            continue;
          }

          // Candidate exists but hasn't applied for this job - proceed with parsing and application
          // TODO get response from below funstion like sucess and false then update totalFailed and totalSuccessful
          await ResumeScreenServices.processExistingCandidateApplication(
            existingCandidate,
            candidateData,
            data,
            results,
            dataSource,
            jobApplicationsRepo
          );
          totalSuccessful++;
          continue;
        }

        // Case 1: New candidate - parse resume and assessment, then create candidate and application
        console.log(`Processing new candidate: ${candidateData.email}`);

        // TODO get response from below funstion like sucess and false then update totalFailed and totalSuccessful

        await ResumeScreenServices.processNewCandidate(
          candidateData,
          data,
          results,
          dataSource,
          candidatesRepo,
          jobApplicationsRepo
        );
        totalSuccessful++;
      } catch (outerError) {
        // Handle any errors outside the transaction
        console.log("Error processing candidate", outerError);
        results.push({
          success: false,
          candidateData,
          error: outerError.message || "Failed to process candidate",
        });
        totalFailed++;
      }
    }

    // Return final results
    const overallSuccess = totalSuccessful > 0;
    return {
      success: overallSuccess,
      message: overallSuccess
        ? `Successfully processed ${totalSuccessful} out of ${data.candidates.length} candidates`
        : "Failed to process any candidates",
      results,
    };
  }

  /**
   * Process a new candidate (Case 1)
   * Downloads and parses resume/assessment, creates candidate and job application
   */
  private static async processNewCandidate(
    candidateData: any,
    data: ManualCandidateUploadRequest,
    results: CandidateUploadResult[],
    dataSource: any,
    candidatesRepo: any,
    jobApplicationsRepo: any
  ): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Parse resume from S3
      console.log(`Downloading and parsing resume from: ${candidateData.resume_file}`);
      const resumeParsed = await downloadAndParsePdfFromS3(candidateData.resume_file);

      if (!resumeParsed || !resumeParsed.text || resumeParsed.text.trim() === "") {
        throw new Error("Failed to parse resume or resume is empty");
      }

      // Parse assessment from S3 (optional)
      let assessmentText = "";
      if (candidateData.assessment_file) {
        console.log(`Downloading and parsing assessment from: ${candidateData.assessment_file}`);
        const assessmentParsed = await downloadAndParsePdfFromS3(candidateData.assessment_file);
        if (assessmentParsed && assessmentParsed.text) {
          assessmentText = assessmentParsed.text;
        } else {
          console.warn("Failed to parse assessment file, continuing without it");
        }
      }


      // Generate ATS score
      const atsAnalysis: ATSAnalysisResult = await ResumeScreenServices.analyzeResumeWithATS(
        resumeParsed.text,
        data.job_id,
        data.organization_id
      );


      
      // Create new candidate
      const randomNum =
        candidateData.gender === Gender.FEMALE
          ? Math.floor(Math.random() * 17) + 1 // 1 to 17 for female
          : Math.floor(Math.random() * 13) + 18; // 18 to 30 for male
      const imageUrl = CANDIDATE_AVATAR_URL[randomNum.toString()];

      const candidate = new CandidatesModel();
      candidate.orgId = data.organization_id;
      candidate.name = candidateData.name;
      candidate.email = candidateData.email.toLowerCase();
      candidate.gender = candidateData.gender as Gender;
      candidate.imageUrl = imageUrl;
      const savedCandidate = await candidatesRepo.save(candidate);
      
      console.log("savedCandidate", savedCandidate);

      // Create job application with parsed data
      await ResumeScreenServices.createJobApplicationWithATS(
        savedCandidate.id,
        data,
        candidateData,
        resumeParsed.text,
        assessmentText,
        atsAnalysis,
        jobApplicationsRepo
      );

      await queryRunner.commitTransaction();

      results.push({
        success: true,
        candidateData,
        candidateId: savedCandidate.id,
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error processing new candidate:", error);

      results.push({
        success: false,
        candidateData,
        error: error.message || "Failed to process candidate",
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process an existing candidate (Case 2)
   * Downloads and parses resume/assessment, creates job application only
   */
  private static async processExistingCandidateApplication(
    existingCandidate: CandidatesModel,
    candidateData: any,
    data: ManualCandidateUploadRequest,
    results: CandidateUploadResult[],
    dataSource: any,
    jobApplicationsRepo: any
  ): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Parse resume from S3
      console.log(`Downloading and parsing resume from: ${candidateData.resume_file}`);
      const resumeParsed = await downloadAndParsePdfFromS3(candidateData.resume_file);

      if (!resumeParsed || !resumeParsed.text || resumeParsed.text.trim() === "") {
        throw new Error("Failed to parse resume or resume is empty");
      }

      // Parse assessment from S3 (optional)
      let assessmentText = "";
      if (candidateData.assessment_file) {
        console.log(`Downloading and parsing assessment from: ${candidateData.assessment_file}`);
        const assessmentParsed = await downloadAndParsePdfFromS3(candidateData.assessment_file);
        if (assessmentParsed && assessmentParsed.text) {
          assessmentText = assessmentParsed.text;
        } else {
          console.warn("Failed to parse assessment file, continuing without it");
        }
      }

      // Generate ATS score
      const atsAnalysis: ATSAnalysisResult = await ResumeScreenServices.analyzeResumeWithATS(
        resumeParsed.text,
        data.job_id,
        data.organization_id
      );

      // Create job application with parsed data
      await ResumeScreenServices.createJobApplicationWithATS(
        existingCandidate.id,
        data,
        candidateData,
        resumeParsed.text,
        assessmentText,
        atsAnalysis,
        jobApplicationsRepo
      );

      await queryRunner.commitTransaction();

      results.push({
        success: true,
        candidateData,
        candidateId: existingCandidate.id,
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error processing existing candidate application:", error);

      results.push({
        success: false,
        candidateData,
        error: error.message || "Failed to process candidate application",
      });
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Create job application with ATS analysis and ranking logic
   */
  private static async createJobApplicationWithATS(
    candidateId: number,
    data: ManualCandidateUploadRequest,
    candidateData: any,
    resumeText: string,
    assessmentText: string,
    atsAnalysis: ATSAnalysisResult,
    jobApplicationsRepo: any
  ): Promise<JobApplicationsModel> {
    // Get existing top applications for ranking logic
    const jobApplications = await jobApplicationsRepo
      .createQueryBuilder("application")
      .where("application.job_id = :jobId", { jobId: data.job_id })
      .andWhere("application.is_active = true")
      .andWhere("application.is_top_application = true")
      .orderBy(
        "CAST(JSON_UNQUOTE(JSON_EXTRACT(application.ats_score, '$.total_ats_score')) AS DECIMAL(10,2))",
        "ASC"
      )
      .getMany();

    // Create job application
    const jobApplication = new JobApplicationsModel();
    jobApplication.candidateId = candidateId;
    jobApplication.jobId = data.job_id;
    jobApplication.hiringManagerId = data.hiring_manager_id;
    jobApplication.resumeFile = candidateData.resume_file;
    jobApplication.resumeText = resumeText;
    jobApplication.assessmentFile = candidateData.assessment_file || "";
    jobApplication.assessmentText = assessmentText;
    jobApplication.additionalDetails = candidateData.additional_details || "";
    jobApplication.source = Source.OTHER;

    // Set ATS analysis results
    jobApplication.atsScore = atsAnalysis;
    jobApplication.aiDecision = atsAnalysis.ai_decision === "Approved" ? AIDecision.APPROVED : AIDecision.REJECTED;
    jobApplication.aiReason = atsAnalysis.ai_reason;

    // Determine if this should be a top application
    if (atsAnalysis.ai_decision === "Rejected") {
      jobApplication.isTopApplication = false;
    } else if (jobApplications.length < 10) {
      jobApplication.isTopApplication = true;
      console.log("Promoted new candidate: Less than 10 top applications exist");
    } else {
      // Complex ranking logic for when we have 10+ applications
      const filteredJobApplications = jobApplications.filter(
        (ja) => ja.applicationRankStatus === ApplicationRankStatus.NO_CHANGES
      );

      if (filteredJobApplications.length > 0) {
        const lowestRankedTopApp = filteredJobApplications[0];
        const currentScore = Number(atsAnalysis?.total_ats_score);
        const lowestScore = Number(lowestRankedTopApp.atsScore?.total_ats_score);

        if (currentScore > lowestScore) {
          // Demote the lowest ranked application
          await jobApplicationsRepo.update(lowestRankedTopApp.id, {
            isTopApplication: false,
          });
          jobApplication.isTopApplication = true;
          console.log("Demoted existing candidate. Promoted new candidate with higher ATS score");
        } else {
          jobApplication.isTopApplication = false;
          console.log("Rejected from top 10: Not high enough ATS score");
        }
      } else {
        jobApplication.isTopApplication = false;
      }
    }

    return await jobApplicationsRepo.save(jobApplication);
  }

  /**
   * Get job applications with filtering by job ID, organization ID, status, and pagination support
   * @param filters filters for job_id, organization_id, status, limit, and offset
   * @returns List of job applications with candidate details and pagination metadata
   */
  public static async getAllPendingJobApplications(
    filters: GetAllPendingJobApplicationsRequest
  ) {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Set default values for pagination if not provided
      const limit = filters.limit || DEFAULT_LIMIT;
      const offset = filters.offset || 0;
      const { status } = filters;

      // Build query to get both data and count in a single operation
      const queryBuilder = dataSource
        .getRepository(JobApplicationsModel)
        .createQueryBuilder("app")
        .innerJoin("app.candidate", "c")
        .select([
          "app.id AS application_id",
          "app.jobId AS job_id",
          "app.hiringManagerId AS hiring_manager_id",
          "app.candidateId AS candidate_id",
          "c.name AS candidate_name",
          "app.aiDecision AS ai_decision",
          "app.aiReason AS ai_reason",
          "app.created_ts AS created_ts",
          "app.status AS status",
        ])
        .where("app.jobId = :jobId", { jobId: filters.job_id })
        .andWhere("c.org_id = :orgId", { orgId: filters.organization_id })
        .andWhere("app.hiringManagerId = :hiringManagerId", {
          hiringManagerId: filters.hiring_manager_id,
        })
        .orderBy("app.created_ts", "DESC")
        .limit(limit)
        .offset(offset);

      let applications;
      let totalCount = 0;

      // Add status filter if status is PENDING
      if (status === Status.PENDING) {
        // Add status filter to the main query
        queryBuilder.andWhere("app.status = :status", {
          status: Status.PENDING,
        });

        // Get applications with the pending filter
        applications = await queryBuilder.getRawMany();

        // Get count of only pending applications (use the same filter)
        totalCount = await queryBuilder.clone().getCount();
      } else {
        // get all but not pending
        queryBuilder.andWhere("app.status != :status", {
          status: Status.PENDING,
        });
        // For 'All' status, get applications without status filter
        applications = await queryBuilder.getRawMany();

        // Get count of all applications that match other criteria
        totalCount = await queryBuilder.clone().getCount();
      }

      // Return data with pagination metadata
      return {
        success: true,
        applications,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + applications.length < totalCount,
        },
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.error("Error getting applications:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        error: error.message,
      };
    }
  }

  /**
   * Change the status of a job application
   * @param params Request params containing job_id, candidate_id, hiring_manager_id, and status
   * @param data Request data containing hiring_manager_reason
   * @returns Success/failure response
   */
  public static async changeApplicationStatus(
    data: ChangeApplicationStatusRequest
  ) {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationsRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationsModel
        );
      const statusHistoryRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationStatusHistoryModel
        );

      if (
        ![Status.APPROVED, Status.REJECTED, Status.ON_HOLD].includes(
          data.status as Status
        )
      ) {
        // Validate that status is either APPROVED or REJECTED
        return {
          success: false,
          message: "Invalid status. Must be either Approved or Rejected.",
        };
      }
      // Start a transaction
      const queryRunner = dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // find job is associated with organization or not
        const jobRepo = dataSource.getRepository(JobsModel);

        const job = await jobRepo.findOneBy({
          id: data.job_id,
          orgId: data.org_id,
        });

        if (!job) {
          return {
            success: false,
            message: `Job not found`,
          };
        }

        // Find the job application
        const jobApplication = await jobApplicationsRepo.findOne({
          where: {
            jobId: data.job_id,
            candidateId: data.candidate_id,
            isActive: true,
          },
        });

        if (!jobApplication) {
          return {
            success: false,
            message: API_RESPONSE_MSG.job_application_not_found,
          };
        }

        if (![Status.PENDING, Status.ON_HOLD].includes(jobApplication.status)) {
          return {
            success: false,
            message: `Application status is ${jobApplication.status}. Cannot change status from ${jobApplication.status} to ${data.status}`,
          };
        }
        // Save the previous status for history
        const previousStatus = jobApplication.status;

        const isRejected = data.status === Status.REJECTED;
        // Update the job application status
        jobApplication.status = data.status as Status;
        jobApplication.hiringManagerReason = data.hiring_manager_reason;

        // if reject by hiring manager, then remove form Top Application
        jobApplication.isTopApplication = isRejected
          ? false
          : jobApplication.isTopApplication;
        jobApplication.applicationRankStatus =
          jobApplication.isTopApplication && isRejected
            ? ApplicationRankStatus.DEMOTED
            : jobApplication.applicationRankStatus;

        await jobApplicationsRepo.save(jobApplication);

        // Create a status history record
        const statusHistory = new JobApplicationStatusHistoryModel();
        statusHistory.applicationId = jobApplication.id;
        statusHistory.changedByUserId = data.hiring_manager_id;
        statusHistory.previousStatus = previousStatus;
        statusHistory.newStatus = data.status;
        statusHistory.reason = data.hiring_manager_reason;

        await statusHistoryRepo.save(statusHistory);

        // Commit the transaction
        await queryRunner.commitTransaction();

        return {
          success: true,
          message: API_RESPONSE_MSG.success,
          application: {
            id: jobApplication.id,
            status: jobApplication.status,
          },
        };
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        console.error("Error in changeApplicationStatus transaction:", error);
        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
          error: error.message,
        };
      } finally {
        // Release query runner
        await queryRunner.release();
      }
    } catch (error) {
      console.error("Error in changeApplicationStatus:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
        error: error.message,
      };
    }
  }

  private static async analyzeResumeWithATS(
    resumeContent: string,
    jobId: number,
    orgId: number // ✅ Added orgId to validate job ownership
  ): Promise<ATSAnalysisResponse> {
    const jobRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

    // ✅ Modified query to check both jobId and orgId for access control
    const jobDescription = await jobRepo.findOne({
      where: {
        id: jobId,
        orgId,
      },
    });

    console.log("Job description", jobDescription);

    // ✅ Improved error handling to clearly state why the job was not found
    if (!jobDescription) {
      console.log(`Job not found for ID: ${jobId} and orgId: ${orgId}`);
      return {
        total_ats_score: 0,
        ai_decision: "Rejected",
        ai_reason: "Job description not found with provided ID",
        weighted_criteria: {
          skills_match: {
            score: 0,
            weight_percentage: 30,
            out_of: 30,
            details: "No job description found.",
          },
          experience_relevance: {
            score: 0,
            weight_percentage: 25,
            out_of: 25,
            details: "No job description found.",
          },
          education_qualifications: {
            score: 0,
            weight_percentage: 15,
            out_of: 15,
            details: "No job description found.",
          },
          keywords_match: {
            score: 0,
            weight_percentage: 15,
            out_of: 15,
            details: "No job description found.",
          },
          role_responsibilities: {
            score: 0,
            weight_percentage: 15,
            out_of: 15,
            details: "No job description found.",
          },
        },
      };
    }

    try {
      // ✅ Prepare prompt for OpenAI using the job description and resume
      const userPrompt = `
JOB DESCRIPTION:
${jobDescription}

CANDIDATE RESUME:
${resumeContent}

Please analyze this resume against the job description and provide ATS scoring with detailed breakdown.
`;

      const completion = await this.openAiClient.chat.completions.create({
        model: GPT_MODEL,
        messages: [
          {
            role: "system",
            content: ATS_SYSTEM_PROMPT,
          },
          {
            role: "user",
            content: userPrompt,
          },
        ],
        temperature: 0,
        max_tokens: 2000,
        response_format: { type: "json_object" }, // Ensures JSON response
      });

      const response = completion.choices[0].message.content;
      return JSON.parse(response);
    } catch (error) {
      console.error("OpenAI API Error:", error);
      throw new Error(`ATS Analysis failed: ${error.message}`);
    }
  }
}

export default ResumeScreenServices;

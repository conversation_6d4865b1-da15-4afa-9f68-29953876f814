{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/PreviewResumeIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction PreviewResumeIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M19.4419 1.28516V1.28613C19.546 1.28464 19.6494 1.30297 19.7456 1.34277L19.8169 1.37695C19.8854 1.41401 19.948 1.46142 20.0024 1.51758L27.5933 9.10742V9.1084L27.645 9.16602C27.7603 9.30714 27.8241 9.48419 27.8247 9.66797V27.8438C27.8247 28.605 27.5222 29.3347 26.9839 29.873C26.4456 30.4114 25.7159 30.7138 24.9546 30.7139H7.04346C6.37733 30.7139 5.73491 30.483 5.22412 30.0645L5.01416 29.873C4.47584 29.3347 4.17334 28.6051 4.17334 27.8438V4.15527C4.17338 3.39401 4.47586 2.66428 5.01416 2.12598L5.22412 1.93457C5.7349 1.5161 6.37736 1.28517 7.04346 1.28516H19.4419ZM7.04541 2.87012C6.7472 2.87016 6.45962 2.97377 6.23096 3.16113L6.13721 3.24707C5.89621 3.48807 5.76029 3.81446 5.76025 4.15527V27.8438C5.76026 28.1846 5.89617 28.5119 6.13721 28.7529L6.23096 28.8379C6.45963 29.0253 6.74717 29.1289 7.04541 29.1289H24.9546L25.0806 29.123C25.2063 29.1109 25.3296 29.0796 25.4468 29.0312C25.6029 28.9668 25.7452 28.8723 25.8647 28.7529C25.9842 28.6336 26.0794 28.4919 26.144 28.3359L26.186 28.2168C26.2227 28.096 26.2417 27.9704 26.2417 27.8438V10.4609H21.1753C20.5059 10.4609 19.8635 10.195 19.3901 9.72168C18.9168 9.2483 18.6509 8.60595 18.6509 7.93652V2.87012H7.04541ZM20.2349 7.93652C20.2349 8.18554 20.3332 8.4245 20.5093 8.60059L20.5786 8.66309C20.7456 8.79983 20.9556 8.87597 21.1733 8.87598H25.1196L20.2349 3.99121V7.93652Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M12.3069 6.4873C12.7821 6.33047 13.2913 6.29936 13.7844 6.39746L13.9934 6.44727C14.4755 6.58088 14.9162 6.83691 15.2717 7.19238L15.4182 7.34961C15.7449 7.72854 15.9685 8.18664 16.0667 8.67969L16.1008 8.8916C16.162 9.38833 16.0941 9.89378 15.9016 10.3584C15.7093 10.8228 15.4003 11.2282 15.0061 11.5361L14.8313 11.6621C14.3537 11.9812 13.7924 12.1513 13.218 12.1514H13.217C12.5433 12.1504 11.8938 11.9165 11.3772 11.4932L11.1643 11.2998C10.6198 10.7553 10.3138 10.0171 10.3127 9.24707L10.3206 9.03223C10.3575 8.5332 10.5227 8.05091 10.802 7.63281L10.928 7.45801C11.2359 7.06383 11.6414 6.75485 12.1057 6.5625L12.3069 6.4873ZM13.0872 7.93359C12.7852 7.964 12.5009 8.097 12.2844 8.31348C12.037 8.56093 11.8982 8.89713 11.8977 9.24707L11.9124 9.44043C11.9409 9.63256 12.0116 9.81665 12.1204 9.97949L12.2395 10.1338C12.3699 10.2775 12.5314 10.3909 12.7122 10.4658L12.8977 10.5273C13.0859 10.5743 13.283 10.5791 13.4749 10.541L13.6624 10.4893C13.8453 10.4238 14.0121 10.3182 14.1506 10.1797C14.3352 9.99509 14.461 9.75994 14.512 9.50391L14.5364 9.31055C14.5426 9.18148 14.5296 9.05212 14.4983 8.92676L14.4368 8.74121C14.3618 8.56046 14.2485 8.39894 14.1047 8.26855L13.9504 8.14941C13.7335 8.00453 13.4788 7.92685 13.218 7.92676L13.0872 7.93359Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M14.0181 12.252L14.2183 12.2568C15.2193 12.3076 16.1692 12.7274 16.8813 13.4395L17.02 13.585C17.693 14.328 18.0686 15.2965 18.0698 16.3037V16.7266C18.0698 16.9104 18.0056 17.0875 17.8901 17.2285L17.8374 17.2871C17.6888 17.4356 17.487 17.5186 17.2769 17.5186C17.0931 17.5185 16.9158 17.4552 16.7749 17.3398L16.7163 17.2871C16.5677 17.1385 16.4849 16.9367 16.4849 16.7266V16.3037L16.4722 16.0605C16.4236 15.5765 16.2328 15.1174 15.9243 14.7412L15.7612 14.5605C15.3566 14.1559 14.8257 13.9064 14.2612 13.8496L14.0171 13.8369H12.4175C11.8453 13.8377 11.2937 14.0369 10.855 14.3965L10.6743 14.5605C10.2119 15.023 9.95162 15.6497 9.95068 16.3037V16.7266C9.95062 16.9104 9.8865 17.0875 9.771 17.2285L9.71826 17.2871C9.56964 17.4356 9.36781 17.5186 9.15771 17.5186C8.97399 17.5185 8.7967 17.4552 8.65576 17.3398L8.59717 17.2871C8.44859 17.1385 8.3658 16.9367 8.36572 16.7266V16.3037L8.37061 16.1025C8.42148 15.1015 8.84113 14.1515 9.55322 13.4395L9.69873 13.3018C10.4418 12.6286 11.4101 12.2532 12.4175 12.252H14.0181Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M17.1772 21.002L17.2554 21.0059C17.4369 21.0238 17.6077 21.1034 17.7378 21.2334C17.8864 21.382 17.9701 21.5838 17.9702 21.7939C17.9702 22.0041 17.8864 22.2058 17.7378 22.3545C17.6076 22.4847 17.437 22.5651 17.2554 22.583L17.1772 22.5869H8.77197C8.58813 22.5869 8.411 22.5227 8.27002 22.4072L8.21143 22.3545C8.06294 22.2059 7.97998 22.004 7.97998 21.7939C7.98005 21.5838 8.06284 21.382 8.21143 21.2334L8.27002 21.1807C8.41096 21.0653 8.58824 21.002 8.77197 21.002H17.1772Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M21.9321 24.6504C22.1422 24.6504 22.3441 24.7334 22.4927 24.8818L22.5454 24.9404C22.6609 25.0814 22.725 25.2585 22.7251 25.4424C22.7251 25.6526 22.6413 25.8543 22.4927 26.0029C22.344 26.1516 22.1423 26.2354 21.9321 26.2354H8.77197C8.58813 26.2353 8.411 26.1712 8.27002 26.0557L8.21143 26.0029C8.06294 25.8543 7.97998 25.6525 7.97998 25.4424C7.98005 25.2323 8.06284 25.0304 8.21143 24.8818L8.27002 24.8291C8.41096 24.7137 8.58824 24.6505 8.77197 24.6504H21.9321Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n    </svg>\n  );\n}\n\nexport default PreviewResumeIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAA0B;IAC9D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAIpB;uCAEe", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ArrowDownIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype ArrowDownIconProps = {\n  className?: string;\n};\n\nfunction ArrowDownIcon({ className }: ArrowDownIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"15\" height=\"12\" viewBox=\"0 0 18 11\" fill=\"none\">\n      <path d=\"M2 9L9 2L16 9\" strokeWidth=\"2.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n    </svg>\n  );\n}\n\nexport default ArrowDownIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,SAAS,EAAsB;IACtD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBAC5G,cAAA,8OAAC;YAAK,GAAE;YAAgB,aAAY;YAAM,eAAc;YAAQ,gBAAe;;;;;;;;;;;AAGrF;uCAEe", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/ProgressTracker.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { useTranslations } from \"next-intl\";\n\nimport { IGetInterviewSkillQuestionsResponse } from \"@/interfaces/interviewInterfaces\";\n\n// Define interfaces for our progress tracking\ninterface ProgressState {\n  career: {\n    totalSkills: number;\n    completedSkills: number;\n  };\n  culture: {\n    totalSkills: number;\n    totalQuestions: number;\n    completedQuestions: number;\n    skillProgress: Record<string, number>; // skill name -> completed questions count\n  };\n  role: {\n    totalSkills: number;\n    totalQuestions: number;\n    completedQuestions: number;\n    skillProgress: Record<string, number>; // skill name -> completed questions count\n  };\n}\n\nconst ProgressTracker: React.FC<{ isRecording: boolean }> = ({ isRecording }) => {\n  // Initialize progress state\n  const t = useTranslations();\n  const [progressState, setProgressState] = useState<ProgressState>({\n    career: {\n      totalSkills: 0,\n      completedSkills: 0,\n    },\n    culture: {\n      totalSkills: 0,\n      totalQuestions: 0,\n      completedQuestions: 0,\n      skillProgress: {},\n    },\n    role: {\n      totalSkills: 0,\n      totalQuestions: 0,\n      completedQuestions: 0,\n      skillProgress: {},\n    },\n  });\n\n  // Add state for tracking recording time\n  const [elapsedTime, setElapsedTime] = useState<number>(0);\n\n  // Get interview data from Redux store\n  const interviewData = useSelector((state: { interview: IGetInterviewSkillQuestionsResponse }) => state.interview);\n\n  // Calculate progress percentages\n  const careerProgress = progressState.career.totalSkills > 0 ? (progressState.career.completedSkills / progressState.career.totalSkills) * 100 : 0;\n\n  const cultureProgress =\n    progressState.culture.totalQuestions > 0 ? (progressState.culture.completedQuestions / progressState.culture.totalQuestions) * 100 : 0;\n\n  const roleProgress = progressState.role.totalQuestions > 0 ? (progressState.role.completedQuestions / progressState.role.totalQuestions) * 100 : 0;\n\n  // Calculate overall progress (average of the three sections)\n  const overallProgress = (careerProgress + cultureProgress + roleProgress) / 3;\n\n  // Update progress state based on interview data\n  useEffect(() => {\n    if (!interviewData) return;\n\n    const newProgressState: ProgressState = {\n      career: {\n        totalSkills: 0,\n        completedSkills: 0,\n      },\n      culture: {\n        totalSkills: 0,\n        totalQuestions: 0,\n        completedQuestions: 0,\n        skillProgress: {},\n      },\n      role: {\n        totalSkills: 0,\n        totalQuestions: 0,\n        completedQuestions: 0,\n        skillProgress: {},\n      },\n    };\n\n    // Process career-based questions\n    if (interviewData.careerBasedQuestions) {\n      const careerSkills = Object.keys(interviewData.careerBasedQuestions);\n      newProgressState.career.totalSkills = careerSkills.length;\n\n      // Count completed skills (a skill is completed if it has at least one answered question)\n      newProgressState.career.completedSkills = careerSkills.filter(() => {\n        const questions = interviewData.careerBasedQuestions.questions || [];\n        return questions.some((q) => q.answer && q.answer.trim() !== \"\");\n      }).length;\n    }\n\n    // Process culture-based questions\n    if (interviewData.cultureSpecificQuestions) {\n      const cultureSkills = Object.keys(interviewData.cultureSpecificQuestions);\n      newProgressState.culture.totalSkills = cultureSkills.length;\n\n      let completedQuestions = 0;\n      let totalQuestions = 0;\n\n      cultureSkills.forEach((skill) => {\n        const questions = interviewData.cultureSpecificQuestions[skill].questions || [];\n        totalQuestions += questions.length;\n\n        const skillCompletedQuestions = questions.filter((q) => q.answer && q.answer.trim() !== \"\").length;\n        completedQuestions += skillCompletedQuestions;\n\n        newProgressState.culture.skillProgress[skill] = skillCompletedQuestions;\n      });\n\n      newProgressState.culture.totalQuestions = totalQuestions;\n      newProgressState.culture.completedQuestions = completedQuestions;\n    }\n\n    // Process role-based questions\n    if (interviewData.roleSpecificQuestions) {\n      const roleSkills = Object.keys(interviewData.roleSpecificQuestions);\n      newProgressState.role.totalSkills = roleSkills.length;\n\n      let completedQuestions = 0;\n      let totalQuestions = 0;\n\n      roleSkills.forEach((skill) => {\n        const questions = interviewData.roleSpecificQuestions[skill].questions || [];\n        totalQuestions += questions.length;\n\n        const skillCompletedQuestions = questions.filter((q) => q.answer && q.answer.trim() !== \"\").length;\n        completedQuestions += skillCompletedQuestions;\n\n        newProgressState.role.skillProgress[skill] = skillCompletedQuestions;\n      });\n\n      newProgressState.role.totalQuestions = totalQuestions;\n      newProgressState.role.completedQuestions = completedQuestions;\n    }\n\n    // Update state and save to localStorage\n    setProgressState(newProgressState);\n    localStorage.setItem(\"interviewProgress\", JSON.stringify(newProgressState));\n  }, [interviewData]);\n\n  // Add timer functionality when recording starts/stops\n  useEffect(() => {\n    let timerId: NodeJS.Timeout | null = null;\n\n    if (isRecording) {\n      // Start the timer when recording begins\n      timerId = setInterval(() => {\n        setElapsedTime((prevTime) => prevTime + 1);\n      }, 1000);\n    } else {\n      // Reset the timer when recording stops\n      setElapsedTime(0);\n    }\n\n    // Cleanup the interval when component unmounts or isRecording changes\n    return () => {\n      if (timerId) clearInterval(timerId);\n    };\n  }, [isRecording]);\n\n  // Format the elapsed time as HH:MM:SS\n  const formatTime = (timeInSeconds: number): string => {\n    const hours = Math.floor(timeInSeconds / 3600);\n    const minutes = Math.floor((timeInSeconds % 3600) / 60);\n    const seconds = timeInSeconds % 60;\n\n    return [hours, minutes, seconds].map((val) => val.toString().padStart(2, \"0\")).join(\":\");\n  };\n\n  return (\n    <div className=\"progress-container\">\n      <div className=\"d-flex justify-content-between align-items-center\">\n        <p className=\"time\">{formatTime(elapsedTime)}</p>\n        {isRecording ? <p className=\"status\">{t(\"recording_in_progress\")}</p> : null}\n      </div>\n      <div className=\"progress-tracker\">\n        <div className=\"bar-container\">\n          <div className=\"bar\">\n            <div className=\"progress\" style={{ width: `${overallProgress}%` }} />\n            <div className=\"marker active\" style={{ left: \"0%\", opacity: 0 }} />\n            <div className=\"marker active\" style={{ left: \"33.3333%\" }} />\n            <div className=\"marker active\" style={{ left: \"66.6667%\" }} />\n            <div className=\"marker\" style={{ left: \"100%\", opacity: 0 }} />\n          </div>\n          <div className=\"labels\">\n            <div className=\"label\" style={{ left: \"0%\" }}>\n              {t(\"career_based\") + t(\"interview_\")}\n            </div>\n            <div className=\"label\" style={{ left: \"33.3333%\" }}>\n              {t(\"role_based\") + t(\"interview_\")}\n            </div>\n            <div className=\"label\" style={{ left: \"66.6667%\" }}>\n              {t(\"culture_based\") + t(\"interview_\")}\n            </div>\n            <div className=\"label\" style={{ left: \"100%\" }}></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProgressTracker;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAwBA,MAAM,kBAAsD,CAAC,EAAE,WAAW,EAAE;IAC1E,4BAA4B;IAC5B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,QAAQ;YACN,aAAa;YACb,iBAAiB;QACnB;QACA,SAAS;YACP,aAAa;YACb,gBAAgB;YAChB,oBAAoB;YACpB,eAAe,CAAC;QAClB;QACA,MAAM;YACJ,aAAa;YACb,gBAAgB;YAChB,oBAAoB;YACpB,eAAe,CAAC;QAClB;IACF;IAEA,wCAAwC;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,sCAAsC;IACtC,MAAM,gBAAgB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA8D,MAAM,SAAS;IAEhH,iCAAiC;IACjC,MAAM,iBAAiB,cAAc,MAAM,CAAC,WAAW,GAAG,IAAI,AAAC,cAAc,MAAM,CAAC,eAAe,GAAG,cAAc,MAAM,CAAC,WAAW,GAAI,MAAM;IAEhJ,MAAM,kBACJ,cAAc,OAAO,CAAC,cAAc,GAAG,IAAI,AAAC,cAAc,OAAO,CAAC,kBAAkB,GAAG,cAAc,OAAO,CAAC,cAAc,GAAI,MAAM;IAEvI,MAAM,eAAe,cAAc,IAAI,CAAC,cAAc,GAAG,IAAI,AAAC,cAAc,IAAI,CAAC,kBAAkB,GAAG,cAAc,IAAI,CAAC,cAAc,GAAI,MAAM;IAEjJ,6DAA6D;IAC7D,MAAM,kBAAkB,CAAC,iBAAiB,kBAAkB,YAAY,IAAI;IAE5E,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,mBAAkC;YACtC,QAAQ;gBACN,aAAa;gBACb,iBAAiB;YACnB;YACA,SAAS;gBACP,aAAa;gBACb,gBAAgB;gBAChB,oBAAoB;gBACpB,eAAe,CAAC;YAClB;YACA,MAAM;gBACJ,aAAa;gBACb,gBAAgB;gBAChB,oBAAoB;gBACpB,eAAe,CAAC;YAClB;QACF;QAEA,iCAAiC;QACjC,IAAI,cAAc,oBAAoB,EAAE;YACtC,MAAM,eAAe,OAAO,IAAI,CAAC,cAAc,oBAAoB;YACnE,iBAAiB,MAAM,CAAC,WAAW,GAAG,aAAa,MAAM;YAEzD,yFAAyF;YACzF,iBAAiB,MAAM,CAAC,eAAe,GAAG,aAAa,MAAM,CAAC;gBAC5D,MAAM,YAAY,cAAc,oBAAoB,CAAC,SAAS,IAAI,EAAE;gBACpE,OAAO,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,OAAO;YAC/D,GAAG,MAAM;QACX;QAEA,kCAAkC;QAClC,IAAI,cAAc,wBAAwB,EAAE;YAC1C,MAAM,gBAAgB,OAAO,IAAI,CAAC,cAAc,wBAAwB;YACxE,iBAAiB,OAAO,CAAC,WAAW,GAAG,cAAc,MAAM;YAE3D,IAAI,qBAAqB;YACzB,IAAI,iBAAiB;YAErB,cAAc,OAAO,CAAC,CAAC;gBACrB,MAAM,YAAY,cAAc,wBAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE;gBAC/E,kBAAkB,UAAU,MAAM;gBAElC,MAAM,0BAA0B,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,OAAO,IAAI,MAAM;gBAClG,sBAAsB;gBAEtB,iBAAiB,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG;YAClD;YAEA,iBAAiB,OAAO,CAAC,cAAc,GAAG;YAC1C,iBAAiB,OAAO,CAAC,kBAAkB,GAAG;QAChD;QAEA,+BAA+B;QAC/B,IAAI,cAAc,qBAAqB,EAAE;YACvC,MAAM,aAAa,OAAO,IAAI,CAAC,cAAc,qBAAqB;YAClE,iBAAiB,IAAI,CAAC,WAAW,GAAG,WAAW,MAAM;YAErD,IAAI,qBAAqB;YACzB,IAAI,iBAAiB;YAErB,WAAW,OAAO,CAAC,CAAC;gBAClB,MAAM,YAAY,cAAc,qBAAqB,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE;gBAC5E,kBAAkB,UAAU,MAAM;gBAElC,MAAM,0BAA0B,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,OAAO,IAAI,MAAM;gBAClG,sBAAsB;gBAEtB,iBAAiB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;YAC/C;YAEA,iBAAiB,IAAI,CAAC,cAAc,GAAG;YACvC,iBAAiB,IAAI,CAAC,kBAAkB,GAAG;QAC7C;QAEA,wCAAwC;QACxC,iBAAiB;QACjB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D,GAAG;QAAC;KAAc;IAElB,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAiC;QAErC,IAAI,aAAa;YACf,wCAAwC;YACxC,UAAU,YAAY;gBACpB,eAAe,CAAC,WAAa,WAAW;YAC1C,GAAG;QACL,OAAO;YACL,uCAAuC;YACvC,eAAe;QACjB;QAEA,sEAAsE;QACtE,OAAO;YACL,IAAI,SAAS,cAAc;QAC7B;IACF,GAAG;QAAC;KAAY;IAEhB,sCAAsC;IACtC,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,gBAAgB,OAAQ;QACpD,MAAM,UAAU,gBAAgB;QAEhC,OAAO;YAAC;YAAO;YAAS;SAAQ,CAAC,GAAG,CAAC,CAAC,MAAQ,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;IACtF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAQ,WAAW;;;;;;oBAC/B,4BAAc,8OAAC;wBAAE,WAAU;kCAAU,EAAE;;;;;+BAAgC;;;;;;;0BAE1E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAW,OAAO;wCAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;oCAAC;;;;;;8CAChE,8OAAC;oCAAI,WAAU;oCAAgB,OAAO;wCAAE,MAAM;wCAAM,SAAS;oCAAE;;;;;;8CAC/D,8OAAC;oCAAI,WAAU;oCAAgB,OAAO;wCAAE,MAAM;oCAAW;;;;;;8CACzD,8OAAC;oCAAI,WAAU;oCAAgB,OAAO;wCAAE,MAAM;oCAAW;;;;;;8CACzD,8OAAC;oCAAI,WAAU;oCAAS,OAAO;wCAAE,MAAM;wCAAQ,SAAS;oCAAE;;;;;;;;;;;;sCAE5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAQ,OAAO;wCAAE,MAAM;oCAAK;8CACxC,EAAE,kBAAkB,EAAE;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;oCAAQ,OAAO;wCAAE,MAAM;oCAAW;8CAC9C,EAAE,gBAAgB,EAAE;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;oCAAQ,OAAO;wCAAE,MAAM;oCAAW;8CAC9C,EAAE,mBAAmB,EAAE;;;;;;8CAE1B,8OAAC;oCAAI,WAAU;oCAAQ,OAAO;wCAAE,MAAM;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD;uCAEe", "debugId": null}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/RecIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction RecIcon({ className }: { className: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"20\" height=\"20\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <g clipPath=\"url(#clip0_9893_7870)\">\n        <path\n          d=\"M16 1C7.72656 1 1 7.72656 1 16C1 24.2734 7.72656 31 16 31C24.2734 31 31 24.2734 31 16C31 7.72656 24.2734 1 16 1ZM16 30.0625C8.24805 30.0625 1.9375 23.752 1.9375 16C1.9375 8.24805 8.24805 1.9375 16 1.9375C23.752 1.9375 30.0625 8.24805 30.0625 16C30.0625 23.752 23.752 30.0625 16 30.0625ZM14.6992 13.5684V15.5312H18.0508C18.3086 15.5312 18.5195 15.7422 18.5195 16C18.5195 16.2578 18.3086 16.4688 18.0508 16.4688H14.6992V18.4316H18.0508C18.3086 18.4316 18.5195 18.6426 18.5195 18.9004C18.5195 19.1582 18.3086 19.3691 18.0508 19.3691H14.2246C13.9668 19.3691 13.7559 19.1582 13.7559 18.9004V13.0996C13.7559 12.8418 13.9668 12.6309 14.2246 12.6309H18.0508C18.3086 12.6309 18.5195 12.8418 18.5195 13.0996C18.5195 13.3574 18.3086 13.5684 18.0508 13.5684H14.6992ZM20.5586 14.7344V17.2598C20.5586 17.9043 21.0801 18.4258 21.7246 18.4258H22.2812C22.9141 18.4258 23.4355 17.9102 23.4473 17.2832C23.4531 17.0254 23.6641 16.8145 23.9277 16.8203C24.1855 16.8262 24.3906 17.0371 24.3906 17.3008C24.3672 18.4375 23.4238 19.3633 22.2871 19.3633H21.7305C20.5703 19.3633 19.627 18.4199 19.627 17.2598V14.7344C19.627 13.5742 20.5703 12.6309 21.7305 12.6309H22.2871C23.4238 12.6309 24.3672 13.5566 24.3906 14.6934C24.3965 14.9512 24.1914 15.168 23.9277 15.1738C23.6641 15.1797 23.4531 14.9746 23.4473 14.7109C23.4355 14.0781 22.9141 13.5684 22.2812 13.5684H21.7246C21.0801 13.5684 20.5586 14.0957 20.5586 14.7344ZM10.4336 12.6309H8.08984C7.83203 12.6309 7.62109 12.8418 7.62109 13.0996V16.4277V18.8945C7.62109 19.1523 7.83203 19.3633 8.08984 19.3633C8.34766 19.3633 8.55859 19.1523 8.55859 18.8945V16.8965H9.29688L11.0195 19.0938C11.1133 19.2109 11.248 19.2754 11.3887 19.2754C11.4883 19.2754 11.5938 19.2402 11.6758 19.1758C11.8809 19.0176 11.916 18.7188 11.7578 18.5195L10.4863 16.9023C11.6406 16.873 12.5664 15.9297 12.5664 14.7695C12.5723 13.5918 11.6113 12.6309 10.4336 12.6309ZM10.4336 15.9648H8.55859V13.5742H10.4336C11.0957 13.5742 11.6289 14.1074 11.6289 14.7695C11.6289 15.4258 11.0957 15.9648 10.4336 15.9648Z\"\n          strokeWidth=\"0.6\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9893_7870\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default RecIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,QAAQ,EAAE,SAAS,EAAyB;IACnD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBAAE,UAAS;0BACV,cAAA,8OAAC;oBACC,GAAE;oBACF,aAAY;;;;;;;;;;;0BAGhB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/LetterFoldIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction LetterFoldIcon({ className }: { className: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"50\" height=\"66\" viewBox=\"0 0 50 66\" fill=\"none\">\n      <path d=\"M0 0.580553L49.5778 0.224609V65.0572L0 0.580553Z\" fill=\"black\" fillOpacity=\"0.1\" />\n    </svg>\n  );\n}\n\nexport default LetterFoldIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,eAAe,EAAE,SAAS,EAAyB;IAC1D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBAC5G,cAAA,8OAAC;YAAK,GAAE;YAAmD,MAAK;YAAQ,aAAY;;;;;;;;;;;AAG1F;uCAEe", "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/conductInterview.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additional\": \"conductInterview-module-scss-module__yztraq__additional\",\n  \"completed\": \"conductInterview-module-scss-module__yztraq__completed\",\n  \"conduct_interview\": \"conductInterview-module-scss-module__yztraq__conduct_interview\",\n  \"conduct_interview_page\": \"conductInterview-module-scss-module__yztraq__conduct_interview_page\",\n  \"current\": \"conductInterview-module-scss-module__yztraq__current\",\n  \"question_info_box\": \"conductInterview-module-scss-module__yztraq__question_info_box\",\n  \"summary_card_height\": \"conductInterview-module-scss-module__yztraq__summary_card_height\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/interviewServices.ts"], "sourcesContent": ["import * as http from \"@/utils/http\";\nimport endpoint from \"@/constants/endpoint\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  IAddInterviewSkillQuestion,\n  IGetCandidateListResponse,\n  IGetInterviewersResponse,\n  IGetInterviews,\n  IGetInterviewSkillQuestions,\n  IGetInterviewSkillQuestionsResponse,\n  IGetInterviewsResponse,\n  IGetJobListResponse,\n  IInterviewStaticInformation,\n  IScheduleInterview,\n  IUpcomingOrPastInterview,\n  IUpdateInterviewAnswers,\n  IUpdateInterviewSkillQuestion,\n  IUpdateScheduleInterview,\n} from \"@/interfaces/interviewInterfaces\";\n\nexport const updateOrScheduleInterview = (data: IScheduleInterview | IUpdateScheduleInterview): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);\n};\n\nexport const getInterviews = (data: IGetInterviews): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\n  return http.get(endpoint.interview.GET_INTERVIEWS, data);\n};\n\nexport const getInterviewers = (searchString: string, jobId: string): Promise<ApiResponse<IGetInterviewersResponse[]>> => {\n  return http.get(endpoint.interview.GET_INTERVIEWERS, { searchString, jobId });\n};\n\nexport const upcomigOrPastInterview = (params: {\n  isPast: boolean;\n  limit?: number;\n  offset?: number;\n  searchStr?: string;\n}): Promise<ApiResponse<IUpcomingOrPastInterview[]>> => {\n  return http.get(endpoint.interview.GET_UPCOMING_OR_PAST_INTERVIEW, { ...params });\n};\n\nexport const getMyInterviews = (monthYear: string): Promise<ApiResponse<IGetInterviewsResponse[]>> => {\n  return http.get(endpoint.interview.GET_MY_INTERVIEWS, { monthYear });\n};\n\nexport const getInterviewSkillQuestions = (data: IGetInterviewSkillQuestions): Promise<ApiResponse<IGetInterviewSkillQuestionsResponse>> => {\n  return http.get(endpoint.interview.GET_INTERVIEW_SKILL_QUESTIONS, data);\n};\n\nexport const updateInterviewSkillQuestion = (data: IUpdateInterviewSkillQuestion): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);\n};\n\nexport const addInterviewSkillQuestion = (data: IAddInterviewSkillQuestion): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.ADD_INTERVIEW_SKILL_QUESTION, data);\n};\n\nexport const getJobList = (searchString: string): Promise<ApiResponse<IGetJobListResponse[]>> => {\n  return http.get(endpoint.interview.GET_JOB_LIST, { searchString });\n};\n\nexport const getCandidateList = (data: { searchString: string; jobId: string }): Promise<ApiResponse<IGetCandidateListResponse[]>> => {\n  return http.get(endpoint.interview.GET_CANDIDATE_LIST, data);\n};\n\nexport const updateInterviewAnswers = (data: IUpdateInterviewAnswers): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.UPDATE_INTERVIEW_ANSWERS, data);\n};\n\nexport const endInterview = (data: { interviewId: number; behaviouralNotes: string }): Promise<ApiResponse<null>> => {\n  return http.post(endpoint.interview.END_INTERVIEW, data);\n};\n\nexport const conductInterviewStaticInformation = (): Promise<ApiResponse<IInterviewStaticInformation>> => {\n  return http.get(endpoint.interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAmBO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,cAAc,EAAE;AACrD;AAEO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE;QAAE;QAAc;IAAM;AAC7E;AAEO,MAAM,yBAAyB,CAAC;IAMrC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,8BAA8B,EAAE;QAAE,GAAG,MAAM;IAAC;AACjF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE;QAAE;IAAU;AACpE;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,6BAA6B,EAAE;AACpE;AAEO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,+BAA+B,EAAE;AACvE;AAEO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE;AACpE;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,YAAY,EAAE;QAAE;IAAa;AAClE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE;AACzD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE;AAChE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,aAAa,EAAE;AACrD;AAEO,MAAM,oCAAoC;IAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,oCAAoC;AACzE", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/EndInterViewModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport { useTranslations } from \"next-intl\";\n\ninterface IProps {\n  onClickCancel: () => void;\n  onClickEndInterview: () => void;\n  disabled?: boolean;\n}\n\nconst EndInterViewModal: FC<IProps> = ({ onClickCancel, onClickEndInterview, disabled }) => {\n  const t = useTranslations();\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center\">\n            <h2>{t(\"end_interview\") + \"?\"}</h2>\n            <p className=\"textMd w100\">{t(\"end_interview_desc\")}</p>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body pt-0\">\n            <div className=\"action-btn\">\n              <Button className=\"primary-btn rounded-md w-100\" onClick={onClickEndInterview} disabled={disabled}>\n                {t(\"end_interview\")}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\nexport default EndInterViewModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,oBAAgC,CAAC,EAAE,aAAa,EAAE,mBAAmB,EAAE,QAAQ,EAAE;IACrF,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI,EAAE,mBAAmB;;;;;;0CAC1B,8OAAC;gCAAE,WAAU;0CAAe,EAAE;;;;;;0CAC9B,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;0CAC3C,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAA+B,SAAS;gCAAqB,UAAU;0CACtF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCACe", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/interviewValidations.ts"], "sourcesContent": ["import * as yup from \"yup\";\n\nexport const scheduleInterviewValidation = (translation: (key: string) => string) =>\n  yup.object({\n    eventTitle: yup.string().required(translation(\"title_req\")).min(5, translation(\"title_min_5_chars\")).max(100, translation(\"title_max_100_chars\")),\n    jobTitle: yup.number().required(translation(\"job_title_req\")),\n    interviewType: yup.string().required(translation(\"interview_type_req\")),\n    jobId: yup.string().required(translation(\"job_id_req\")),\n    date: yup\n      .string()\n      .required(translation(\"date_req\"))\n      .test(\"is-not-in-past\", translation(\"cannot_schedule_interview_past\"), function (dateStr) {\n        if (!dateStr) return true;\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const inputDate = new Date(dateStr);\n        inputDate.setHours(0, 0, 0, 0);\n        return inputDate >= today;\n      })\n      .test(\"not-more-than-month-in-advance\", translation(\"cannot_schedule_more_than_one_month_in_advance\"), function (dateStr) {\n        if (!dateStr) return true;\n        const oneMonthFromNow = new Date();\n        oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);\n        oneMonthFromNow.setHours(0, 0, 0, 0);\n        const inputDate = new Date(dateStr);\n        inputDate.setHours(0, 0, 0, 0);\n        return inputDate <= oneMonthFromNow;\n      }),\n\n    startTime: yup\n      .string()\n      .required(translation(\"start_time_req\"))\n      .matches(/^([01]\\d|2[0-3]):([0-5]\\d)$/, translation(\"invalid_time_format\")),\n    description: yup.string().nullable().optional().max(2000, translation(\"description_max_2000_chars\")),\n    endTime: yup\n      .string()\n      .required(translation(\"end_time_req\"))\n      .matches(/^([01]\\d|2[0-3]):([0-5]\\d)$/, translation(\"invalid_time_format\"))\n      .test(\"is-greater-than-start-time\", translation(\"end_time_must_be_after_start_time\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Compare times\n        return endTime > startTime;\n      })\n      .test(\"is-at-least-30-min\", translation(\"interview_must_be_at_least_10_min\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Parse hours and minutes\n        const [startHour, startMin] = startTime.split(\":\").map(Number);\n        const [endHour, endMin] = endTime.split(\":\").map(Number);\n\n        // Calculate total minutes\n        const startTotalMins = startHour * 60 + startMin;\n        const endTotalMins = endHour * 60 + endMin;\n        const diffMins = endTotalMins - startTotalMins;\n\n        // Ensure at least 30 minutes difference\n        return diffMins >= 30;\n      })\n      .test(\"max-duration\", translation(\"interview_must_not_exceed_2_hours\"), function (endTime) {\n        const { startTime } = this.parent;\n        if (!startTime || !endTime) return true;\n\n        // Parse hours and minutes\n        const [startHour, startMin] = startTime.split(\":\").map(Number);\n        const [endHour, endMin] = endTime.split(\":\").map(Number);\n\n        // Calculate total minutes\n        const startTotalMins = startHour * 60 + startMin;\n        const endTotalMins = endHour * 60 + endMin;\n        const diffMins = endTotalMins - startTotalMins;\n\n        // Ensure interview is no longer than 2 hours (120 minutes)\n        return diffMins <= 120;\n      }),\n    interviewer: yup.number().required(translation(\"interviewer_req\")),\n    candidate: yup\n      .number()\n      .test(\"job-title-required\", translation(\"please_select_job_title_first\"), function () {\n        const { jobTitle } = this.parent;\n        return !!jobTitle && jobTitle > 0;\n      })\n      .required(translation(\"candidate_req\")),\n  });\n\nexport const addUpdateQuestionValidation = (translation: (key: string) => string) =>\n  yup.object().shape({\n    question: yup.string().required(translation(\"question_req\")).max(500, translation(\"question_max_500_chars\")),\n  });\n\nexport const addAnswerValidation = (translation: (key: string) => string) =>\n  yup\n    .object()\n    .shape({\n      behavioralInfo: yup.string().optional().max(1500, translation(\"behavioral_info_max_1500_chars\")),\n    })\n    .test(\"dynamic-answer-validation\", \"\", function (values) {\n      console.log(\"values========\", values);\n      // Get all field names from the form values\n      const fieldNames = Object.keys(values || {});\n\n      // Create a new schema object to add dynamic validations\n      let dynamicSchema = yup.object().shape({});\n\n      // Add validation for each answer field\n      fieldNames.forEach((fieldName) => {\n        if (fieldName.startsWith(\"answer-\")) {\n          // Add validation for this specific answer field\n          dynamicSchema = dynamicSchema.shape({\n            [fieldName]: yup.string().optional().max(2000, translation(\"answer_max_2000_chars\")),\n          });\n        }\n      });\n\n      try {\n        dynamicSchema.validateSync(values, { abortEarly: false });\n        return true;\n      } catch (error) {\n        console.log(\"error\", error);\n        return false;\n      }\n    });\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,MAAM,8BAA8B,CAAC,cAC1C,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,EAAE;QACT,YAAY,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY,cAAc,GAAG,CAAC,GAAG,YAAY,sBAAsB,GAAG,CAAC,KAAK,YAAY;QAC1H,UAAU,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QAC5C,eAAe,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QACjD,OAAO,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QACzC,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,QAAQ,CAAC,YAAY,aACrB,IAAI,CAAC,kBAAkB,YAAY,mCAAmC,SAAU,OAAO;YACtF,IAAI,CAAC,SAAS,OAAO;YACrB,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YACxB,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,OAAO,aAAa;QACtB,GACC,IAAI,CAAC,kCAAkC,YAAY,mDAAmD,SAAU,OAAO;YACtH,IAAI,CAAC,SAAS,OAAO;YACrB,MAAM,kBAAkB,IAAI;YAC5B,gBAAgB,QAAQ,CAAC,gBAAgB,QAAQ,KAAK;YACtD,gBAAgB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAClC,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,OAAO,aAAa;QACtB;QAEF,WAAW,CAAA,GAAA,mIAAA,CAAA,SACF,AAAD,IACL,QAAQ,CAAC,YAAY,mBACrB,OAAO,CAAC,+BAA+B,YAAY;QACtD,aAAa,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;QACtE,SAAS,CAAA,GAAA,mIAAA,CAAA,SACA,AAAD,IACL,QAAQ,CAAC,YAAY,iBACrB,OAAO,CAAC,+BAA+B,YAAY,wBACnD,IAAI,CAAC,8BAA8B,YAAY,sCAAsC,SAAU,OAAO;YACrG,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,gBAAgB;YAChB,OAAO,UAAU;QACnB,GACC,IAAI,CAAC,sBAAsB,YAAY,sCAAsC,SAAU,OAAO;YAC7F,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,0BAA0B;YAC1B,MAAM,CAAC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YACvD,MAAM,CAAC,SAAS,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEjD,0BAA0B;YAC1B,MAAM,iBAAiB,YAAY,KAAK;YACxC,MAAM,eAAe,UAAU,KAAK;YACpC,MAAM,WAAW,eAAe;YAEhC,wCAAwC;YACxC,OAAO,YAAY;QACrB,GACC,IAAI,CAAC,gBAAgB,YAAY,sCAAsC,SAAU,OAAO;YACvF,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;YAEnC,0BAA0B;YAC1B,MAAM,CAAC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YACvD,MAAM,CAAC,SAAS,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEjD,0BAA0B;YAC1B,MAAM,iBAAiB,YAAY,KAAK;YACxC,MAAM,eAAe,UAAU,KAAK;YACpC,MAAM,WAAW,eAAe;YAEhC,2DAA2D;YAC3D,OAAO,YAAY;QACrB;QACF,aAAa,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY;QAC/C,WAAW,CAAA,GAAA,mIAAA,CAAA,SACF,AAAD,IACL,IAAI,CAAC,sBAAsB,YAAY,kCAAkC;YACxE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM;YAChC,OAAO,CAAC,CAAC,YAAY,WAAW;QAClC,GACC,QAAQ,CAAC,YAAY;IAC1B;AAEK,MAAM,8BAA8B,CAAC,cAC1C,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,UAAU,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,YAAY,iBAAiB,GAAG,CAAC,KAAK,YAAY;IACpF;AAEK,MAAM,sBAAsB,CAAC,cAClC,CAAA,GAAA,mIAAA,CAAA,SACS,AAAD,IACL,KAAK,CAAC;QACL,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;IAChE,GACC,IAAI,CAAC,6BAA6B,IAAI,SAAU,MAAM;QACrD,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,2CAA2C;QAC3C,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,CAAC;QAE1C,wDAAwD;QACxD,IAAI,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC,CAAC;QAExC,uCAAuC;QACvC,WAAW,OAAO,CAAC,CAAC;YAClB,IAAI,UAAU,UAAU,CAAC,YAAY;gBACnC,gDAAgD;gBAChD,gBAAgB,cAAc,KAAK,CAAC;oBAClC,CAAC,UAAU,EAAE,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,YAAY;gBAC7D;YACF;QACF;QAEA,IAAI;YACF,cAAc,YAAY,CAAC,QAAQ;gBAAE,YAAY;YAAM;YACvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;YACrB,OAAO;QACT;IACF", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/InterviewQuestion.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { use, useCallback, useEffect, useMemo, useRef, useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslations } from \"next-intl\";\nimport { useRouter } from \"next/navigation\";\nimport { io, Socket } from \"socket.io-client\";\n// import Link from \"next/link\";\nimport { useDispatch, useSelector } from \"react-redux\";\n\nimport Button from \"@/components/formElements/Button\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport PreviewResumeIcon from \"@/components/svgComponents/PreviewResumeIcon\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport Textarea from \"@/components/formElements/Textarea\";\nimport ArrowDownIcon from \"@/components/svgComponents/ArrowDownIcon\";\n// import RightGreenIcon from \"@/components/svgComponents/RightGreenIcon\";\n// import WrongRedIcon from \"@/components/svgComponents/WrongRedIcon\";\nimport ProgressTracker from \"./ProgressTracker\";\nimport RecIcon from \"@/components/svgComponents/RecIcon\";\nimport LetterFoldIcon from \"@/components/svgComponents/LetterFoldIcon\";\nimport style from \"../../../styles/conductInterview.module.scss\";\nimport { IGetInterviewSkillQuestionsResponse, IInterviewQuestionResponse, IInterviewQuestionFormValues } from \"@/interfaces/interviewInterfaces\";\nimport { QUESTION_TYPES } from \"@/constants/commonConstants\";\nimport { toastMessageError, toastMessageSuccess } from \"@/utils/helper\";\nimport { endInterview, updateInterviewAnswers } from \"@/services/interviewServices\";\nimport { clearInterview, updateQuestionAnswer } from \"@/redux/slices/interviewSlice\";\nimport EndInterViewModal from \"@/components/commonModals/EndInterViewModal\";\nimport { getSession } from \"next-auth/react\";\nimport { ISession } from \"@/interfaces/commonInterfaces\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { addAnswerValidation } from \"@/validations/interviewValidations\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport ROUTES from \"@/constants/routes\";\n\nconst InterviewQuestion = ({ params }: { params: Promise<{ interviewId: string; jobApplicationId: string; resumeLink: string }> }) => {\n  const searchParamsPromise = use(params);\n  const interviewId = +searchParamsPromise.interviewId;\n  const resumeLink = searchParamsPromise.resumeLink;\n\n  const interviewQuestionsData = useSelector((state: { interview: IGetInterviewSkillQuestionsResponse }) => state.interview);\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n\n  const router = useRouter();\n  const t = useTranslations();\n  const dispatch = useDispatch();\n  const socketRef = useRef<Socket | null>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const streamRef = useRef<MediaStream | null>(null);\n  const [isRecording, setIsRecording] = useState(false);\n\n  const {\n    control,\n    handleSubmit,\n    reset,\n    getValues,\n    watch,\n    formState: { errors },\n  } = useForm<IInterviewQuestionFormValues | any>({ mode: \"onChange\", resolver: yupResolver(addAnswerValidation(t)) });\n  const [openQuestions, setOpenQuestions] = useState<number[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [loader, setLoader] = useState(false);\n  const [showEndInterviewModal, setShowEndInterviewModal] = useState(false);\n\n  const [selectedTab, setSelectedTab] = useState<string>(QUESTION_TYPES.CAREER_BASED);\n  const [selectedSkill, setSelectedSkill] = useState(\"\");\n  const [skillMarked, setSkillMarked] = useState(0);\n\n  const [currentCultureSkillIndex, setCurrentCultureSkillIndex] = useState(0);\n  const [currentRoleSkillIndex, setCurrentRoleSkillIndex] = useState(0);\n\n  const roleSkills = useMemo(() => Object.keys(interviewQuestionsData.roleSpecificQuestions || {}), [interviewQuestionsData.roleSpecificQuestions]);\n\n  const cultureSkills = useMemo(\n    () => Object.keys(interviewQuestionsData.cultureSpecificQuestions || {}),\n    [interviewQuestionsData.cultureSpecificQuestions]\n  );\n\n  console.log(\"===========errors\", control._formState.errors);\n\n  console.log(\"====selectedSkill\", selectedSkill);\n\n  useEffect(() => {\n    prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);\n    setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);\n  }, []);\n\n  // Function to initialize socket with the token\n  const initializeSocket = (token: string) => {\n    if (socketRef.current) {\n      socketRef.current.disconnect(); // Disconnect the previous socket connection\n    }\n\n    // Initialize the socket with the new token\n    socketRef.current = io(\"http://localhost:3001\", {\n      path: \"/conduct-interview\",\n      reconnection: true,\n      reconnectionAttempts: 5,\n      reconnectionDelay: 1000,\n      reconnectionDelayMax: 5000,\n      extraHeaders: {\n        Authorization: `Bearer ${token}`,\n      },\n      query: {\n        interviewId: interviewId.toString(),\n      },\n    });\n\n    // Add socket event listeners\n    socketRef.current?.on(\"connect\", () => {\n      console.log(\"Connected to server\");\n    });\n\n    socketRef.current?.on(\"disconnect\", (reason, details) => {\n      console.log(\"Disconnected from server:\", reason, details);\n    });\n\n    socketRef.current?.on(\"connect_error\", (error) => {\n      console.log(\"Connect error:\", error);\n    });\n  };\n\n  const startSocketConnection = async () => {\n    const session = await getSession();\n    const parsedSession = { ...session } as ISession;\n    const token = parsedSession?.user?.data?.token;\n    console.log(\"token\", token);\n    if (token) {\n      initializeSocket(token);\n    }\n    return () => {\n      if (socketRef.current) {\n        socketRef.current.disconnect();\n      }\n    };\n  };\n\n  const handleOpen = (questionId: number) => {\n    setOpenQuestions((prev) => {\n      // If the question is already open, close it\n      if (prev.includes(questionId)) {\n        return prev.filter((id) => id !== questionId);\n      }\n      // Otherwise, add it to the open questions array\n      return [...prev, questionId];\n    });\n  };\n\n  async function openMicrophone(microphone: MediaRecorder, socket: Socket | null) {\n    console.log(\"microphone======\", microphone);\n    return new Promise<void>((resolve) => {\n      let buffer: Blob[] = [];\n      const bufferInterval = 1000;\n      microphone.onstart = () => {\n        resolve();\n      };\n\n      microphone.onstop = () => {\n        if (buffer.length > 0 && socket?.connected) {\n          const finalBlob = new Blob(buffer, { type: \"audio/webm\" });\n          socket.emit(\"message\", finalBlob);\n          buffer = [];\n        }\n      };\n\n      microphone.ondataavailable = (event: BlobEvent) => {\n        console.log(\"event======\", event);\n        if (event.data.size > 0) {\n          buffer.push(event.data);\n        }\n      };\n\n      const sendInterval = setInterval(() => {\n        if (buffer.length > 0 && socket?.connected) {\n          const audioBlob = new Blob(buffer, { type: \"audio/webm\" });\n          socket.emit(\"message\", audioBlob);\n          buffer = [];\n        }\n      }, bufferInterval);\n\n      microphone.start(500);\n\n      console.log(\"buffer======\", buffer);\n\n      const cleanup = () => {\n        clearInterval(sendInterval);\n        if (microphone.state !== \"inactive\") {\n          microphone.stop();\n        }\n      };\n      socket?.on(\"disconnect\", cleanup);\n      socket?.on(\"error\", cleanup);\n    });\n  }\n\n  const startTranscription = useCallback(async () => {\n    try {\n      setIsRecording(true);\n      await startSocketConnection();\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      console.log(\"stream======\", stream);\n      streamRef.current = stream;\n      const options = { mimeType: \"audio/webm\" };\n      const recorder = new MediaRecorder(stream, options);\n      console.log(\"recorder======\", recorder);\n      mediaRecorderRef.current = recorder;\n      await openMicrophone(recorder, socketRef.current);\n    } catch (error) {\n      console.error(\"Error starting transcription:\", error);\n    }\n  }, []);\n\n  const stopTranscription = useCallback(async () => {\n    setIsRecording(false);\n    if (mediaRecorderRef.current) {\n      if (mediaRecorderRef.current.state !== \"inactive\") {\n        mediaRecorderRef.current.stop();\n      }\n      mediaRecorderRef.current = null;\n    }\n    if (streamRef.current) {\n      streamRef.current.getTracks().forEach((track: MediaStreamTrack) => track.stop());\n      streamRef.current = null;\n    }\n\n    // so that previous audio is processed\n    setTimeout(() => {\n      if (socketRef.current) {\n        socketRef.current.close();\n        socketRef.current = null;\n      }\n    }, 5000);\n  }, []);\n\n  console.log(control._formValues, \"control._formValues===========\");\n\n  const handleSaveAndNext = async (data: IInterviewQuestionFormValues) => {\n    try {\n      console.log(\"inside handleSaveAndNext\");\n      if (skillMarked === 0) {\n        toastMessageError(t(\"please_mark_stratum_score\"));\n        return;\n      }\n      setLoading(true);\n      const { behavioralInfo, ...rest } = data;\n\n      console.log(\"==========behavioralInfo\", behavioralInfo);\n\n      // Transform the data into the required format\n      const answers = Object.entries(rest).map(([key, value]) => {\n        // Extract the ID from the key (e.g., \"answer--->>148\" -> 148)\n        const questionId = parseInt(key.replace(\"answer-\", \"\"));\n\n        return {\n          questionId,\n          answer: value,\n        };\n      });\n\n      let skillId;\n      let jobSkillId;\n\n      if (selectedTab === QUESTION_TYPES.ROLE_SPECIFIC) {\n        skillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].skillId;\n        jobSkillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].jobSkillId;\n      } else if (selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC) {\n        skillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].skillId;\n        jobSkillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].jobSkillId;\n      }\n\n      const payload = {\n        interviewId: interviewId,\n        skillId,\n        jobSkillId,\n        skillMarked: skillMarked,\n        skillType: selectedTab,\n        answers,\n      };\n\n      console.log(\"==========payload\", payload);\n\n      // After saving, handle navigation based on current section and skill\n      const response = await updateInterviewAnswers(payload);\n\n      const interviewerName = `${authData?.first_name.charAt(0).toUpperCase()}${authData?.last_name.charAt(0).toUpperCase()}`;\n      console.log(\"interviewerName\", interviewerName);\n\n      console.log(\"api response====\", response);\n\n      if (response?.data?.success) {\n        console.log(\"interviewerName\", interviewerName);\n        switch (selectedTab) {\n          case QUESTION_TYPES.CAREER_BASED:\n            dispatch(\n              updateQuestionAnswer({\n                questionType: selectedTab,\n                questionAnswers: answers,\n                stratumScore: skillMarked,\n              })\n            );\n            break;\n          case QUESTION_TYPES.ROLE_SPECIFIC:\n            dispatch(\n              updateQuestionAnswer({\n                questionType: selectedTab,\n                category: selectedSkill,\n                questionAnswers: answers,\n                stratumScore: skillMarked,\n                interviewerName,\n              })\n            );\n            break;\n          case QUESTION_TYPES.CULTURE_SPECIFIC:\n            dispatch(\n              updateQuestionAnswer({\n                questionType: selectedTab,\n                category: selectedSkill,\n                questionAnswers: answers,\n                stratumScore: skillMarked,\n                interviewerName,\n              })\n            );\n            break;\n        }\n      }\n\n      setTimeout(() => {\n        handleNextSkillInterview();\n      }, 100);\n    } catch {\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleEndInterview = async () => {\n    try {\n      setLoader(true);\n      const response = await endInterview({ interviewId, behaviouralNotes: getValues(\"behavioralInfo\") ?? \"\" });\n\n      if (response?.data?.success) {\n        toastMessageSuccess(t(response?.data?.message));\n        dispatch(clearInterview());\n        router.push(ROUTES.DASHBOARD);\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch {\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setLoader(false);\n    }\n  };\n\n  const handlePreviousSkillInterview = () => {\n    setOpenQuestions([]);\n    const behavioralInfo = getValues(\"behavioralInfo\");\n    reset({\n      behavioralInfo: behavioralInfo,\n    });\n\n    switch (selectedTab) {\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        // Check if we're on the first role skill\n        if (currentRoleSkillIndex === 0) {\n          // Move from Role-specific back to Career-based\n          setSelectedTab(QUESTION_TYPES.CAREER_BASED);\n          prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);\n          setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);\n        } else {\n          // Move to previous role skill\n          const prevIndex = currentRoleSkillIndex - 1;\n          setCurrentRoleSkillIndex(prevIndex);\n          setSelectedSkill(roleSkills[prevIndex]);\n          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].questions || []);\n          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].score);\n        }\n        break;\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        // Check if we're on the first culture skill\n        if (currentCultureSkillIndex === 0) {\n          // Move from Culture-specific to Role-specific\n          setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);\n          if (roleSkills.length > 0) {\n            setSelectedSkill(roleSkills[roleSkills.length - 1]);\n            setCurrentRoleSkillIndex(roleSkills.length - 1);\n            prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].questions || []);\n            setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].score);\n          }\n        } else {\n          // Move to previous culture skill\n          const prevIndex = currentCultureSkillIndex - 1;\n          setCurrentCultureSkillIndex(prevIndex);\n          setSelectedSkill(cultureSkills[prevIndex]);\n          prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].questions || []);\n          setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].score);\n        }\n        break;\n      case QUESTION_TYPES.CAREER_BASED:\n        break;\n    }\n  };\n\n  const handleNextSkillInterview = () => {\n    setOpenQuestions([]);\n    const behavioralInfo = getValues(\"behavioralInfo\");\n    reset({\n      behavioralInfo: behavioralInfo,\n    });\n\n    switch (selectedTab) {\n      case QUESTION_TYPES.CAREER_BASED:\n        // Move from Career-based to Role-specific\n        setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);\n        if (roleSkills.length > 0) {\n          setSelectedSkill(roleSkills[0]);\n          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].questions || []);\n          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].score);\n        }\n        break;\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        // Check if there are more role skills to navigate to\n        if (currentRoleSkillIndex < roleSkills.length - 1) {\n          // Move to next role skill\n          const nextIndex = currentRoleSkillIndex + 1;\n          setCurrentRoleSkillIndex(nextIndex);\n          setSelectedSkill(roleSkills[nextIndex]);\n          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].questions || []);\n          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].score);\n        } else {\n          // Move from Role-specific to Culture-specific\n          setSelectedTab(QUESTION_TYPES.CULTURE_SPECIFIC);\n          setCurrentRoleSkillIndex(0);\n          if (cultureSkills.length > 0) {\n            setSelectedSkill(cultureSkills[0]);\n            setCurrentCultureSkillIndex(0);\n            prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].questions || []);\n            setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].score);\n          }\n        }\n        break;\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        // Check if there are more culture skills to navigate to\n        if (currentCultureSkillIndex < cultureSkills.length - 1) {\n          // Move to next culture skill\n          const nextIndex = currentCultureSkillIndex + 1;\n          setCurrentCultureSkillIndex(nextIndex);\n          setSelectedSkill(cultureSkills[nextIndex]);\n          prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].questions || []);\n          setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].score);\n        }\n        break;\n    }\n  };\n\n  const isLastSkillInSection = (): boolean => {\n    switch (selectedTab) {\n      case QUESTION_TYPES.CAREER_BASED:\n        return true; // Career-based doesn't have sub-skills\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        return currentCultureSkillIndex >= cultureSkills.length - 1;\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        return currentRoleSkillIndex >= roleSkills.length - 1;\n      default:\n        return false;\n    }\n  };\n\n  const isLastSkillOverall = (): boolean => {\n    return selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC && isLastSkillInSection();\n  };\n\n  const isSkillCompleted = (skillType: string, skillName: string): boolean => {\n    switch (skillType) {\n      case QUESTION_TYPES.CAREER_BASED:\n        return (\n          interviewQuestionsData.careerBasedQuestions?.questions?.some((question) => question.answer !== undefined && question.answer !== \"\") ||\n          !!interviewQuestionsData.careerBasedQuestions.score\n        );\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        return (\n          interviewQuestionsData.roleSpecificQuestions?.[skillName]?.questions?.some(\n            (question) => question.answer !== undefined && question.answer !== \"\"\n          ) || !!interviewQuestionsData.roleSpecificQuestions[skillName].score\n        );\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        return (\n          interviewQuestionsData.cultureSpecificQuestions?.[skillName]?.questions?.some(\n            (question) => question.answer !== undefined && question.answer !== \"\"\n          ) || !!interviewQuestionsData.cultureSpecificQuestions[skillName].score\n        );\n      default:\n        return false;\n    }\n  };\n\n  const prefillFormData = (questions: IInterviewQuestionResponse[]) => {\n    if (!questions || questions.length === 0) {\n      return;\n    }\n\n    // For each question, prefill the form if we have data\n    questions.forEach((question) => {\n      if (question.answer) {\n        // Set the form value for this question\n        const fieldName = `answer-${question.id}`;\n        // Use reset to set the value while preserving other form values\n        reset({\n          ...control._formValues,\n          [fieldName]: question.answer,\n        });\n\n        // Open the question card\n        if (!openQuestions.includes(question.id)) {\n          setOpenQuestions((prev) => [...prev, question.id]);\n        }\n      }\n    });\n  };\n\n  const handleSkillSelection = (skill: string) => {\n    setSelectedSkill(skill);\n\n    // update additionalInfo field\n    reset({\n      ...control._formValues,\n      behavioralInfo: getValues(\"behavioralInfo\"),\n    });\n\n    // Update the corresponding index tracker based on the selected tab\n    switch (selectedTab) {\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        const roleIndex = roleSkills.findIndex((s) => s === skill);\n        if (roleIndex !== -1) {\n          setCurrentRoleSkillIndex(roleIndex);\n        }\n        // Prefill form data for role-specific skills\n        prefillFormData(interviewQuestionsData.roleSpecificQuestions[skill].questions || []);\n        setSkillMarked(interviewQuestionsData.roleSpecificQuestions[skill].score);\n        break;\n\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        const cultureIndex = cultureSkills.findIndex((s) => s === skill);\n        if (cultureIndex !== -1) {\n          setCurrentCultureSkillIndex(cultureIndex);\n        }\n        // Prefill form data for culture-specific skills\n        prefillFormData(interviewQuestionsData.cultureSpecificQuestions[skill].questions || []);\n        setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[skill].score);\n        break;\n\n      case QUESTION_TYPES.CAREER_BASED:\n        // Prefill form data for career-based skills\n        prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);\n        setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);\n        break;\n\n      default:\n        break;\n    }\n  };\n\n  const renderQuestion = (question: IInterviewQuestionResponse, index: number) => (\n    <div key={question.id} className=\"interview-question-card \">\n      <div onClick={() => handleOpen(question.id)}>\n        <p className=\"tittle\">\n          {t(\"question\")} {index < 9 ? `0${index + 1}` : index + 1} <ArrowDownIcon className={openQuestions.includes(question.id) ? \"rotate\" : \"\"} />\n        </p>\n        <h5>{question.question}</h5>\n      </div>\n      {openQuestions.includes(question.id) ? (\n        <div className=\"question-body \">\n          <InputWrapper>\n            <InputWrapper.Label htmlFor={`answer-${question.id}`} required>\n              {t(\"your_notes\")}\n            </InputWrapper.Label>\n            <Textarea rows={3} name={`answer-${question.id}`} control={control} placeholder={t(\"additional_info_desc\")} className=\"form-control\" />\n            {watch(`answer-${question.id}`)?.length > 2000 ? <InputWrapper.Error message={t(\"answer_max_2000_chars\")} /> : null}\n          </InputWrapper>\n          {/* <div className=\"follow-up-container\">\n                <p className=\"follow-up-text\">\n                  <span>Follow Up:</span> What tools or systems did you use to stay organized?{\" \"}\n                </p>\n                <div className=\"follow-up-btn\">\n                  <Button className=\"clear-btn p-0 m-0\">\n                    <RightGreenIcon />\n                  </Button>\n                  <Button className=\"clear-btn p-0 m-0\">\n                    <WrongRedIcon />\n                  </Button>\n                </div>\n            </div> \n          */}\n        </div>\n      ) : null}\n    </div>\n  );\n\n  const renderAllQuestions = (type: string) => {\n    switch (type) {\n      case QUESTION_TYPES.CAREER_BASED:\n        return interviewQuestionsData.careerBasedQuestions?.questions?.map((question, index) => renderQuestion(question, index)) || [];\n      case QUESTION_TYPES.ROLE_SPECIFIC:\n        return (\n          interviewQuestionsData.roleSpecificQuestions?.[selectedSkill]?.questions?.map((question, index) => renderQuestion(question, index)) || []\n        );\n      case QUESTION_TYPES.CULTURE_SPECIFIC:\n        return (\n          interviewQuestionsData.cultureSpecificQuestions?.[selectedSkill]?.questions?.map((question, index) => renderQuestion(question, index)) || []\n        );\n      default:\n        return [];\n    }\n  };\n\n  const getSkills = (type: string) => (type === QUESTION_TYPES.CULTURE_SPECIFIC ? cultureSkills : roleSkills);\n\n  return (\n    <div className={style.conduct_interview_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <Button\n            className=\"danger-outline-btn rounded-md py-3 px-4 mb-5\"\n            onClick={() => {\n              if (isRecording) {\n                stopTranscription();\n              } else {\n                startTranscription();\n              }\n            }}\n          >\n            <RecIcon className=\"me-3\" />\n            {isRecording ? t(\"stop_recording\") : t(\"record_interview\")}\n          </Button>\n\n          <ProgressTracker isRecording={isRecording} />\n\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                <BackArrowIcon onClick={() => {\n                  if (selectedTab !== QUESTION_TYPES.CAREER_BASED) {\n                    handlePreviousSkillInterview()\n                  }\n                }} />\n                {selectedTab === QUESTION_TYPES.CAREER_BASED ? (\n                  <>\n                    {t(\"career_based_skills_and_general_interview\")} <span>{t(\"questions\")}</span>\n                  </>\n                ) : (\n                  <>\n                    {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC ? t(\"role_specific_interview\") : t(\"culture_specific_interview\")}{\" \"}\n                    <span>{t(\"questions\")}</span>\n                  </>\n                )}\n              </h2>\n              <Button className=\"clear-btn text-btn primary p-0 m-0\" onClick={() => window.open(resumeLink, \"_blank\")}>\n                <PreviewResumeIcon className=\"me-2\" />\n                {t(\"preview_candidate_resume\")}\n              </Button>\n            </div>\n          </div>\n        </div>\n        <form onSubmit={handleSubmit((data) => handleSaveAndNext(data as IInterviewQuestionFormValues))}>\n          <div className=\"inner-section\">\n            {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC || selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC ? (\n              <>\n                <div className={style.question_info_box}>\n                  <ul>\n                    <li>\n                      <span className={style.current} />\n                      {t(\"current\")}\n                    </li>\n                    <li>\n                      <span className={style.completed} />\n                      {t(\"completed\")}\n                    </li>\n                    {/* <li>\n                      <span className={style.additional} />\n                      {t(\"additional\")}\n                    </li> */}\n                  </ul>\n                </div>\n                <ul className=\"interview-topic-list\" aria-label=\"Job roles list\">\n                  {getSkills(selectedTab)?.map((skill, index) => (\n                    <li\n                      key={skill}\n                      className={`topic-item ${skill === selectedSkill ? \"current\" : isSkillCompleted(selectedTab, skill) ? \"completed\" : \"\"}`}\n                      tabIndex={index}\n                      onClick={() => handleSkillSelection(skill)}\n                    >\n                      {skill}\n                      {isSkillCompleted(selectedTab, skill) ? (\n                        <span className=\"interviewer-name\">\n                          {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC\n                            ? interviewQuestionsData.roleSpecificQuestions[skill]?.interviewerName\n                            : interviewQuestionsData.cultureSpecificQuestions[skill]?.interviewerName}\n                        </span>\n                      ) : null}\n                    </li>\n                  ))}\n                </ul>\n              </>\n            ) : null}\n            <div className=\"row\">\n              <div className=\"col-md-8\">{renderAllQuestions(selectedTab)}</div>\n              <div className=\"col-md-4\">\n                <div className=\"behavioral-letter-card\">\n                  <h5>\n                    {t(\"behavioral\")} <span>{t(\"performance\")}</span>\n                  </h5>\n                  <InputWrapper>\n                    <InputWrapper.Label htmlFor=\"behavioralInfo\">{t(\"describe_candidate_behaviours\")}</InputWrapper.Label>\n                    <Textarea\n                      rows={5}\n                      name=\"behavioralInfo\"\n                      control={control}\n                      placeholder={t(\"describe_candidate_behaviours\")}\n                      className=\"form-control\"\n                    />\n                    <InputWrapper.Error message={errors.behavioralInfo?.message as string} />\n                  </InputWrapper>\n                  <LetterFoldIcon className=\"fold-svg\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"section-heading\">\n              <h2>\n                {t(\"score\")}{\" \"}\n                <span className=\"primary\">\n                  {selectedTab === QUESTION_TYPES.CAREER_BASED ? t(\"career\") : selectedSkill} {t(\"stratum\")}\n                </span>\n              </h2>\n              <p>\n                {t(\"score_the_candidate_for\")} {selectedTab === QUESTION_TYPES.CAREER_BASED ? t(\"career_based\") : selectedSkill.toLowerCase()}{\" \"}\n                {t(\"stratum\")} {t(\"score_range\")}\n              </p>\n            </div>\n            <ul className=\"number-task\">\n              {Array.from({ length: 10 }).map((_, index) => (\n                <li\n                  className={index > 8 ? (index + 1 === skillMarked ? \"extreme active\" : \"extreme\") : index + 1 === skillMarked ? \"active\" : \"\"}\n                  key={index}\n                  onClick={() => {\n                    setSkillMarked(index + 1 === skillMarked ? 0 : index + 1);\n                  }}\n                >\n                  {index > 8 ? t(\"extreme\") : index + 1}\n                </li>\n              ))}\n            </ul>\n            <div className=\"interview-question-card\">\n              <h5>{t(\"candidate_not_aware\")}</h5>\n            </div>\n          </div>\n          <div className=\"button-align justify-content-between pt-4 pb-5\">\n            <div className=\"button-align \">\n              {!isLastSkillOverall() ? (\n                <Button className=\"primary-btn rounded-md\" type=\"button\" disabled={loading || loader} onClick={handleNextSkillInterview}>\n                  {t(\"next_skill_interview\")}\n                </Button>\n              ) : null}\n              <Button className=\"dark-outline-btn rounded-md\" type=\"submit\" disabled={loading || loader} loading={loading}>\n                {t(\"save_next\")}\n              </Button>\n            </div>\n            <Button\n              className=\"danger-btn rounded-md\"\n              type=\"button\"\n              onClick={() => setShowEndInterviewModal(true)}\n              loading={loader}\n              disabled={loading || loader}\n            >\n              {t(\"end_interview\")}\n            </Button>\n          </div>\n        </form>\n      </div>\n      {showEndInterviewModal ? (\n        <EndInterViewModal\n          onClickCancel={() => setShowEndInterviewModal(false)}\n          onClickEndInterview={handleEndInterview}\n          disabled={loading || loader}\n        />\n      ) : null}\n    </div>\n  );\n};\n\nexport default InterviewQuestion;\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AAAA;AACA,gCAAgC;AAChC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E;AAC1E,sEAAsE;AACtE;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EAAE,MAAM,EAA8F;IAC/H,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IAChC,MAAM,cAAc,CAAC,oBAAoB,WAAW;IACpD,MAAM,aAAa,oBAAoB,UAAU;IAEjD,MAAM,yBAAyB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA8D,MAAM,SAAS;IACzH,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAEhF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,KAAK,EACL,SAAS,EACT,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsC;QAAE,MAAM;QAAY,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE;IAAI;IAClH,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,mIAAA,CAAA,iBAAc,CAAC,YAAY;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,OAAO,IAAI,CAAC,uBAAuB,qBAAqB,IAAI,CAAC,IAAI;QAAC,uBAAuB,qBAAqB;KAAC;IAEhJ,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC1B,IAAM,OAAO,IAAI,CAAC,uBAAuB,wBAAwB,IAAI,CAAC,IACtE;QAAC,uBAAuB,wBAAwB;KAAC;IAGnD,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,UAAU,CAAC,MAAM;IAE1D,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,uBAAuB,oBAAoB,CAAC,SAAS,IAAI,EAAE;QAC3E,eAAe,uBAAuB,oBAAoB,CAAC,KAAK;IAClE,GAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,mBAAmB,CAAC;QACxB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,UAAU,IAAI,4CAA4C;QAC9E;QAEA,2CAA2C;QAC3C,UAAU,OAAO,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;YAC9C,MAAM;YACN,cAAc;YACd,sBAAsB;YACtB,mBAAmB;YACnB,sBAAsB;YACtB,cAAc;gBACZ,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;YACA,OAAO;gBACL,aAAa,YAAY,QAAQ;YACnC;QACF;QAEA,6BAA6B;QAC7B,UAAU,OAAO,EAAE,GAAG,WAAW;YAC/B,QAAQ,GAAG,CAAC;QACd;QAEA,UAAU,OAAO,EAAE,GAAG,cAAc,CAAC,QAAQ;YAC3C,QAAQ,GAAG,CAAC,6BAA6B,QAAQ;QACnD;QAEA,UAAU,OAAO,EAAE,GAAG,iBAAiB,CAAC;YACtC,QAAQ,GAAG,CAAC,kBAAkB;QAChC;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,UAAU,MAAM,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;QAC/B,MAAM,gBAAgB;YAAE,GAAG,OAAO;QAAC;QACnC,MAAM,QAAQ,eAAe,MAAM,MAAM;QACzC,QAAQ,GAAG,CAAC,SAAS;QACrB,IAAI,OAAO;YACT,iBAAiB;QACnB;QACA,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,UAAU;YAC9B;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAC;YAChB,4CAA4C;YAC5C,IAAI,KAAK,QAAQ,CAAC,aAAa;gBAC7B,OAAO,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO;YACpC;YACA,gDAAgD;YAChD,OAAO;mBAAI;gBAAM;aAAW;QAC9B;IACF;IAEA,eAAe,eAAe,UAAyB,EAAE,MAAqB;QAC5E,QAAQ,GAAG,CAAC,oBAAoB;QAChC,OAAO,IAAI,QAAc,CAAC;YACxB,IAAI,SAAiB,EAAE;YACvB,MAAM,iBAAiB;YACvB,WAAW,OAAO,GAAG;gBACnB;YACF;YAEA,WAAW,MAAM,GAAG;gBAClB,IAAI,OAAO,MAAM,GAAG,KAAK,QAAQ,WAAW;oBAC1C,MAAM,YAAY,IAAI,KAAK,QAAQ;wBAAE,MAAM;oBAAa;oBACxD,OAAO,IAAI,CAAC,WAAW;oBACvB,SAAS,EAAE;gBACb;YACF;YAEA,WAAW,eAAe,GAAG,CAAC;gBAC5B,QAAQ,GAAG,CAAC,eAAe;gBAC3B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,OAAO,IAAI,CAAC,MAAM,IAAI;gBACxB;YACF;YAEA,MAAM,eAAe,YAAY;gBAC/B,IAAI,OAAO,MAAM,GAAG,KAAK,QAAQ,WAAW;oBAC1C,MAAM,YAAY,IAAI,KAAK,QAAQ;wBAAE,MAAM;oBAAa;oBACxD,OAAO,IAAI,CAAC,WAAW;oBACvB,SAAS,EAAE;gBACb;YACF,GAAG;YAEH,WAAW,KAAK,CAAC;YAEjB,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,MAAM,UAAU;gBACd,cAAc;gBACd,IAAI,WAAW,KAAK,KAAK,YAAY;oBACnC,WAAW,IAAI;gBACjB;YACF;YACA,QAAQ,GAAG,cAAc;YACzB,QAAQ,GAAG,SAAS;QACtB;IACF;IAEA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,eAAe;YACf,MAAM;YACN,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,UAAU,OAAO,GAAG;YACpB,MAAM,UAAU;gBAAE,UAAU;YAAa;YACzC,MAAM,WAAW,IAAI,cAAc,QAAQ;YAC3C,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,iBAAiB,OAAO,GAAG;YAC3B,MAAM,eAAe,UAAU,UAAU,OAAO;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,eAAe;QACf,IAAI,iBAAiB,OAAO,EAAE;YAC5B,IAAI,iBAAiB,OAAO,CAAC,KAAK,KAAK,YAAY;gBACjD,iBAAiB,OAAO,CAAC,IAAI;YAC/B;YACA,iBAAiB,OAAO,GAAG;QAC7B;QACA,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,QAA4B,MAAM,IAAI;YAC7E,UAAU,OAAO,GAAG;QACtB;QAEA,sCAAsC;QACtC,WAAW;YACT,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,KAAK;gBACvB,UAAU,OAAO,GAAG;YACtB;QACF,GAAG;IACL,GAAG,EAAE;IAEL,QAAQ,GAAG,CAAC,QAAQ,WAAW,EAAE;IAEjC,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,IAAI,gBAAgB,GAAG;gBACrB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACpB;YACF;YACA,WAAW;YACX,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,GAAG;YAEpC,QAAQ,GAAG,CAAC,4BAA4B;YAExC,8CAA8C;YAC9C,MAAM,UAAU,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gBACpD,8DAA8D;gBAC9D,MAAM,aAAa,SAAS,IAAI,OAAO,CAAC,WAAW;gBAEnD,OAAO;oBACL;oBACA,QAAQ;gBACV;YACF;YAEA,IAAI;YACJ,IAAI;YAEJ,IAAI,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,aAAa,EAAE;gBAChD,UAAU,uBAAuB,qBAAqB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO;gBAC1F,aAAa,uBAAuB,qBAAqB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU;YAClG,OAAO,IAAI,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,gBAAgB,EAAE;gBAC1D,UAAU,uBAAuB,wBAAwB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO;gBAC7F,aAAa,uBAAuB,wBAAwB,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU;YACrG;YAEA,MAAM,UAAU;gBACd,aAAa;gBACb;gBACA;gBACA,aAAa;gBACb,WAAW;gBACX;YACF;YAEA,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,qEAAqE;YACrE,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;YAE9C,MAAM,kBAAkB,GAAG,UAAU,WAAW,OAAO,GAAG,gBAAgB,UAAU,UAAU,OAAO,GAAG,eAAe;YACvH,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,IAAI,UAAU,MAAM,SAAS;gBAC3B,QAAQ,GAAG,CAAC,mBAAmB;gBAC/B,OAAQ;oBACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;wBAC9B,SACE,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;4BACnB,cAAc;4BACd,iBAAiB;4BACjB,cAAc;wBAChB;wBAEF;oBACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;wBAC/B,SACE,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;4BACnB,cAAc;4BACd,UAAU;4BACV,iBAAiB;4BACjB,cAAc;4BACd;wBACF;wBAEF;oBACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;wBAClC,SACE,CAAA,GAAA,wIAAA,CAAA,uBAAoB,AAAD,EAAE;4BACnB,cAAc;4BACd,UAAU;4BACV,iBAAiB;4BACjB,cAAc;4BACd;wBACF;wBAEF;gBACJ;YACF;YAEA,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,UAAU;YACV,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;gBAAE;gBAAa,kBAAkB,UAAU,qBAAqB;YAAG;YAEvG,IAAI,UAAU,MAAM,SAAS;gBAC3B,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,UAAU,MAAM;gBACtC,SAAS,CAAA,GAAA,wIAAA,CAAA,iBAAc,AAAD;gBACtB,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;YAC9B,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,+BAA+B;QACnC,iBAAiB,EAAE;QACnB,MAAM,iBAAiB,UAAU;QACjC,MAAM;YACJ,gBAAgB;QAClB;QAEA,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,yCAAyC;gBACzC,IAAI,0BAA0B,GAAG;oBAC/B,+CAA+C;oBAC/C,eAAe,mIAAA,CAAA,iBAAc,CAAC,YAAY;oBAC1C,gBAAgB,uBAAuB,oBAAoB,CAAC,SAAS,IAAI,EAAE;oBAC3E,eAAe,uBAAuB,oBAAoB,CAAC,KAAK;gBAClE,OAAO;oBACL,8BAA8B;oBAC9B,MAAM,YAAY,wBAAwB;oBAC1C,yBAAyB;oBACzB,iBAAiB,UAAU,CAAC,UAAU;oBACtC,gBAAgB,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE;oBACnG,eAAe,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,KAAK;gBAC1F;gBACA;YACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,4CAA4C;gBAC5C,IAAI,6BAA6B,GAAG;oBAClC,8CAA8C;oBAC9C,eAAe,mIAAA,CAAA,iBAAc,CAAC,aAAa;oBAC3C,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,iBAAiB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;wBAClD,yBAAyB,WAAW,MAAM,GAAG;wBAC7C,gBAAgB,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,CAAC,SAAS,IAAI,EAAE;wBAC/G,eAAe,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK;oBACtG;gBACF,OAAO;oBACL,iCAAiC;oBACjC,MAAM,YAAY,2BAA2B;oBAC7C,4BAA4B;oBAC5B,iBAAiB,aAAa,CAAC,UAAU;oBACzC,gBAAgB,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE;oBACzG,eAAe,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK;gBAChG;gBACA;YACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B;QACJ;IACF;IAEA,MAAM,2BAA2B;QAC/B,iBAAiB,EAAE;QACnB,MAAM,iBAAiB,UAAU;QACjC,MAAM;YACJ,gBAAgB;QAClB;QAEA,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B,0CAA0C;gBAC1C,eAAe,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC3C,IAAI,WAAW,MAAM,GAAG,GAAG;oBACzB,iBAAiB,UAAU,CAAC,EAAE;oBAC9B,gBAAgB,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,SAAS,IAAI,EAAE;oBAC3F,eAAe,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,KAAK;gBAClF;gBACA;YACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,qDAAqD;gBACrD,IAAI,wBAAwB,WAAW,MAAM,GAAG,GAAG;oBACjD,0BAA0B;oBAC1B,MAAM,YAAY,wBAAwB;oBAC1C,yBAAyB;oBACzB,iBAAiB,UAAU,CAAC,UAAU;oBACtC,gBAAgB,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE;oBACnG,eAAe,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,KAAK;gBAC1F,OAAO;oBACL,8CAA8C;oBAC9C,eAAe,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;oBAC9C,yBAAyB;oBACzB,IAAI,cAAc,MAAM,GAAG,GAAG;wBAC5B,iBAAiB,aAAa,CAAC,EAAE;wBACjC,4BAA4B;wBAC5B,gBAAgB,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,SAAS,IAAI,EAAE;wBACjG,eAAe,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,KAAK;oBACxF;gBACF;gBACA;YACF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,wDAAwD;gBACxD,IAAI,2BAA2B,cAAc,MAAM,GAAG,GAAG;oBACvD,6BAA6B;oBAC7B,MAAM,YAAY,2BAA2B;oBAC7C,4BAA4B;oBAC5B,iBAAiB,aAAa,CAAC,UAAU;oBACzC,gBAAgB,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE;oBACzG,eAAe,uBAAuB,wBAAwB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,KAAK;gBAChG;gBACA;QACJ;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B,OAAO,MAAM,uCAAuC;YACtD,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,OAAO,4BAA4B,cAAc,MAAM,GAAG;YAC5D,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,OAAO,yBAAyB,WAAW,MAAM,GAAG;YACtD;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAO,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,gBAAgB,IAAI;IAC5D;IAEA,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B,OACE,uBAAuB,oBAAoB,EAAE,WAAW,KAAK,CAAC,WAAa,SAAS,MAAM,KAAK,aAAa,SAAS,MAAM,KAAK,OAChI,CAAC,CAAC,uBAAuB,oBAAoB,CAAC,KAAK;YAEvD,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,OACE,uBAAuB,qBAAqB,EAAE,CAAC,UAAU,EAAE,WAAW,KACpE,CAAC,WAAa,SAAS,MAAM,KAAK,aAAa,SAAS,MAAM,KAAK,OAChE,CAAC,CAAC,uBAAuB,qBAAqB,CAAC,UAAU,CAAC,KAAK;YAExE,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,OACE,uBAAuB,wBAAwB,EAAE,CAAC,UAAU,EAAE,WAAW,KACvE,CAAC,WAAa,SAAS,MAAM,KAAK,aAAa,SAAS,MAAM,KAAK,OAChE,CAAC,CAAC,uBAAuB,wBAAwB,CAAC,UAAU,CAAC,KAAK;YAE3E;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;YACxC;QACF;QAEA,sDAAsD;QACtD,UAAU,OAAO,CAAC,CAAC;YACjB,IAAI,SAAS,MAAM,EAAE;gBACnB,uCAAuC;gBACvC,MAAM,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACzC,gEAAgE;gBAChE,MAAM;oBACJ,GAAG,QAAQ,WAAW;oBACtB,CAAC,UAAU,EAAE,SAAS,MAAM;gBAC9B;gBAEA,yBAAyB;gBACzB,IAAI,CAAC,cAAc,QAAQ,CAAC,SAAS,EAAE,GAAG;oBACxC,iBAAiB,CAAC,OAAS;+BAAI;4BAAM,SAAS,EAAE;yBAAC;gBACnD;YACF;QACF;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;QAEjB,8BAA8B;QAC9B,MAAM;YACJ,GAAG,QAAQ,WAAW;YACtB,gBAAgB,UAAU;QAC5B;QAEA,mEAAmE;QACnE,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,MAAM,YAAY,WAAW,SAAS,CAAC,CAAC,IAAM,MAAM;gBACpD,IAAI,cAAc,CAAC,GAAG;oBACpB,yBAAyB;gBAC3B;gBACA,6CAA6C;gBAC7C,gBAAgB,uBAAuB,qBAAqB,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE;gBACnF,eAAe,uBAAuB,qBAAqB,CAAC,MAAM,CAAC,KAAK;gBACxE;YAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,MAAM,eAAe,cAAc,SAAS,CAAC,CAAC,IAAM,MAAM;gBAC1D,IAAI,iBAAiB,CAAC,GAAG;oBACvB,4BAA4B;gBAC9B;gBACA,gDAAgD;gBAChD,gBAAgB,uBAAuB,wBAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE;gBACtF,eAAe,uBAAuB,wBAAwB,CAAC,MAAM,CAAC,KAAK;gBAC3E;YAEF,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B,4CAA4C;gBAC5C,gBAAgB,uBAAuB,oBAAoB,CAAC,SAAS,IAAI,EAAE;gBAC3E,eAAe,uBAAuB,oBAAoB,CAAC,KAAK;gBAChE;YAEF;gBACE;QACJ;IACF;IAEA,MAAM,iBAAiB,CAAC,UAAsC,sBAC5D,8OAAC;YAAsB,WAAU;;8BAC/B,8OAAC;oBAAI,SAAS,IAAM,WAAW,SAAS,EAAE;;sCACxC,8OAAC;4BAAE,WAAU;;gCACV,EAAE;gCAAY;gCAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,GAAG,GAAG,QAAQ;gCAAE;8CAAC,8OAAC,oJAAA,CAAA,UAAa;oCAAC,WAAW,cAAc,QAAQ,CAAC,SAAS,EAAE,IAAI,WAAW;;;;;;;;;;;;sCAEvI,8OAAC;sCAAI,SAAS,QAAQ;;;;;;;;;;;;gBAEvB,cAAc,QAAQ,CAAC,SAAS,EAAE,kBACjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;0CACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gCAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gCAAE,QAAQ;0CAC3D,EAAE;;;;;;0CAEL,8OAAC,8IAAA,CAAA,UAAQ;gCAAC,MAAM;gCAAG,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gCAAE,SAAS;gCAAS,aAAa,EAAE;gCAAyB,WAAU;;;;;;4BACrH,MAAM,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,qBAAO,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gCAAC,SAAS,EAAE;;;;;uCAA+B;;;;;;;;;;;2BAiBjH;;WA/BI,SAAS,EAAE;;;;;IAmCvB,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK,mIAAA,CAAA,iBAAc,CAAC,YAAY;gBAC9B,OAAO,uBAAuB,oBAAoB,EAAE,WAAW,IAAI,CAAC,UAAU,QAAU,eAAe,UAAU,WAAW,EAAE;YAChI,KAAK,mIAAA,CAAA,iBAAc,CAAC,aAAa;gBAC/B,OACE,uBAAuB,qBAAqB,EAAE,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,UAAU,QAAU,eAAe,UAAU,WAAW,EAAE;YAE7I,KAAK,mIAAA,CAAA,iBAAc,CAAC,gBAAgB;gBAClC,OACE,uBAAuB,wBAAwB,EAAE,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,UAAU,QAAU,eAAe,UAAU,WAAW,EAAE;YAEhJ;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,YAAY,CAAC,OAAkB,SAAS,mIAAA,CAAA,iBAAc,CAAC,gBAAgB,GAAG,gBAAgB;IAEhG,qBACE,8OAAC;QAAI,WAAW,+JAAA,CAAA,UAAK,CAAC,sBAAsB;;0BAC1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAA,CAAA,UAAM;gCACL,WAAU;gCACV,SAAS;oCACP,IAAI,aAAa;wCACf;oCACF,OAAO;wCACL;oCACF;gCACF;;kDAEA,8OAAC,8IAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,cAAc,EAAE,oBAAoB,EAAE;;;;;;;0CAGzC,8OAAC,kKAAA,CAAA,UAAe;gCAAC,aAAa;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,oJAAA,CAAA,UAAa;oDAAC,SAAS;wDACtB,IAAI,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,YAAY,EAAE;4DAC/C;wDACF;oDACF;;;;;;gDACC,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,YAAY,iBAC1C;;wDACG,EAAE;wDAA6C;sEAAC,8OAAC;sEAAM,EAAE;;;;;;;iFAG5D;;wDACG,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,aAAa,GAAG,EAAE,6BAA6B,EAAE;wDAA+B;sEAChH,8OAAC;sEAAM,EAAE;;;;;;;;;;;;;;sDAIf,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;4CAAqC,SAAS,IAAM,OAAO,IAAI,CAAC,YAAY;;8DAC5F,8OAAC,wJAAA,CAAA,UAAiB;oDAAC,WAAU;;;;;;gDAC5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAKX,8OAAC;wBAAK,UAAU,aAAa,CAAC,OAAS,kBAAkB;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,aAAa,IAAI,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,gBAAgB,iBAC9F;;0DACE,8OAAC;gDAAI,WAAW,+JAAA,CAAA,UAAK,CAAC,iBAAiB;0DACrC,cAAA,8OAAC;;sEACC,8OAAC;;8EACC,8OAAC;oEAAK,WAAW,+JAAA,CAAA,UAAK,CAAC,OAAO;;;;;;gEAC7B,EAAE;;;;;;;sEAEL,8OAAC;;8EACC,8OAAC;oEAAK,WAAW,+JAAA,CAAA,UAAK,CAAC,SAAS;;;;;;gEAC/B,EAAE;;;;;;;;;;;;;;;;;;0DAQT,8OAAC;gDAAG,WAAU;gDAAuB,cAAW;0DAC7C,UAAU,cAAc,IAAI,CAAC,OAAO,sBACnC,8OAAC;wDAEC,WAAW,CAAC,WAAW,EAAE,UAAU,gBAAgB,YAAY,iBAAiB,aAAa,SAAS,cAAc,IAAI;wDACxH,UAAU;wDACV,SAAS,IAAM,qBAAqB;;4DAEnC;4DACA,iBAAiB,aAAa,uBAC7B,8OAAC;gEAAK,WAAU;0EACb,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,aAAa,GACzC,uBAAuB,qBAAqB,CAAC,MAAM,EAAE,kBACrD,uBAAuB,wBAAwB,CAAC,MAAM,EAAE;;;;;uEAE5D;;uDAZC;;;;;;;;;;;uDAiBX;kDACJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAY,mBAAmB;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEACE,EAAE;gEAAc;8EAAC,8OAAC;8EAAM,EAAE;;;;;;;;;;;;sEAE7B,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAQ;8EAAkB,EAAE;;;;;;8EAChD,8OAAC,8IAAA,CAAA,UAAQ;oEACP,MAAM;oEACN,MAAK;oEACL,SAAS;oEACT,aAAa,EAAE;oEACf,WAAU;;;;;;8EAEZ,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,OAAO,cAAc,EAAE;;;;;;;;;;;;sEAEtD,8OAAC,qJAAA,CAAA,UAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDACE,EAAE;oDAAU;kEACb,8OAAC;wDAAK,WAAU;;4DACb,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,YAAY,GAAG,EAAE,YAAY;4DAAc;4DAAE,EAAE;;;;;;;;;;;;;0DAGnF,8OAAC;;oDACE,EAAE;oDAA2B;oDAAE,gBAAgB,mIAAA,CAAA,iBAAc,CAAC,YAAY,GAAG,EAAE,kBAAkB,cAAc,WAAW;oDAAI;oDAC9H,EAAE;oDAAW;oDAAE,EAAE;;;;;;;;;;;;;kDAGtB,8OAAC;wCAAG,WAAU;kDACX,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,8OAAC;gDACC,WAAW,QAAQ,IAAK,QAAQ,MAAM,cAAc,mBAAmB,YAAa,QAAQ,MAAM,cAAc,WAAW;gDAE3H,SAAS;oDACP,eAAe,QAAQ,MAAM,cAAc,IAAI,QAAQ;gDACzD;0DAEC,QAAQ,IAAI,EAAE,aAAa,QAAQ;+CAL/B;;;;;;;;;;kDASX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;sDAAI,EAAE;;;;;;;;;;;;;;;;;0CAGX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,qCACA,8OAAC,4IAAA,CAAA,UAAM;gDAAC,WAAU;gDAAyB,MAAK;gDAAS,UAAU,WAAW;gDAAQ,SAAS;0DAC5F,EAAE;;;;;uDAEH;0DACJ,8OAAC,4IAAA,CAAA,UAAM;gDAAC,WAAU;gDAA8B,MAAK;gDAAS,UAAU,WAAW;gDAAQ,SAAS;0DACjG,EAAE;;;;;;;;;;;;kDAGP,8OAAC,4IAAA,CAAA,UAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAS,IAAM,yBAAyB;wCACxC,SAAS;wCACT,UAAU,WAAW;kDAEpB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;YAKV,sCACC,8OAAC,uJAAA,CAAA,UAAiB;gBAChB,eAAe,IAAM,yBAAyB;gBAC9C,qBAAqB;gBACrB,UAAU,WAAW;;;;;uBAErB;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 2150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/interview-question/page.tsx"], "sourcesContent": ["\"use client\";\nimport InterviewQuestion from \"@/components/views/conductInterview/InterviewQuestion\";\nimport React from \"react\";\n\nconst page = ({ searchParams }: { searchParams: Promise<{ interviewId: string; jobApplicationId: string; resumeLink: string }> }) => {\n  return (\n    <div>\n      <InterviewQuestion params={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,YAAY,EAAoG;IAC9H,qBACE,8OAAC;kBACC,cAAA,8OAAC,oKAAA,CAAA,UAAiB;YAAC,QAAQ;;;;;;;;;;;AAGjC;uCAEe", "debugId": null}}, {"offset": {"line": 2180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
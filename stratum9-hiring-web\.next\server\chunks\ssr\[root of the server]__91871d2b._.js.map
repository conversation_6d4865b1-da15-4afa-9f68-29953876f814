{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/jobRequirements/jobServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse, IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport { GenerateJobSchema } from \"@/validations/jobRequirementsValidations\";\n\n/**\n * Saves job details to the backend\n * @param formData Job requirement form data\n * @returns Job details response\n */\nexport const saveJobDetails = (formData: GenerateJobSchema): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.SAVE_JOB_DETAILS, formData);\n};\n\nexport interface Job {\n  id: number;\n  title: string;\n  jobId: string;\n  postedDate: string;\n  updatedDate: string;\n  isActive: boolean;\n  applicationCount?: number;\n  finalJobDescriptionHtml: string;\n}\n\nexport type JobsApiResponse = ApiResponse<Job[]>;\n\n/**\n * Fetch paginated jobs metadata from the API\n * @param data Object with pagination and filter params\n * @returns Promise resolving to JobsApiResponse\n */\nexport const fetchJobsMeta = (data: {\n  page?: number;\n  limit?: number;\n  searchStr?: string;\n  isActive?: boolean;\n  applicationCount?: number;\n}): Promise<IApiResponseCommonInterface<Job[]>> => {\n  // Use 'params' to send query parameters in axios GET\n  return http.get(endpoint.jobRequirements.GET_JOBS_META, { ...data });\n};\n\n/**\n * Fetches the HTML description of a job\n * @param id Job ID\n * @returns Promise resolving to ApiResponse<Job>\n */\nexport const getJobHtmlDescription = (id: string): Promise<ApiResponse<Job>> => {\n  return http.get(endpoint.jobRequirements.GET_JOB_HTML_DESCRIPTION, { id });\n};\n\n/**\n * Updates the HTML description of a job\n * @param htmlData Object containing job ID and HTML description\n * @returns Promise resolving to ApiResponse<Job>\n */\nexport const updateJobDescription = (htmlData: { jobId: number; finalJobDescriptionHtml: string }): Promise<ApiResponse<Job>> => {\n  return http.put(endpoint.jobRequirements.UPDATE_JOB_DESCRIPTION, htmlData);\n};\n\n/**\n * Generates PDF from job title and editor content\n * @param data Object containing job title and editor content\n * @returns Promise resolving to ApiResponse\n */\nexport const generatePDF = (data: { jobTitle: string; editorContent: string }): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.GENERATE_PDF, data);\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AASO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,gBAAgB,EAAE;AAC9D;AAoBO,MAAM,gBAAgB,CAAC;IAO5B,qDAAqD;IACrD,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,aAAa,EAAE;QAAE,GAAG,IAAI;IAAC;AACpE;AAOO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE;QAAE;IAAG;AAC1E;AAOO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE;AACnE;AAOO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,YAAY,EAAE;AAC1D", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/CopyIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction CopyIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"25\" height=\"25\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M26.5713 2.16016H9.69728C7.90088 2.16016 6.43948 3.62596 6.43948 5.42776V6.43946H5.41698C3.62058 6.43946 2.15918 7.90526 2.15918 9.70806V26.5724C2.15918 28.3742 3.62058 29.84 5.41698 29.84H22.2915C24.0937 29.84 25.5596 28.3742 25.5596 26.5724V25.5607H26.5713C28.3735 25.5607 29.8399 24.0949 29.8399 22.2921V5.42766C29.8398 3.62596 28.3735 2.16016 26.5713 2.16016ZM23.5596 26.5723C23.5596 27.2715 22.9908 27.8399 22.2915 27.8399H5.41698C4.72358 27.8399 4.15918 27.2715 4.15918 26.5723V9.70796C4.15918 9.00876 4.72368 8.43936 5.41698 8.43936H7.43948H22.2915C22.9907 8.43936 23.5596 9.00866 23.5596 9.70796V24.5605V26.5723ZM27.8398 22.292C27.8398 22.9912 27.271 23.5606 26.5712 23.5606H25.5595V9.70796C25.5595 7.90526 24.0937 6.43936 22.2914 6.43936H8.43938V5.42766C8.43938 4.72846 9.00388 4.16006 9.69718 4.16006H26.5712C27.2709 4.16006 27.8398 4.72846 27.8398 5.42766V22.292Z\"\n        fill=\"#436EB6\"\n      />\n    </svg>\n  );\n}\n\nexport default CopyIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DownloadResumeIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction DownloadResumeIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"24\" height=\"23\" viewBox=\"0 0 32 32\" fill=\"#436EB6\">\n      <g>\n        <path\n          d=\"M20.0625 -0.162109C20.3006 -0.161974 20.5276 -0.0563589 20.6914 0.116211H20.6904L28.5752 8.38672L28.5771 8.38867L28.6768 8.51855C28.7671 8.65777 28.8261 8.82269 28.8262 8.99609V27.7402C28.8262 30.0992 26.8799 32.0371 24.5215 32.0371H7.5791C5.22064 32.0371 3.27441 30.0992 3.27441 27.7402V4.13477C3.27441 1.77607 5.22064 -0.162109 7.5791 -0.162109H20.0625ZM7.5791 1.58301C6.18101 1.58301 5.01953 2.74487 5.01953 4.13477V27.7402C5.01953 29.1378 6.18845 30.292 7.5791 30.292H24.5215C25.9198 30.292 27.0811 29.138 27.0811 27.7402V9.85352H22.6904C20.7573 9.85352 19.1963 8.30187 19.1963 6.36816V1.58301H7.5791ZM20.9424 6.36816C20.9424 7.33265 21.7248 8.1084 22.6904 8.1084H25.8887L20.9424 2.91504V6.36816Z\"\n          strokeWidth=\"0.2\"\n        />\n        <path\n          d=\"M22.4121 24.959C22.8993 24.9592 23.292 25.351 23.292 25.832C23.2917 26.3119 22.8998 26.7038 22.4199 26.7041H9.68945C9.20934 26.7041 8.81667 26.3121 8.81641 25.832C8.81641 25.3515 9.2092 24.959 9.68945 24.959H22.4121Z\"\n          strokeWidth=\"0.2\"\n        />\n        <path\n          d=\"M16.0498 10.4824C16.5301 10.4824 16.9229 10.8749 16.9229 11.3555V19.542L19.9424 16.3027C20.2651 15.9542 20.8224 15.928 21.1729 16.2598L21.2354 16.3223C21.5055 16.6292 21.5268 17.0958 21.2744 17.4238L21.2168 17.4912L16.6875 22.3525L16.6865 22.3535C16.5222 22.5264 16.2954 22.6318 16.0498 22.6318C15.8044 22.6317 15.5775 22.5264 15.4131 22.3535V22.3525L10.8906 17.4912V17.4902C10.5584 17.1393 10.5871 16.5904 10.9336 16.2607C11.2626 15.9481 11.767 15.9532 12.1006 16.2412L12.165 16.3027L12.166 16.3037L15.1768 19.54V11.3555C15.1768 10.875 15.5697 10.4826 16.0498 10.4824Z\"\n          strokeWidth=\"0.2\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_1697\">\n          <rect width=\"32\" height=\"32\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default DownloadResumeIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,mBAAmB,EAAE,SAAS,EAA0B;IAC/D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;;kCACC,8OAAC;wBACC,GAAE;wBACF,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,aAAY;;;;;;;;;;;;0BAGhB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/jobRequirement/PdfGenerator.tsx"], "sourcesContent": ["import React from \"react\";\nimport { toast } from \"react-hot-toast\";\nimport html2pdf from \"html2pdf.js\";\nimport Button from \"@/components/formElements/Button\";\nimport DownloadResumeIcon from \"@/components/svgComponents/DownloadResumeIcon\";\nimport { http } from \"@/utils/http\";\ntype PdfGeneratorProps = {\n  content: string;\n  fileName: string;\n  onLoadingChange?: (isLoading: boolean) => void;\n  title?: string;\n  subtitle?: string;\n  companyLogo?: string;\n  watermark?: string;\n  theme?: \"default\" | \"professional\" | \"modern\" | \"minimal\";\n  pageBreaks?: boolean;\n  footerLogo?: string;\n};\n\nconst PdfGenerator = ({\n  content,\n  fileName,\n  onLoadingChange,\n  title,\n  subtitle,\n  companyLogo,\n  watermark,\n  theme = \"default\",\n  pageBreaks = true,\n  footerLogo,\n}: PdfGeneratorProps) => {\n  const [isLoading, setIsLoading] = React.useState(false);\n  const [footerLogoBase64, setFooter<PERSON>ogoBase64] = React.useState(\"\");\n\n  console.log(\"footerLogoBase64\", footerLogoBase64);\n\n  const convertImageUrlToBase64 = async (url: string): Promise<string> => {\n    try {\n      const response = await http.get(url, {\n        responseType: \"arraybuffer\",\n      });\n\n      const base64 = Buffer.from(response.data, \"binary\").toString(\"base64\");\n      return `data:image/png;base64,${base64}`;\n    } catch (error) {\n      console.error(\"Error converting image to base64:\", error);\n      return \"\"; // Return empty string if conversion fails\n    }\n  };\n  const getThemeStyles = (theme: string) => {\n    const baseStyles = {\n      fontFamily: \"Arial, sans-serif\",\n      lineHeight: \"1.6\",\n      color: \"#333\",\n      padding: \"15mm 20mm 20mm 20mm\",\n      boxSizing: \"border-box\",\n      width: \"100%\",\n      minHeight: \"100vh\",\n      position: \"relative\",\n    };\n\n    const themes = {\n      default: {\n        ...baseStyles,\n        fontSize: \"11pt\",\n        backgroundColor: \"#ffffff\",\n      },\n      professional: {\n        ...baseStyles,\n        fontSize: \"10pt\",\n        fontFamily: \"Georgia, serif\",\n        backgroundColor: \"#fafafa\",\n        borderLeft: \"4px solid #2563eb\",\n        paddingLeft: \"24mm\",\n      },\n      modern: {\n        ...baseStyles,\n        fontSize: \"11pt\",\n        fontFamily: \"Helvetica, Arial, sans-serif\",\n        backgroundColor: \"#ffffff\",\n        borderTop: \"3px solid #10b981\",\n      },\n      minimal: {\n        ...baseStyles,\n        fontSize: \"12pt\",\n        fontFamily: \"Helvetica, Arial, sans-serif\",\n        backgroundColor: \"#ffffff\",\n        padding: \"20mm\",\n      },\n    };\n\n    return themes[theme as keyof typeof themes] || themes.default;\n  };\n\n  const createStyledContent = (content: string) => {\n    const themeStyles = getThemeStyles(theme);\n\n    return `\n      <div style=\"${Object.entries(themeStyles)\n        .map(([key, value]) => `${key.replace(/([A-Z])/g, \"-$1\").toLowerCase()}: ${value}`)\n        .join(\"; \")}\">\n        \n        ${\n          companyLogo\n            ? `\n          <div style=\"text-align: center; margin-bottom: 20px;\">\n            <img src=\"${companyLogo}\" alt=\"Company Logo\" style=\"max-height: 40px; max-width: 150px;\" />\n          </div>\n        `\n            : \"\"\n        }\n        \n        ${\n          title\n            ? `\n          <div style=\"text-align: center; margin-bottom: 15px;\">\n            <h1 style=\"\n              margin: 0; \n              font-size: ${theme === \"minimal\" ? \"18pt\" : \"16pt\"}; \n              font-weight: bold; \n              color: ${theme === \"professional\" ? \"#1f2937\" : theme === \"modern\" ? \"#10b981\" : \"#2563eb\"};\n              border-bottom: ${theme === \"minimal\" ? \"none\" : \"2px solid #e5e7eb\"};\n              padding-bottom: ${theme === \"minimal\" ? \"0\" : \"10px\"};\n            \">\n              ${title}\n            </h1>\n          </div>\n        `\n            : \"\"\n        }\n        \n        ${\n          subtitle\n            ? `\n          <div style=\"text-align: center; margin-bottom: 25px;\">\n            <p style=\"\n              margin: 0; \n              font-size: 12pt; \n              color: #6b7280; \n              font-style: italic;\n            \">\n              ${subtitle}\n            </p>\n          </div>\n        `\n            : \"\"\n        }\n        \n        <div style=\"position: relative; z-index: 1;\">\n          ${content}\n        </div>\n        \n        ${\n          watermark\n            ? `\n          <div style=\"\n            position: fixed;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%) rotate(-45deg);\n            font-size: 48pt;\n            color: rgba(0, 0, 0, 0.05);\n            z-index: 0;\n            pointer-events: none;\n            font-weight: bold;\n            white-space: nowrap;\n          \">\n            ${watermark}\n          </div>\n        `\n            : \"\"\n        }\n        \n        <div style=\"\n          position: fixed;\n          bottom: 10mm;\n          right: 20mm;\n          font-size: 8pt;\n          color: #9ca3af;\n          z-index: 2;\n        \">\n          Generated on ${new Date().toLocaleDateString()}\n        </div>\n      </div>\n\n      ${\n        footerLogoBase64\n          ? `\n          <div style=\"text-align: center; margin-bottom: 20px;\">\n            <img src=\"${footerLogoBase64}\" alt=\"Footer Logo\" style=\"max-height: 40px; max-width: 150px;\" />\n          </div>\n        `\n          : \"\"\n      }\n      \n      <style>\n        @media print {\n          ${\n            pageBreaks\n              ? `\n            h1, h2, h3 { \n              page-break-after: avoid; \n              page-break-inside: avoid; \n            }\n            \n            p, li { \n              page-break-inside: avoid; \n            }\n            \n            .page-break { \n              page-break-before: always; \n            }\n            \n            .no-break { \n              page-break-inside: avoid; \n            }\n          `\n              : \"\"\n          }\n          \n          table { \n            border-collapse: collapse; \n            width: 100%; \n            margin: 10px 0; \n          }\n          \n          table, th, td { \n            border: 1px solid #ddd; \n          }\n          \n          th, td { \n            padding: 8px; \n            text-align: left; \n          }\n          \n          th { \n            background-color: #f8f9fa; \n            font-weight: bold; \n          }\n          \n          ul, ol { \n            margin: 10px 0; \n            padding-left: 25px; \n          }\n          \n          li { \n            margin-bottom: 5px; \n          }\n          \n          blockquote {\n            margin: 15px 0;\n            padding: 10px 15px;\n            border-left: 4px solid #e5e7eb;\n            background-color: #f9fafb;\n            font-style: italic;\n          }\n          \n          code {\n            background-color: #f3f4f6;\n            padding: 2px 4px;\n            border-radius: 3px;\n            font-family: 'Courier New', monospace;\n            font-size: 90%;\n          }\n          \n          pre {\n            background-color: #f3f4f6;\n            padding: 15px;\n            border-radius: 5px;\n            overflow-x: auto;\n            font-family: 'Courier New', monospace;\n            font-size: 90%;\n            line-height: 1.4;\n          }\n        }\n      </style>\n    `;\n  };\n\n  const generatePdf = async () => {\n    if (!content) {\n      toast.error(\"No content to generate PDF\");\n      return;\n    }\n\n    try {\n      setIsLoading(true);\n      if (onLoadingChange) onLoadingChange(true);\n\n      // Create a temporary div with the styled content\n      const element = document.createElement(\"div\");\n      element.innerHTML = createStyledContent(content);\n\n      let tempFooterLogoBase64 = \"\";\n      if (footerLogo) {\n        tempFooterLogoBase64 = await convertImageUrlToBase64(footerLogo);\n        setFooterLogoBase64(tempFooterLogoBase64);\n      }\n\n      // Enhanced PDF options\n      const options = {\n        filename: `${fileName.replace(/[^a-zA-Z0-9]/g, \"_\")}_${new Date().toISOString().slice(0, 19).replace(/[-:T]/g, \"\")}.pdf`,\n        image: {\n          type: \"jpeg\",\n          quality: 0.98,\n        },\n        html2canvas: {\n          scale: 2,\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: \"#ffffff\",\n          removeContainer: true,\n          logging: false,\n        },\n        jsPDF: {\n          unit: \"mm\",\n          format: \"a4\",\n          orientation: \"portrait\",\n          compress: true,\n        },\n        pagebreak: pageBreaks\n          ? {\n              mode: [\"avoid-all\", \"css\", \"legacy\"],\n              before: \".page-break\",\n              after: \".page-break-after\",\n              avoid: \".no-break\",\n            }\n          : undefined,\n      };\n\n      // Generate and download PDF\n      await html2pdf().set(options).from(element).save();\n\n      toast.success(\"PDF generated successfully\");\n    } catch (error) {\n      console.error(\"Error generating PDF:\", error);\n      toast.error(\"Failed to generate PDF. Please try again.\");\n    } finally {\n      setIsLoading(false);\n      if (onLoadingChange) onLoadingChange(false);\n    }\n  };\n\n  return (\n    <Button\n      className=\"clear-btn p-0 ms-3\"\n      disabled={!content || isLoading}\n      onClick={generatePdf}\n      title={isLoading ? \"Generating PDF...\" : \"Download PDF\"}\n    >\n      <DownloadResumeIcon />\n    </Button>\n  );\n};\n\nexport default PdfGenerator;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAcA,MAAM,eAAe,CAAC,EACpB,OAAO,EACP,QAAQ,EACR,eAAe,EACf,KAAK,EACL,QAAQ,EACR,WAAW,EACX,SAAS,EACT,QAAQ,SAAS,EACjB,aAAa,IAAI,EACjB,UAAU,EACQ;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE/D,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,OAAI,CAAC,GAAG,CAAC,KAAK;gBACnC,cAAc;YAChB;YAEA,MAAM,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,UAAU,QAAQ,CAAC;YAC7D,OAAO,CAAC,sBAAsB,EAAE,QAAQ;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,IAAI,0CAA0C;QACvD;IACF;IACA,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa;YACjB,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,SAAS;YACT,WAAW;YACX,OAAO;YACP,WAAW;YACX,UAAU;QACZ;QAEA,MAAM,SAAS;YACb,SAAS;gBACP,GAAG,UAAU;gBACb,UAAU;gBACV,iBAAiB;YACnB;YACA,cAAc;gBACZ,GAAG,UAAU;gBACb,UAAU;gBACV,YAAY;gBACZ,iBAAiB;gBACjB,YAAY;gBACZ,aAAa;YACf;YACA,QAAQ;gBACN,GAAG,UAAU;gBACb,UAAU;gBACV,YAAY;gBACZ,iBAAiB;gBACjB,WAAW;YACb;YACA,SAAS;gBACP,GAAG,UAAU;gBACb,UAAU;gBACV,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;YACX;QACF;QAEA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,OAAO;IAC/D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,eAAe;QAEnC,OAAO,CAAC;kBACM,EAAE,OAAO,OAAO,CAAC,aAC1B,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW,GAAG,EAAE,EAAE,OAAO,EACjF,IAAI,CAAC,MAAM;;QAEZ,EACE,cACI,CAAC;;sBAEO,EAAE,YAAY;;QAE5B,CAAC,GACK,GACL;;QAED,EACE,QACI,CAAC;;;;yBAIU,EAAE,UAAU,YAAY,SAAS,OAAO;;qBAE5C,EAAE,UAAU,iBAAiB,YAAY,UAAU,WAAW,YAAY,UAAU;6BAC5E,EAAE,UAAU,YAAY,SAAS,oBAAoB;8BACpD,EAAE,UAAU,YAAY,MAAM,OAAO;;cAErD,EAAE,MAAM;;;QAGd,CAAC,GACK,GACL;;QAED,EACE,WACI,CAAC;;;;;;;;cAQD,EAAE,SAAS;;;QAGjB,CAAC,GACK,GACL;;;UAGC,EAAE,QAAQ;;;QAGZ,EACE,YACI,CAAC;;;;;;;;;;;;;YAaH,EAAE,UAAU;;QAEhB,CAAC,GACK,GACL;;;;;;;;;;uBAUc,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;MAInD,EACE,mBACI,CAAC;;sBAES,EAAE,iBAAiB;;QAEjC,CAAC,GACG,GACL;;;;UAIG,EACE,aACI,CAAC;;;;;;;;;;;;;;;;;UAiBP,CAAC,GACK,GACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0DP,CAAC;IACH;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,aAAa;YACb,IAAI,iBAAiB,gBAAgB;YAErC,iDAAiD;YACjD,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG,oBAAoB;YAExC,IAAI,uBAAuB;YAC3B,IAAI,YAAY;gBACd,uBAAuB,MAAM,wBAAwB;gBACrD,oBAAoB;YACtB;YAEA,uBAAuB;YACvB,MAAM,UAAU;gBACd,UAAU,GAAG,SAAS,OAAO,CAAC,iBAAiB,KAAK,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC;gBACxH,OAAO;oBACL,MAAM;oBACN,SAAS;gBACX;gBACA,aAAa;oBACX,OAAO;oBACP,SAAS;oBACT,YAAY;oBACZ,iBAAiB;oBACjB,iBAAiB;oBACjB,SAAS;gBACX;gBACA,OAAO;oBACL,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,UAAU;gBACZ;gBACA,WAAW,aACP;oBACE,MAAM;wBAAC;wBAAa;wBAAO;qBAAS;oBACpC,QAAQ;oBACR,OAAO;oBACP,OAAO;gBACT,IACA;YACN;YAEA,4BAA4B;YAC5B,MAAM,CAAA,GAAA,kJAAA,CAAA,UAAQ,AAAD,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI;YAEhD,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;YACb,IAAI,iBAAiB,gBAAgB;QACvC;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,UAAM;QACL,WAAU;QACV,UAAU,CAAC,WAAW;QACtB,SAAS;QACT,OAAO,YAAY,sBAAsB;kBAEzC,cAAA,8OAAC,yJAAA,CAAA,UAAkB;;;;;;;;;;AAGzB;uCAEe", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/jobRequirement/JobEditor.tsx"], "sourcesContent": ["\"use client\";\n\n// Internal libraries\nimport React, { useState, useEffect, useRef } from \"react\";\n\nimport dynamic from \"next/dynamic\";\nimport { useRouter } from \"next/navigation\";\nimport { useDispatch, useSelector } from \"react-redux\";\n\n// Components\nimport Loader from \"@/components/loader/Loader\";\nimport Button from \"@/components/formElements/Button\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\n\n// Services\nimport { getJobHtmlDescription, saveJobDetails, updateJobDescription } from \"@/services/jobRequirements/jobServices\";\n\n// Redux, constants, interfaces\nimport { clearJobRequirement, selectJobRequirement } from \"@/redux/slices/jobRequirementSlice\";\nimport { clearJobDetails, selectJobDetails } from \"@/redux/slices/jobDetailsSlice\";\nimport { clearSkillsData, selectCareerSkills, selectCultureSpecificSkills, selectRoleSpecificSkills } from \"@/redux/slices/jobSkillsSlice\";\nimport { ISkillData } from \"@/interfaces/jobRequirementesInterfaces\";\nimport ROUTES from \"@/constants/routes\";\nimport { EMPTY_CONTENT_PATTERNS } from \"@/constants/commonConstants\";\nimport { SUN_EDITOR_BUTTON_LIST } from \"@/constants/jobRequirementConstant\";\n\n// CSS\nimport style from \"@/styles/commonPage.module.scss\";\n\n// Import SunEditor dynamically to avoid SSR issues\nconst SunEditor = dynamic(() => import(\"suneditor-react\"), {\n  ssr: false,\n});\n\n// Import SunEditor CSS\nimport \"suneditor/dist/css/suneditor.min.css\";\nimport { useTranslations } from \"next-intl\";\nimport CopyIcon from \"@/components/svgComponents/CopyIcon\";\nimport { toastMessageError, toastMessageSuccess } from \"@/utils/helper\";\nimport PdfGenerator from \"./PdfGenerator\";\n\n/**\n * JobEditor component for creating and editing job requirements\n * @component\n * @description A rich text editor component for creating and managing job requirements\n * @returns {JSX.Element} The JobEditor component\n */\nfunction JobEditor() {\n  const router = useRouter();\n  const dispatch = useDispatch();\n  const jobRequirement = useSelector(selectJobRequirement);\n  const jobDetails = useSelector(selectJobDetails);\n\n  // State for edit mode and job details\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [jobId, setJobId] = useState<number | null>(null);\n  const [jobTitle, setJobTitle] = useState(\"\");\n\n  const [editorContent, setEditorContent] = useState(\"\");\n  const editorRef = useRef<unknown>(null);\n  const cultureSpecificSkills = useSelector(selectCultureSpecificSkills) || [];\n  const careerSkills = useSelector(selectCareerSkills) || [];\n  const roleSpecificSkills = useSelector(selectRoleSpecificSkills) || [];\n  const [isLoading, setIsLoading] = useState(false);\n  const [isEditorLoading, setIsEditorLoading] = useState(true);\n  const t = useTranslations();\n  const tGenerate = useTranslations(\"jobRequirement\");\n  /**\n   * Initializes editor content from Redux store\n   * @function\n   * @description Sets the editor content from job requirement data when available\n   */\n  // Parse URL parameters for edit mode\n  useEffect(() => {\n    // Check URL parameters for edit mode\n    const searchParams = new URLSearchParams(window.location.search);\n    const id = searchParams.get(\"jobId\");\n\n    // Define an async function inside useEffect\n    const loadJobData = async () => {\n      try {\n        // Set edit mode\n        setIsEditMode(true);\n        // Make sure id is not null before parsing\n        if (id) {\n          setJobId(parseInt(id, 10));\n          const result = await getJobHtmlDescription(id);\n          console.log(result);\n          if (result && result.data && result.data.success) {\n            const decoder = document.createElement(\"textarea\");\n            decoder.innerHTML = result.data.data.htmlDescription;\n            const decodedHtml = decoder.value;\n            setEditorContent(decodedHtml);\n            setJobTitle(result.data.data.title);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading job data:\", error);\n      } finally {\n        setIsEditorLoading(false);\n      }\n    };\n\n    // Only require id to activate edit mode\n    if (id) {\n      // Call the async function\n      loadJobData();\n    } else if (jobRequirement && jobRequirement.content) {\n      // Normal mode - set from Redux\n      setEditorContent(jobRequirement.content);\n\n      // Set a timeout to simulate loading and ensure proper initialization\n      const timer = setTimeout(() => {\n        setIsEditorLoading(false);\n      }, 800);\n\n      return () => clearTimeout(timer);\n    } else {\n      // Just initialize the editor\n      const timer = setTimeout(() => {\n        setIsEditorLoading(false);\n      }, 800);\n\n      return () => clearTimeout(timer);\n    }\n\n    // Set a timeout to simulate loading and ensure proper initialization\n    const timer = setTimeout(() => {\n      setIsEditorLoading(false);\n    }, 800);\n\n    return () => clearTimeout(timer);\n  }, [jobRequirement]);\n\n  /**\n   * Updates editor content state\n   * @function handleEditorChange\n   * @param {string} content - The new content from the editor\n   * @description Updates the editor content state with new content\n   */\n  const handleEditorChange = (content: string) => {\n    setEditorContent(content);\n  };\n\n  /**\n   * Checks if editor content is empty or contains only whitespace\n   * @function isEditorEmpty\n   * @param {string} content - The content to check\n   * @returns {boolean} True if content is empty or contains only whitespace\n   * @description Handles various empty content patterns including HTML tags\n   */\n  const isEditorEmpty = (content: string) => {\n    if (!content) return true;\n\n    const trimmed = content.trim();\n    if (trimmed === \"\") return true;\n\n    if (EMPTY_CONTENT_PATTERNS.includes(trimmed)) return true;\n\n    const tempDiv = document.createElement(\"div\");\n    tempDiv.innerHTML = content;\n    const textContent = tempDiv.textContent || tempDiv.innerText || \"\";\n    return textContent.trim() === \"\";\n  };\n\n  /**\n   * Saves job requirement data to backend\n   * @function handleSave\n   * @async\n   * @returns {Promise<void>}\n   * @throws {Error} If save operation fails\n   * @description Validates content, formats job data, and saves to backend\n   */\n  const handleSave = async () => {\n    try {\n      if (isEditorEmpty(editorContent)) {\n        toastMessageError(t(\"enter_job_requirement\"));\n        return;\n      }\n\n      setIsLoading(true);\n\n      if (isEditMode && jobId) {\n        // Edit mode - prepare data for updating existing job\n        const updateData = {\n          jobId: jobId,\n          finalJobDescriptionHtml: editorContent,\n        };\n        // Call API to update job details\n        const result = await updateJobDescription(updateData); // Using type assertion for now\n\n        if (result && result.data && result.data.success) {\n          // Show success message\n          toastMessageSuccess(t(result.data.message));\n          // empty job editor\n          setEditorContent(\"\");\n          // Navigate to active jobs page\n          router.push(ROUTES.JOBS.ACTIVE_JOBS);\n        } else {\n          throw new Error(t(\"failed_to_update_job_discription\"));\n        }\n      } else {\n        // Regular mode - use existing JobEditor logic with proper typing\n        console.log(\"jdlink\", jobDetails.jd_link);\n        const requestData = {\n          title: jobDetails.title,\n          employment_type: jobDetails.employment_type,\n          salary_range: jobDetails.salary_range,\n          salary_cycle: jobDetails.salary_cycle,\n          location_type: jobDetails.location_type,\n          state: jobDetails.state,\n          city: jobDetails.city,\n          role_overview: jobDetails.role_overview,\n          experience_level: jobDetails.experience_level,\n          responsibilities: jobDetails.responsibilities,\n          educations_requirement: jobDetails.educations_requirement,\n          certifications: jobDetails.certifications,\n          skills_and_software_expertise: jobDetails.skills_and_software_expertise,\n          experience_required: jobDetails.experience_required,\n          ideal_candidate_traits: jobDetails.ideal_candidate_traits,\n          about_company: jobDetails.about_company,\n          perks_benefits: jobDetails.perks_benefits,\n          tone_style: jobDetails.tone_style,\n          additional_info: jobDetails.additional_info,\n          compliance_statement: jobDetails.compliance_statement,\n          show_compliance: jobDetails.show_compliance,\n          final_job_description_html: editorContent,\n          hiring_type: jobDetails.hiring_type,\n          department_id: jobDetails.department_id,\n          jd_link: jobDetails.jd_link,\n          career_skills: careerSkills.map((skill: ISkillData) => ({\n            name: skill.name,\n            description: skill.description,\n          })),\n          role_specific_skills: roleSpecificSkills.map((skill: ISkillData) => ({\n            id: skill.id,\n            name: skill.name,\n            description: skill.description,\n          })),\n          culture_specific_skills: cultureSpecificSkills.map((skill) => ({\n            id: skill.id,\n            name: skill.name,\n            description: skill.description,\n          })),\n        };\n\n        const saveJobResponse = await saveJobDetails(requestData);\n\n        if (saveJobResponse && saveJobResponse.data && saveJobResponse.data.success) {\n          toastMessageSuccess(t(saveJobResponse.data.message));\n          // Empty redux store\n          dispatch(clearJobRequirement());\n          dispatch(clearJobDetails());\n          dispatch(clearSkillsData());\n          router.push(ROUTES.JOBS.ACTIVE_JOBS);\n        } else {\n          toastMessageError(t(saveJobResponse?.data?.message) || t(\"save_job_failed\"));\n        }\n      }\n    } catch (error) {\n      console.error(t(\"save_job_error_log\"), error);\n      toastMessageError(isEditMode ? t(\"update_job_description_error\") : t(\"save_job_unknown_error\"));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  /**\n   * Downloads job requirement as PDF\n   * @function handleDownloadPDF\n   * @async\n   * @returns {Promise<void>}\n   * @throws {Error} If PDF generation fails\n   * @description Generates and downloads job requirement content as PDF\n   */\n  // we will use later if need or remove\n  // const handleDownloadPDF = async () => {\n  //   if (isEditorEmpty(editorContent)) {\n  //     toastMessageError(t(\"pdf_job_req_empty\"));\n  //     return;\n  //   }\n\n  //   try {\n  //     // Show loading toast\n  //     toast.loading(t(\"pdf_generating\"));\n  //     const generatePDFResponse = await generatePDF({\n  //       jobTitle: jobDetails.title,\n  //       editorContent: editorContent,\n  //     });\n  //     if (generatePDFResponse && generatePDFResponse.data && generatePDFResponse.data.success) {\n  //       toast.dismiss();\n  //       toastMessageSuccess(t(generatePDFResponse.data.message));\n  //     } else {\n  //       toast.dismiss();\n  //       toastMessageError(t(\"pdf_generation_failed\"));\n  //     }\n  //   } catch (error) {\n  //     console.error(\"Error generating PDF:\", error);\n  //     toast.dismiss();\n  //     toastMessageError(t(\"pdf_generation_failed\"));\n  //   }\n  // };\n\n  // Handle copy content\n  const handleCopyContent = async () => {\n    if (isEditorEmpty(editorContent)) {\n      toastMessageError(t(\"copy_job_req_empty\"));\n      return;\n    }\n\n    try {\n      // We'll implement two methods of copying to support both plain text and rich text\n\n      // Method 1: Copy as rich text (HTML)\n      // Create a blob with HTML mime type\n      const htmlBlob = new Blob([editorContent], { type: \"text/html\" });\n      // Create a temporary div to extract text content as fallback\n      const tempDiv = document.createElement(\"div\");\n      tempDiv.innerHTML = editorContent;\n      const textContent = tempDiv.innerText || tempDiv.textContent || \"\";\n      const textBlob = new Blob([textContent], { type: \"text/plain\" });\n\n      // Try the modern API first (which supports HTML)\n      if (navigator.clipboard.write) {\n        await navigator.clipboard.write([\n          new ClipboardItem({\n            \"text/html\": htmlBlob,\n            \"text/plain\": textBlob,\n          }),\n        ]);\n        toastMessageSuccess(t(\"copy_html_success\"));\n      } else {\n        // Fallback to the text-only method\n        await navigator.clipboard.writeText(textContent);\n        toastMessageSuccess(t(\"copy_text_success\"));\n      }\n    } catch (error) {\n      console.error(\"Copy error:\", error);\n\n      // Last resort fallback - if the modern approach fails\n      try {\n        // Create element to copy from\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = editorContent;\n        document.body.appendChild(tempDiv);\n\n        // Select the content\n        const range = document.createRange();\n        range.selectNodeContents(tempDiv);\n        const selection = window.getSelection();\n        selection?.removeAllRanges();\n        selection?.addRange(range);\n\n        // Execute copy command\n        document.execCommand(\"copy\");\n\n        // Clean up\n        selection?.removeAllRanges();\n        document.body.removeChild(tempDiv);\n\n        toastMessageSuccess(t(\"copy_success\"));\n      } catch (fallbackError) {\n        toastMessageError(t(\"copy_fail\"));\n        console.error(t(\"copy_error\"), fallbackError);\n      }\n    }\n  };\n\n  return (\n    <div className={style.job_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                  <BackArrowIcon\n                    onClick={() => (isEditMode ? router.push(ROUTES.JOBS.ACTIVE_JOBS) : router.push(`${ROUTES.JOBS.CULTURE_BASED_SKILLS}`))}\n                  />\n                {isEditMode ? (\n                  <>\n                    {jobTitle ? tGenerate(\"edit_job_description\") : \"\"} <span>{jobTitle || \"\"}</span>\n                  </>\n                ) : (\n                  <>\n                    {jobDetails.title ? tGenerate(\"job_requirment_for\") : \"\"} <span>{jobDetails?.title || \"\"}</span>\n                  </>\n                )}\n              </h2>\n              {editorContent && (\n                <div className=\"d-flex\">\n                  <Button className=\"clear-btn p-0 m-0\" onClick={() => handleCopyContent()} disabled={!editorContent || isLoading}>\n                    <CopyIcon />\n                  </Button>\n                  <PdfGenerator\n                    content={editorContent}\n                    fileName={`job_requirement_${jobTitle}`}\n                    onLoadingChange={setIsLoading}\n                    footerLogo=\"https://stratum9-images-dev.s3.us-east-1.amazonaws.com/stratum9_logo.png\"\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n        <div className=\"inner-section\">\n          {/* Display message if no job requirement has been generated */}\n          {!editorContent && !isEditorLoading ? (\n            <div className=\"text-center\">\n              <p>{tGenerate(\"no_job_requirment_generated\")}</p>\n            </div>\n          ) : isEditorLoading ? (\n            /* Skeleton loader for SunEditor */\n            <div className=\"editor-skeleton\">\n              {/* Toolbar skeleton */}\n              <div className=\"skeleton-toolbar\">\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n              </div>\n              {/* Content skeleton */}\n              <div className=\"skeleton-content\">\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n              </div>\n            </div>\n          ) : isEditorLoading ? (\n            /* Skeleton loader for SunEditor */\n            <div className=\"editor-skeleton\">\n              {/* Toolbar skeleton */}\n              <div className=\"skeleton-toolbar\">\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n                <div className=\"skeleton-button\"></div>\n              </div>\n              {/* Content skeleton */}\n              <div className=\"skeleton-content\">\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n                <div className=\"skeleton-paragraph\"></div>\n              </div>\n            </div>\n          ) : (\n            /* SunEditor component */\n            <SunEditor\n              setContents={editorContent}\n              onChange={handleEditorChange}\n              getSunEditorInstance={(sunEditor) => {\n                // Store the SunEditor instance in our ref\n                editorRef.current = sunEditor;\n                // Check if content starts with our HTML marker\n                if (editorContent.startsWith(\"HTML:\")) {\n                  setTimeout(() => {\n                    try {\n                      // Clear the editor first\n                      sunEditor.setContents(\"\");\n\n                      // Extract the HTML content without the marker\n                      const htmlContent = editorContent.substring(5);\n\n                      // Insert as HTML rather than setting contents\n                      sunEditor.insertHTML(htmlContent);\n\n                      // Force editor to process the content properly\n                      sunEditor.core.focus();\n                    } catch (err) {\n                      console.error(\"Error setting HTML content:\", err);\n                    }\n                  }, 100);\n                } else if (editorContent) {\n                  setTimeout(() => {\n                    try {\n                      sunEditor.setContents(editorContent);\n                    } catch (error) {\n                      console.error(\"Error setting editor content:\", error);\n                    }\n                  }, 100);\n                }\n              }}\n              setOptions={{\n                buttonList: SUN_EDITOR_BUTTON_LIST,\n                minHeight: \"550px\",\n                defaultStyle: \"font-size: 16px; font-family: Arial, sans-serif;\",\n                formats: [\"p\", \"div\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"],\n                font: [\"Arial\", \"Verdana\", \"Georgia\", \"Courier New\", \"Times New Roman\"],\n                fontSize: [10, 12, 14, 16, 18, 20, 22, 24, 28, 36, 48],\n                toolbarContainer: \"#editor-toolbar\",\n                attributesWhitelist: {\n                  all: \"style\",\n                  span: \"style\",\n                },\n              }}\n            />\n          )}\n        </div>\n\n        {editorContent && (\n          <div className=\"button-align py-5\">\n            <Button className=\"primary-btn rounded-md\" onClick={handleSave} disabled={!editorContent || isLoading}>\n              {isEditMode ? \"Update Job Description\" : \"Save Job Requirement\"} {isLoading && <Loader />}\n            </Button>\n            <Button\n              className=\"dark-outline-btn rounded-md\"\n              onClick={() => (isEditMode ? router.push(ROUTES.JOBS.ACTIVE_JOBS) : router.push(`${ROUTES.JOBS.GENERATE_JOB}`))}\n            >\n              {isEditMode ? t(\"back_to_jobs\") : t(\"back_to_start\")}\n            </Button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default JobEditor;\n"], "names": [], "mappings": ";;;;AAEA,qBAAqB;AACrB;AAEA;AACA;AACA;AAEA,aAAa;AACb;AACA;AACA;AAEA,WAAW;AACX;AAEA,+BAA+B;AAC/B;AACA;AACA;AAEA;AACA;AACA;AAEA,MAAM;AACN;AASA;AACA;AACA;AACA;;AAvCA;;;;;;;;;;;;;;;;;AA6BA,mDAAmD;AACnD,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACxB,KAAK;;;;;;;AAUP;;;;;CAKC,GACD,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,iBAAiB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,6IAAA,CAAA,uBAAoB;IACvD,MAAM,aAAa,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,yIAAA,CAAA,mBAAgB;IAE/C,sCAAsC;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAClC,MAAM,wBAAwB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,wIAAA,CAAA,8BAA2B,KAAK,EAAE;IAC5E,MAAM,eAAe,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,wIAAA,CAAA,qBAAkB,KAAK,EAAE;IAC1D,MAAM,qBAAqB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,wIAAA,CAAA,2BAAwB,KAAK,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAClC;;;;GAIC,GACD,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,eAAe,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC/D,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,4CAA4C;QAC5C,MAAM,cAAc;YAClB,IAAI;gBACF,gBAAgB;gBAChB,cAAc;gBACd,0CAA0C;gBAC1C,IAAI,IAAI;oBACN,SAAS,SAAS,IAAI;oBACtB,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAqB,AAAD,EAAE;oBAC3C,QAAQ,GAAG,CAAC;oBACZ,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;wBAChD,MAAM,UAAU,SAAS,aAAa,CAAC;wBACvC,QAAQ,SAAS,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe;wBACpD,MAAM,cAAc,QAAQ,KAAK;wBACjC,iBAAiB;wBACjB,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;oBACpC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,SAAU;gBACR,mBAAmB;YACrB;QACF;QAEA,wCAAwC;QACxC,IAAI,IAAI;YACN,0BAA0B;YAC1B;QACF,OAAO,IAAI,kBAAkB,eAAe,OAAO,EAAE;YACnD,+BAA+B;YAC/B,iBAAiB,eAAe,OAAO;YAEvC,qEAAqE;YACrE,MAAM,QAAQ,WAAW;gBACvB,mBAAmB;YACrB,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO;YACL,6BAA6B;YAC7B,MAAM,QAAQ,WAAW;gBACvB,mBAAmB;YACrB,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;QAEA,qEAAqE;QACrE,MAAM,QAAQ,WAAW;YACvB,mBAAmB;QACrB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAe;IAEnB;;;;;GAKC,GACD,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;IACnB;IAEA;;;;;;GAMC,GACD,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,UAAU,QAAQ,IAAI;QAC5B,IAAI,YAAY,IAAI,OAAO;QAE3B,IAAI,mIAAA,CAAA,yBAAsB,CAAC,QAAQ,CAAC,UAAU,OAAO;QAErD,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,SAAS,GAAG;QACpB,MAAM,cAAc,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI;QAChE,OAAO,YAAY,IAAI,OAAO;IAChC;IAEA;;;;;;;GAOC,GACD,MAAM,aAAa;QACjB,IAAI;YACF,IAAI,cAAc,gBAAgB;gBAChC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACpB;YACF;YAEA,aAAa;YAEb,IAAI,cAAc,OAAO;gBACvB,qDAAqD;gBACrD,MAAM,aAAa;oBACjB,OAAO;oBACP,yBAAyB;gBAC3B;gBACA,iCAAiC;gBACjC,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,+BAA+B;gBAEtF,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;oBAChD,uBAAuB;oBACvB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,OAAO,IAAI,CAAC,OAAO;oBACzC,mBAAmB;oBACnB,iBAAiB;oBACjB,+BAA+B;oBAC/B,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;gBACrC,OAAO;oBACL,MAAM,IAAI,MAAM,EAAE;gBACpB;YACF,OAAO;gBACL,iEAAiE;gBACjE,QAAQ,GAAG,CAAC,UAAU,WAAW,OAAO;gBACxC,MAAM,cAAc;oBAClB,OAAO,WAAW,KAAK;oBACvB,iBAAiB,WAAW,eAAe;oBAC3C,cAAc,WAAW,YAAY;oBACrC,cAAc,WAAW,YAAY;oBACrC,eAAe,WAAW,aAAa;oBACvC,OAAO,WAAW,KAAK;oBACvB,MAAM,WAAW,IAAI;oBACrB,eAAe,WAAW,aAAa;oBACvC,kBAAkB,WAAW,gBAAgB;oBAC7C,kBAAkB,WAAW,gBAAgB;oBAC7C,wBAAwB,WAAW,sBAAsB;oBACzD,gBAAgB,WAAW,cAAc;oBACzC,+BAA+B,WAAW,6BAA6B;oBACvE,qBAAqB,WAAW,mBAAmB;oBACnD,wBAAwB,WAAW,sBAAsB;oBACzD,eAAe,WAAW,aAAa;oBACvC,gBAAgB,WAAW,cAAc;oBACzC,YAAY,WAAW,UAAU;oBACjC,iBAAiB,WAAW,eAAe;oBAC3C,sBAAsB,WAAW,oBAAoB;oBACrD,iBAAiB,WAAW,eAAe;oBAC3C,4BAA4B;oBAC5B,aAAa,WAAW,WAAW;oBACnC,eAAe,WAAW,aAAa;oBACvC,SAAS,WAAW,OAAO;oBAC3B,eAAe,aAAa,GAAG,CAAC,CAAC,QAAsB,CAAC;4BACtD,MAAM,MAAM,IAAI;4BAChB,aAAa,MAAM,WAAW;wBAChC,CAAC;oBACD,sBAAsB,mBAAmB,GAAG,CAAC,CAAC,QAAsB,CAAC;4BACnE,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,aAAa,MAAM,WAAW;wBAChC,CAAC;oBACD,yBAAyB,sBAAsB,GAAG,CAAC,CAAC,QAAU,CAAC;4BAC7D,IAAI,MAAM,EAAE;4BACZ,MAAM,MAAM,IAAI;4BAChB,aAAa,MAAM,WAAW;wBAChC,CAAC;gBACH;gBAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE;gBAE7C,IAAI,mBAAmB,gBAAgB,IAAI,IAAI,gBAAgB,IAAI,CAAC,OAAO,EAAE;oBAC3E,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,gBAAgB,IAAI,CAAC,OAAO;oBAClD,oBAAoB;oBACpB,SAAS,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;oBAC3B,SAAS,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;oBACvB,SAAS,CAAA,GAAA,wIAAA,CAAA,kBAAe,AAAD;oBACvB,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;gBACrC,OAAO;oBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,iBAAiB,MAAM,YAAY,EAAE;gBAC3D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,EAAE,uBAAuB;YACvC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,EAAE,kCAAkC,EAAE;QACvE,SAAU;YACR,aAAa;QACf;IACF;IAEA;;;;;;;GAOC,GACD,sCAAsC;IACtC,0CAA0C;IAC1C,wCAAwC;IACxC,iDAAiD;IACjD,cAAc;IACd,MAAM;IAEN,UAAU;IACV,4BAA4B;IAC5B,0CAA0C;IAC1C,sDAAsD;IACtD,oCAAoC;IACpC,sCAAsC;IACtC,UAAU;IACV,iGAAiG;IACjG,yBAAyB;IACzB,kEAAkE;IAClE,eAAe;IACf,yBAAyB;IACzB,uDAAuD;IACvD,QAAQ;IACR,sBAAsB;IACtB,qDAAqD;IACrD,uBAAuB;IACvB,qDAAqD;IACrD,MAAM;IACN,KAAK;IAEL,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI,cAAc,gBAAgB;YAChC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;QACF;QAEA,IAAI;YACF,kFAAkF;YAElF,qCAAqC;YACrC,oCAAoC;YACpC,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAc,EAAE;gBAAE,MAAM;YAAY;YAC/D,6DAA6D;YAC7D,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,SAAS,GAAG;YACpB,MAAM,cAAc,QAAQ,SAAS,IAAI,QAAQ,WAAW,IAAI;YAChE,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAY,EAAE;gBAAE,MAAM;YAAa;YAE9D,iDAAiD;YACjD,IAAI,UAAU,SAAS,CAAC,KAAK,EAAE;gBAC7B,MAAM,UAAU,SAAS,CAAC,KAAK,CAAC;oBAC9B,IAAI,cAAc;wBAChB,aAAa;wBACb,cAAc;oBAChB;iBACD;gBACD,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;YACxB,OAAO;gBACL,mCAAmC;gBACnC,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAE7B,sDAAsD;YACtD,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,UAAU,SAAS,aAAa,CAAC;gBACvC,QAAQ,SAAS,GAAG;gBACpB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,qBAAqB;gBACrB,MAAM,QAAQ,SAAS,WAAW;gBAClC,MAAM,kBAAkB,CAAC;gBACzB,MAAM,YAAY,OAAO,YAAY;gBACrC,WAAW;gBACX,WAAW,SAAS;gBAEpB,uBAAuB;gBACvB,SAAS,WAAW,CAAC;gBAErB,WAAW;gBACX,WAAW;gBACX,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;YACxB,EAAE,OAAO,eAAe;gBACtB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;gBACpB,QAAQ,KAAK,CAAC,EAAE,eAAe;YACjC;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,QAAQ;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACG,8OAAC,oJAAA,CAAA,UAAa;4CACZ,SAAS,IAAO,aAAa,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;;;;;;wCAExH,2BACC;;gDACG,WAAW,UAAU,0BAA0B;gDAAG;8DAAC,8OAAC;8DAAM,YAAY;;;;;;;yEAGzE;;gDACG,WAAW,KAAK,GAAG,UAAU,wBAAwB;gDAAG;8DAAC,8OAAC;8DAAM,YAAY,SAAS;;;;;;;;;;;;;;gCAI3F,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;4CAAoB,SAAS,IAAM;4CAAqB,UAAU,CAAC,iBAAiB;sDACpG,cAAA,8OAAC,+IAAA,CAAA,UAAQ;;;;;;;;;;sDAEX,8OAAC,6JAAA,CAAA,UAAY;4CACX,SAAS;4CACT,UAAU,CAAC,gBAAgB,EAAE,UAAU;4CACvC,iBAAiB;4CACjB,YAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,8OAAC;oBAAI,WAAU;8BAEZ,CAAC,iBAAiB,CAAC,gCAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAG,UAAU;;;;;;;;;;+BAEd,kBACF,iCAAiC,iBACjC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;+BAGjB,kBACF,iCAAiC,iBACjC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;+BAInB,uBAAuB,iBACvB,8OAAC;wBACC,aAAa;wBACb,UAAU;wBACV,sBAAsB,CAAC;4BACrB,0CAA0C;4BAC1C,UAAU,OAAO,GAAG;4BACpB,+CAA+C;4BAC/C,IAAI,cAAc,UAAU,CAAC,UAAU;gCACrC,WAAW;oCACT,IAAI;wCACF,yBAAyB;wCACzB,UAAU,WAAW,CAAC;wCAEtB,8CAA8C;wCAC9C,MAAM,cAAc,cAAc,SAAS,CAAC;wCAE5C,8CAA8C;wCAC9C,UAAU,UAAU,CAAC;wCAErB,+CAA+C;wCAC/C,UAAU,IAAI,CAAC,KAAK;oCACtB,EAAE,OAAO,KAAK;wCACZ,QAAQ,KAAK,CAAC,+BAA+B;oCAC/C;gCACF,GAAG;4BACL,OAAO,IAAI,eAAe;gCACxB,WAAW;oCACT,IAAI;wCACF,UAAU,WAAW,CAAC;oCACxB,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,iCAAiC;oCACjD;gCACF,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,YAAY,0IAAA,CAAA,yBAAsB;4BAClC,WAAW;4BACX,cAAc;4BACd,SAAS;gCAAC;gCAAK;gCAAO;gCAAM;gCAAM;gCAAM;gCAAM;gCAAM;6BAAK;4BACzD,MAAM;gCAAC;gCAAS;gCAAW;gCAAW;gCAAe;6BAAkB;4BACvE,UAAU;gCAAC;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;gCAAI;6BAAG;4BACtD,kBAAkB;4BAClB,qBAAqB;gCACnB,KAAK;gCACL,MAAM;4BACR;wBACF;;;;;;;;;;;gBAKL,+BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4IAAA,CAAA,UAAM;4BAAC,WAAU;4BAAyB,SAAS;4BAAY,UAAU,CAAC,iBAAiB;;gCACzF,aAAa,2BAA2B;gCAAuB;gCAAE,2BAAa,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;sCAExF,8OAAC,4IAAA,CAAA,UAAM;4BACL,WAAU;4BACV,SAAS,IAAO,aAAa,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,EAAE;sCAE5G,aAAa,EAAE,kBAAkB,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAOlD;uCAEe", "debugId": null}}, {"offset": {"line": 1622, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/job-editor/page.tsx"], "sourcesContent": ["\"use client\";\nimport JobEditor from \"@/components/views/jobRequirement/JobEditor\";\nimport React from \"react\";\n\nconst page = () => {\n  return (\n    <div>\n      <JobEditor />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO;IACX,qBACE,8OAAC;kBACC,cAAA,8OAAC,0JAAA,CAAA,UAAS;;;;;;;;;;AAGhB;uCAEe", "debugId": null}}, {"offset": {"line": 1650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
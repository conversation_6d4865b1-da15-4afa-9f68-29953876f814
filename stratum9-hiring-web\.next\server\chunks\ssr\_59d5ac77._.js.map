{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Checkbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CheckboxProps<T extends FieldValues> extends Omit<InputHTMLAttributes<HTMLInputElement>, \"type\"> {\n  name: Path<T>;\n  control: Control<T>;\n  label?: React.ReactNode;\n  className?: string;\n}\n\nexport default function Checkbox<T extends FieldValues>({ name, control, label, className = \"\", ...props }: CheckboxProps<T>) {\n  return (\n    <div className={`checkbox-wrapper ${className}`}>\n      <Controller\n        name={name}\n        control={control}\n        render={({ field }) => (\n          <label className=\"checkbox-label\">\n            <input type=\"checkbox\" checked={!!field.value} onChange={(e) => field.onChange(e.target.checked)} {...props} aria-label=\"\" />\n            {label && <span className=\"checkbox-text\">{label}</span>}\n          </label>\n        )}\n        defaultValue={false as T[typeof name]}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AASe,SAAS,SAAgC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE,GAAG,OAAyB;IAC1H,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;kBAC7C,cAAA,8OAAC,8JAAA,CAAA,aAAU;YACT,MAAM;YACN,SAAS;YACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,MAAK;4BAAW,SAAS,CAAC,CAAC,MAAM,KAAK;4BAAE,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO;4BAAI,GAAG,KAAK;4BAAE,cAAW;;;;;;wBACvH,uBAAS,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;YAG/C,cAAc;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/InfoIcon.tsx"], "sourcesContent": ["import React, { useId } from \"react\";\nimport { Tooltip } from \"react-tooltip\";\n\ninterface InfoIconProps {\n  tooltip: React.ReactNode;\n  id?: string;\n  place?: \"top\" | \"bottom\" | \"left\" | \"right\";\n  className?: string;\n}\n\nfunction InfoIcon({ tooltip, id, place = \"bottom\", className }: InfoIconProps) {\n  const generatedId = useId();\n  const anchorId = id || `info-icon-${generatedId}`;\n  return (\n    <>\n      <span id={anchorId} className={className}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"15\" height=\"15\" viewBox=\"0 0 23 23\" style={{ cursor: \"pointer\" }} fill=\"none\">\n          <g clipPath=\"url(#clip0_9605_3144)\">\n            <path\n              d=\"M11.5 2.15625C9.65198 2.15625 7.84547 2.70425 6.30889 3.73096C4.77232 4.75766 3.57471 6.21695 2.8675 7.9243C2.1603 9.63165 1.97526 11.5104 2.33579 13.3229C2.69632 15.1354 3.58623 16.8003 4.89298 18.107C6.19972 19.4138 7.86462 20.3037 9.67713 20.6642C11.4896 21.0247 13.3684 20.8397 15.0757 20.1325C16.783 19.4253 18.2423 18.2277 19.269 16.6911C20.2958 15.1545 20.8438 13.348 20.8438 11.5C20.8411 9.02269 19.8559 6.64759 18.1041 4.89586C16.3524 3.14413 13.9773 2.15887 11.5 2.15625ZM11.1406 6.46875C11.3539 6.46875 11.5623 6.53198 11.7396 6.65045C11.9169 6.76891 12.0551 6.93729 12.1367 7.13429C12.2183 7.3313 12.2396 7.54807 12.198 7.75721C12.1564 7.96634 12.0538 8.15845 11.903 8.30922C11.7522 8.46 11.5601 8.56268 11.351 8.60428C11.1418 8.64588 10.925 8.62453 10.728 8.54293C10.531 8.46133 10.3627 8.32315 10.2442 8.14585C10.1257 7.96855 10.0625 7.76011 10.0625 7.54688C10.0625 7.26094 10.1761 6.98671 10.3783 6.78453C10.5805 6.58234 10.8547 6.46875 11.1406 6.46875ZM12.2188 16.5312C11.8375 16.5312 11.4719 16.3798 11.2023 16.1102C10.9327 15.8406 10.7813 15.475 10.7813 15.0938V11.5C10.5906 11.5 10.4078 11.4243 10.273 11.2895C10.1382 11.1547 10.0625 10.9719 10.0625 10.7812C10.0625 10.5906 10.1382 10.4078 10.273 10.273C10.4078 10.1382 10.5906 10.0625 10.7813 10.0625C11.1625 10.0625 11.5281 10.214 11.7977 10.4835C12.0673 10.7531 12.2188 11.1188 12.2188 11.5V15.0938C12.4094 15.0938 12.5922 15.1695 12.727 15.3043C12.8618 15.4391 12.9375 15.6219 12.9375 15.8125C12.9375 16.0031 12.8618 16.1859 12.727 16.3207C12.5922 16.4555 12.4094 16.5312 12.2188 16.5312Z\"\n              fill=\"#436EB6\"\n            />\n          </g>\n          <defs>\n            <clipPath id=\"clip0_9605_3144\">\n              <rect width=\"23\" height=\"23\" fill=\"white\" />\n            </clipPath>\n          </defs>\n        </svg>\n      </span>\n      <Tooltip anchorSelect={`#${anchorId}`} className=\"responsive-tooltip\" place={place}>\n        {tooltip}\n      </Tooltip>\n    </>\n  );\n}\n\nexport default InfoIcon;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAiB;IAC3E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACxB,MAAM,WAAW,MAAM,CAAC,UAAU,EAAE,aAAa;IACjD,qBACE;;0BACE,8OAAC;gBAAK,IAAI;gBAAU,WAAW;0BAC7B,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,OAAO;wBAAE,QAAQ;oBAAU;oBAAG,MAAK;;sCACpH,8OAAC;4BAAE,UAAS;sCACV,cAAA,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;sCAGT,8OAAC;sCACC,cAAA,8OAAC;gCAAS,IAAG;0CACX,cAAA,8OAAC;oCAAK,OAAM;oCAAK,QAAO;oCAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC,oKAAA,CAAA,UAAO;gBAAC,cAAc,CAAC,CAAC,EAAE,UAAU;gBAAE,WAAU;gBAAqB,OAAO;0BAC1E;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Select.tsx"], "sourcesContent": ["import { HTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\nimport Loader from \"../loader/Loader\";\n\ninterface SelectProps<T extends FieldValues> extends HTMLAttributes<HTMLSelectElement> {\n  name: Path<T>;\n  placeholder?: string;\n  disabled?: boolean;\n  control: Control<T>;\n  options: Array<{ label: string; value: string | number }>;\n  isLoading?: boolean;\n}\n\nexport default function Select<T extends FieldValues>({ options, name, control, disabled, placeholder, isLoading, ...props }: SelectProps<T>) {\n  return (\n    <Controller\n      name={name}\n      control={control}\n      render={({ field }) => (\n        <select {...props} disabled={disabled} value={field.value} onChange={field.onChange} aria-label=\"\">\n          <option value=\"\">{placeholder}</option>\n          {isLoading ? (\n            <option value=\"0000\">\n              <Loader />\n            </option>\n          ) : (\n            options.map((data) => (\n              <option key={data.value} value={data.value}>\n                {data.label}\n              </option>\n            ))\n          )}\n        </select>\n      )}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAWe,SAAS,OAA8B,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,OAAuB;IAC1I,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,MAAM;QACN,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gBAAQ,GAAG,KAAK;gBAAE,UAAU;gBAAU,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;kCAC9F,8OAAC;wBAAO,OAAM;kCAAI;;;;;;oBACjB,0BACC,8OAAC;wBAAO,OAAM;kCACZ,cAAA,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;iCAGT,QAAQ,GAAG,CAAC,CAAC,qBACX,8OAAC;4BAAwB,OAAO,KAAK,KAAK;sCACvC,KAAK,KAAK;2BADA,KAAK,KAAK;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { InputHTMLAttributes } from \"react\";\n\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface CommonInputProps extends InputHTMLAttributes<HTMLInputElement> {\n  iconClass?: string;\n  align?: \"left\" | \"right\";\n  children?: React.ReactNode;\n}\n\ninterface TextboxProps<T extends FieldValues> extends CommonInputProps {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textbox<T extends FieldValues>({ children, control, name, iconClass, align, ...props }: TextboxProps<T>) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <Controller\n        control={control}\n        name={name}\n        render={({ field }) => (\n          <input\n            {...props}\n            value={field.value}\n            onChange={(e) => {\n              field.onChange(e);\n              props.onChange?.(e);\n            }}\n            aria-label=\"\"\n          />\n        )}\n        defaultValue={\"\" as T[typeof name]}\n      />\n      {children}\n    </div>\n  );\n}\n\nexport function CommonInput({ iconClass, children, align, onChange, ...props }: CommonInputProps) {\n  return (\n    <div className={`${iconClass} ${align}`}>\n      <input {...props} onChange={onChange} />\n\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAae,SAAS,QAA+B,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAwB;IAC7H,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC,8JAAA,CAAA,aAAU;gBACT,SAAS;gBACT,MAAM;gBACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;wBACE,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK;wBAClB,UAAU,CAAC;4BACT,MAAM,QAAQ,CAAC;4BACf,MAAM,QAAQ,GAAG;wBACnB;wBACA,cAAW;;;;;;gBAGf,cAAc;;;;;;YAEf;;;;;;;AAGP;AAEO,SAAS,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAyB;IAC9F,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,CAAC,EAAE,OAAO;;0BACrC,8OAAC;gBAAO,GAAG,KAAK;gBAAE,UAAU;;;;;;YAE3B;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/jobRequirements/generateJobServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { ExtendedFormValues } from \"@/types/types\";\n\n/**\n * Generates job skills based on the provided form data\n * @param formData Job requirement form data\n * @returns Generated job response\n */\nexport const generateJobSkills = (formData: ExtendedFormValues): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.GENERATE_SKILL, formData);\n};\n\n/**\n * Generates job requirement based on the provided form data\n * @param formData Job requirement form data\n * @returns Generated job response\n */\nexport const generateJobRequirement = (\n  formData: Omit<ExtendedFormValues, \"compliance_statement\"> & { compliance_statement: string }\n): Promise<ApiResponse> => {\n  return http.post(endpoint.jobRequirements.GENERATE_JOB_REQUIREMENT, formData);\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AASO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,cAAc,EAAE;AAC5D;AAOO,MAAM,yBAAyB,CACpC;IAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE;AACtE", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/jobRequirements/pdfUploadService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { AxiosResponse } from \"axios\";\n\n/**\n * Process PDF file to extract form fields using GPT\n *\n * This sends the PDF to the backend which:\n * 1. Parses the PDF content\n * 2. Uses GPT to extract structured job information\n * 3. Returns form field data to pre-fill the job form\n *\n * @param file The PDF file to process\n * @param userId The user ID (optional)\n * @param orgId The organization ID (optional)\n * @returns Promise with extracted form fields and jd_link\n */\nexport const processPdfForFormFields = (file: File): Promise<ApiResponse | AxiosResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  return http.postFile(endpoint.jobRequirements.UPLOAD_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload a file to a pre-signed URL\n *\n * This will be used in the final step when saving the job\n *\n * @param url The pre-signed URL to upload to\n * @param file The file to upload\n * @returns Promise that resolves when the upload is complete\n */\nexport const uploadFileToPredefinedUrl = (url: string, file: File): Promise<Response> => {\n  return fetch(url, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiBO,MAAM,0BAA0B,CAAC;IACtC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAa,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAWO,MAAM,4BAA4B,CAAC,KAAa;IACrD,OAAO,MAAM,KAAK;QAChB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/departmentService.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { IApiResponseCommonInterface } from \"@/interfaces/commonInterfaces\";\nimport * as http from \"@/utils/http\";\n\nexport interface IDepartmentAlter {\n  id: number;\n  name: string;\n  organizationId: number;\n}\n\nexport interface FindDepartmentResponse {\n  id: number;\n  name: string;\n  isDefaultDepartment: boolean;\n}\n\n/**\n * Get all departments associated with an organization\n * @param search Optional search query string\n * @returns Promise with API response\n */\nexport const findDepartments = (search?: string): Promise<IApiResponseCommonInterface<FindDepartmentResponse[]>> => {\n  return http.get<{ search?: string }, IApiResponseCommonInterface<FindDepartmentResponse[]>>(endpoint.departments.GET_DEPARTMENTS, {\n    search,\n  });\n};\n\n/**\n * Add a new department\n * @param departmentData Department data to add\n * @returns Promise with API response\n */\nexport const addDepartment = (departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  return http.post(endpoint.departments.ADD_DEPARTMENT, departmentData);\n};\n\n/**\n * Update an existing department\n * @param departmentId ID of the department to update\n * @param departmentData Updated department data\n * @returns Promise with API response\n */\nexport const updateDepartment = (departmentId: number, departmentData: { name: string }): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  const url = endpoint.departments.UPDATE_DEPARTMENT.replace(\":departmentId\", departmentId.toString());\n\n  return http.put(url, departmentData);\n};\n\n/**\n * Delete a department\n * @param departmentId ID of the department to delete\n * @param organizationId ID of the organization\n * @returns Promise with API response\n */\nexport const deleteDepartment = (departmentId: number): Promise<IApiResponseCommonInterface<IDepartmentAlter>> => {\n  const url = endpoint.departments.DELETE_DEPARTMENT.replace(\":departmentId\", departmentId.toString());\n\n  return http.remove(url);\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;;;AAmBO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAA8E,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,eAAe,EAAE;QAChI;IACF;AACF;AAOO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,cAAc,EAAE;AACxD;AAQO,MAAM,mBAAmB,CAAC,cAAsB;IACrD,MAAM,MAAM,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,aAAa,QAAQ;IAEjG,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,KAAK;AACvB;AAQO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,MAAM,4HAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,aAAa,QAAQ;IAEjG,OAAO,CAAA,GAAA,oHAAA,CAAA,SAAW,AAAD,EAAE;AACrB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/jobRequirementsValidations.ts"], "sourcesContent": ["/**\n * Job Requirements Validation Schema using Yup\n */\n\nimport * as yup from \"yup\";\n\n// Type for the form data based on Yup schema\nexport type GenerateJobSchema = yup.InferType<ReturnType<typeof generateJobValidation>>;\n\n/**\n * Generate job requirements validation schema with translations\n * @param translation - Function to translate error message keys\n * @returns Yup validation schema for job requirements\n */\nexport const generateJobValidation = (translation: (key: string) => string) =>\n  yup.object().shape({\n    title: yup\n      .string()\n      .trim()\n      .required(translation(\"title_required\"))\n      .min(3, translation(\"title_min\"))\n      .max(50, translation(\"title_max\"))\n      .matches(/^[A-Za-z\\s\\/&,.'()+#\\-:]+$/, translation(\"title_alpha_only\")),\n    employment_type: yup.string().trim().required(translation(\"employment_type_required\")),\n    department_id: yup.string().trim().required(translation(\"department_id_required\")),\n    salary_range: yup\n      .string()\n      .trim()\n      .required(translation(\"salary_range_required\"))\n      .test(\"is-valid-salary-range\", translation(\"salary_range_format\"), (value) => {\n        if (!value) return false;\n        const match = value.match(/^\\$([0-9,]+) \\- \\$([0-9,]+)$/);\n        if (!match) return false;\n\n        const min = parseInt(match[1].replace(/,/g, \"\"), 10);\n        const max = parseInt(match[2].replace(/,/g, \"\"), 10);\n\n        return min > 0 && max > 0 && max > min;\n      }),\n    salary_cycle: yup.string().trim().required(translation(\"salary_cycle_required\")),\n    location_type: yup.string().trim().required(translation(\"location_type_required\")),\n    state: yup\n      .string()\n      .trim()\n      .required(translation(\"state_required\"))\n      .min(3, translation(\"state_min\"))\n      .max(50, translation(\"state_max\"))\n      .matches(/^[A-Za-z\\s]+$/, translation(\"state_alpha_only\")),\n    city: yup\n      .string()\n      .trim()\n      .required(translation(\"city_required\"))\n      .min(3, translation(\"city_min\"))\n      .max(50, translation(\"city_max\"))\n      .matches(/^[A-Za-z\\s]+$/, translation(\"city_alpha_only\")),\n    role_overview: yup\n      .string()\n      .trim()\n      .required(translation(\"role_overview_required\"))\n      .min(3, translation(\"role_overview_min\"))\n      .max(150, translation(\"role_overview_max\")),\n    experience_level: yup.string().trim().required(translation(\"experience_level_required\")),\n    responsibilities: yup\n      .string()\n      .trim()\n      .required(translation(\"responsibilities_required\"))\n      .min(3, translation(\"responsibilities_min\"))\n      .max(150, translation(\"responsibilities_max\")),\n    educations_requirement: yup\n      .string()\n      .trim()\n      .required(translation(\"education_required\"))\n      .min(3, translation(\"education_min\"))\n      .max(150, translation(\"education_max\")),\n    certifications: yup.string().trim().optional().max(150, translation(\"certifications_max\")),\n    skills_and_software_expertise: yup\n      .string()\n      .trim()\n      .required(translation(\"skills_required\"))\n      .min(3, translation(\"skills_min\"))\n      .max(150, translation(\"skills_max\")),\n    experience_required: yup\n      .string()\n      .trim()\n      .required(translation(\"experience_required_required\"))\n      .matches(/^[0-9]+(\\.[0-9]+)?$/, translation(\"experience_must_be_number\"))\n      .test(\"min-value\", translation(\"experience_min_value\"), (value) => {\n        const numValue = parseFloat(value);\n        return numValue > 0;\n      })\n      .test(\"max-value\", translation(\"experience_max_value\"), (value) => {\n        const numValue = parseFloat(value);\n        return numValue <= 50;\n      })\n      .test(\"max-length\", translation(\"experience_max_length\"), (value) => value.length <= 4),\n    ideal_candidate_traits: yup\n      .string()\n      .trim()\n      .required(translation(\"traits_required\"))\n      .min(3, translation(\"traits_min\"))\n      .max(150, translation(\"traits_max\")),\n    about_company: yup\n      .string()\n      .trim()\n      .required(translation(\"company_required\"))\n      .min(3, translation(\"company_min\"))\n      .max(150, translation(\"company_max\")),\n    perks_benefits: yup.string().trim().optional().max(150, translation(\"perks_max\")),\n    tone_style: yup.string().trim().required(translation(\"tone_required\")),\n    additional_info: yup.string().trim().optional().max(150, translation(\"additional_max\")),\n    compliance_statement: yup\n      .array()\n      .of(yup.string())\n      .required(translation(\"compliance_required\"))\n      .min(1, translation(\"compliance_min\"))\n      .typeError(translation(\"compliance_type_error\")),\n    show_compliance: yup.boolean().oneOf([true], translation(\"show_compliance_required\")).required(translation(\"show_compliance_required\")),\n  });\n\nexport default generateJobValidation;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AAUO,MAAM,wBAAwB,CAAC,cACpC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,OAAO,CAAA,GAAA,mIAAA,CAAA,SACE,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,mBACrB,GAAG,CAAC,GAAG,YAAY,cACnB,GAAG,CAAC,IAAI,YAAY,cACpB,OAAO,CAAC,8BAA8B,YAAY;QACrD,iBAAiB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QAC1D,eAAe,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QACxD,cAAc,CAAA,GAAA,mIAAA,CAAA,SACL,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,0BACrB,IAAI,CAAC,yBAAyB,YAAY,wBAAwB,CAAC;YAClE,IAAI,CAAC,OAAO,OAAO;YACnB,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK;YACjD,MAAM,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK;YAEjD,OAAO,MAAM,KAAK,MAAM,KAAK,MAAM;QACrC;QACF,cAAc,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QACvD,eAAe,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QACxD,OAAO,CAAA,GAAA,mIAAA,CAAA,SACE,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,mBACrB,GAAG,CAAC,GAAG,YAAY,cACnB,GAAG,CAAC,IAAI,YAAY,cACpB,OAAO,CAAC,iBAAiB,YAAY;QACxC,MAAM,CAAA,GAAA,mIAAA,CAAA,SACG,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,kBACrB,GAAG,CAAC,GAAG,YAAY,aACnB,GAAG,CAAC,IAAI,YAAY,aACpB,OAAO,CAAC,iBAAiB,YAAY;QACxC,eAAe,CAAA,GAAA,mIAAA,CAAA,SACN,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,2BACrB,GAAG,CAAC,GAAG,YAAY,sBACnB,GAAG,CAAC,KAAK,YAAY;QACxB,kBAAkB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QAC3D,kBAAkB,CAAA,GAAA,mIAAA,CAAA,SACT,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,8BACrB,GAAG,CAAC,GAAG,YAAY,yBACnB,GAAG,CAAC,KAAK,YAAY;QACxB,wBAAwB,CAAA,GAAA,mIAAA,CAAA,SACf,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,uBACrB,GAAG,CAAC,GAAG,YAAY,kBACnB,GAAG,CAAC,KAAK,YAAY;QACxB,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,YAAY;QACpE,+BAA+B,CAAA,GAAA,mIAAA,CAAA,SACtB,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,oBACrB,GAAG,CAAC,GAAG,YAAY,eACnB,GAAG,CAAC,KAAK,YAAY;QACxB,qBAAqB,CAAA,GAAA,mIAAA,CAAA,SACZ,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,iCACrB,OAAO,CAAC,uBAAuB,YAAY,8BAC3C,IAAI,CAAC,aAAa,YAAY,yBAAyB,CAAC;YACvD,MAAM,WAAW,WAAW;YAC5B,OAAO,WAAW;QACpB,GACC,IAAI,CAAC,aAAa,YAAY,yBAAyB,CAAC;YACvD,MAAM,WAAW,WAAW;YAC5B,OAAO,YAAY;QACrB,GACC,IAAI,CAAC,cAAc,YAAY,0BAA0B,CAAC,QAAU,MAAM,MAAM,IAAI;QACvF,wBAAwB,CAAA,GAAA,mIAAA,CAAA,SACf,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,oBACrB,GAAG,CAAC,GAAG,YAAY,eACnB,GAAG,CAAC,KAAK,YAAY;QACxB,eAAe,CAAA,GAAA,mIAAA,CAAA,SACN,AAAD,IACL,IAAI,GACJ,QAAQ,CAAC,YAAY,qBACrB,GAAG,CAAC,GAAG,YAAY,gBACnB,GAAG,CAAC,KAAK,YAAY;QACxB,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,YAAY;QACpE,YAAY,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,YAAY;QACrD,iBAAiB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,YAAY;QACrE,sBAAsB,CAAA,GAAA,mIAAA,CAAA,QACd,AAAD,IACJ,EAAE,CAAC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,KACZ,QAAQ,CAAC,YAAY,wBACrB,GAAG,CAAC,GAAG,YAAY,mBACnB,SAAS,CAAC,YAAY;QACzB,iBAAiB,CAAA,GAAA,mIAAA,CAAA,UAAW,AAAD,IAAI,KAAK,CAAC;YAAC;SAAK,EAAE,YAAY,6BAA6B,QAAQ,CAAC,YAAY;IAC7G;uCAEa", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/interview-form-icon.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 687, height: 269, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gJAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/UploadDocumentIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype UploadDocumentIconProps = {\n  className?: string;\n};\n\nfunction UploadDocumentIcon({ className }: UploadDocumentIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"52\" height=\"52\" viewBox=\"0 0 52 52\" fill=\"none\" className={className}>\n      <g opacity=\"0.7\" clipPath=\"url(#clip0_9593_10462)\">\n        <path d=\"M32.5 17.332H32.5206\" stroke=\"#333333\" strokeWidth=\"2.6\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n        <path\n          d=\"M6.5 13C6.5 11.2761 7.18482 9.62279 8.40381 8.40381C9.62279 7.18482 11.2761 6.5 13 6.5H39C40.7239 6.5 42.3772 7.18482 43.5962 8.40381C44.8152 9.62279 45.5 11.2761 45.5 13V39C45.5 40.7239 44.8152 42.3772 43.5962 43.5962C42.3772 44.8152 40.7239 45.5 39 45.5H13C11.2761 45.5 9.62279 44.8152 8.40381 43.5962C7.18482 42.3772 6.5 40.7239 6.5 39V13Z\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M6.5 34.6673L17.3333 23.8339C19.344 21.8991 21.8227 21.8991 23.8333 23.8339L34.6667 34.6673\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M30.3281 30.3326L32.4948 28.166C34.5055 26.2311 36.9841 26.2311 38.9948 28.166L45.4948 34.666\"\n          stroke=\"#333333\"\n          strokeWidth=\"1.95\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_9593_10462\">\n          <rect width=\"52\" height=\"52\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default UploadDocumentIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAA2B;IAChE,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAE,SAAQ;gBAAM,UAAS;;kCACxB,8OAAC;wBAAK,GAAE;wBAAuB,QAAO;wBAAU,aAAY;wBAAM,eAAc;wBAAQ,gBAAe;;;;;;kCACvG,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/UploadBox.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport UploadDocumentIcon from \"../svgComponents/UploadDocumentIcon\";\nimport { useTranslations } from \"next-intl\";\n\ninterface UploadBoxProps {\n  UploadBoxClassName?: string;\n  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  inputRef?: React.RefObject<HTMLInputElement | null>;\n  isLoading?: boolean;\n  uploadingMessages?: string[]; // Optional array of dynamic messages\n  messageInterval?: number; // Optional interval for message rotation (default: 2000ms)\n}\n\nconst UploadBox = ({ UploadBoxClassName, onChange, inputRef, isLoading, uploadingMessages, messageInterval = 2000 }: UploadBoxProps) => {\n  const t = useTranslations();\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\n\n  // Dynamic message rotation effect\n  useEffect(() => {\n    if (!isLoading || !uploadingMessages || uploadingMessages.length <= 1) {\n      return;\n    }\n\n    const interval = setInterval(() => {\n      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % uploadingMessages.length);\n    }, messageInterval);\n\n    return () => clearInterval(interval);\n  }, [isLoading, uploadingMessages, messageInterval]);\n\n  // Reset message index when loading starts\n  useEffect(() => {\n    if (isLoading) {\n      setCurrentMessageIndex(0);\n    }\n  }, [isLoading]);\n\n  // Determine what message to show during loading\n  const getLoadingMessage = () => {\n    if (uploadingMessages && uploadingMessages.length > 0) {\n      return uploadingMessages[currentMessageIndex];\n    }\n    return t(\"uploading\");\n  };\n\n  return (\n    <div className={`upload-card ${UploadBoxClassName}`}>\n      <input type=\"file\" accept=\".pdf\" onChange={onChange} disabled={isLoading} ref={inputRef} />\n      <div className=\"upload-box-inner\">\n        <UploadDocumentIcon />\n        {!isLoading ? (\n          <p>\n            {t(\"upload_doc\")}\n            <br />\n            {t(\"max_file_size\")}\n          </p>\n        ) : (\n          <p className=\"uploading-message\">{getLoadingMessage()}</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UploadBox;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAWA,MAAM,YAAY,CAAC,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,IAAI,EAAkB;IACjI,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,qBAAqB,kBAAkB,MAAM,IAAI,GAAG;YACrE;QACF;QAEA,MAAM,WAAW,YAAY;YAC3B,uBAAuB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,kBAAkB,MAAM;QAClF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAW;QAAmB;KAAgB;IAElD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,uBAAuB;QACzB;IACF,GAAG;QAAC;KAAU;IAEd,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;YACrD,OAAO,iBAAiB,CAAC,oBAAoB;QAC/C;QACA,OAAO,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,oBAAoB;;0BACjD,8OAAC;gBAAM,MAAK;gBAAO,QAAO;gBAAO,UAAU;gBAAU,UAAU;gBAAW,KAAK;;;;;;0BAC/E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yJAAA,CAAA,UAAkB;;;;;oBAClB,CAAC,0BACA,8OAAC;;4BACE,EAAE;0CACH,8OAAC;;;;;4BACA,EAAE;;;;;;6CAGL,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/AiMarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction AiMarkIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"25\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M13.294 7.43666L14.097 9.66666C14.989 12.1417 16.938 14.0907 19.413 14.9827L21.643 15.7857C21.844 15.8587 21.844 16.1437 21.643 16.2157L19.413 17.0187C16.938 17.9107 14.989 19.8597 14.097 22.3347L13.294 24.5647C13.221 24.7657 12.936 24.7657 12.864 24.5647L12.061 22.3347C11.169 19.8597 9.22001 17.9107 6.74501 17.0187L4.51501 16.2157C4.31401 16.1427 4.31401 15.8577 4.51501 15.7857L6.74501 14.9827C9.22001 14.0907 11.169 12.1417 12.061 9.66666L12.864 7.43666C12.936 7.23466 13.221 7.23466 13.294 7.43666Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 2.07725L23.7391 3.20625C24.1911 4.45925 25.1781 5.44625 26.4311 5.89825L27.5601 6.30525C27.6621 6.34225 27.6621 6.48625 27.5601 6.52325L26.4311 6.93025C25.1781 7.38225 24.1911 8.36925 23.7391 9.62225L23.3321 10.7513C23.2951 10.8533 23.1511 10.8533 23.1141 10.7513L22.7071 9.62225C22.2551 8.36925 21.2681 7.38225 20.0151 6.93025L18.8861 6.52325C18.7841 6.48625 18.7841 6.34225 18.8861 6.30525L20.0151 5.89825C21.2681 5.44625 22.2551 4.45925 22.7071 3.20625L23.1141 2.07725C23.1511 1.97425 23.2961 1.97425 23.3321 2.07725Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 21.2484L23.7391 22.3774C24.1911 23.6304 25.1781 24.6174 26.4311 25.0694L27.5601 25.4764C27.6621 25.5134 27.6621 25.6574 27.5601 25.6944L26.4311 26.1014C25.1781 26.5534 24.1911 27.5404 23.7391 28.7934L23.3321 29.9224C23.2951 30.0244 23.1511 30.0244 23.1141 29.9224L22.7071 28.7934C22.2551 27.5404 21.2681 26.5534 20.0151 26.1014L18.8861 25.6944C18.7841 25.6574 18.7841 25.5134 18.8861 25.4764L20.0151 25.0694C21.2681 24.6174 22.2551 23.6304 22.7071 22.3774L23.1141 21.2484C23.1511 21.1464 23.2961 21.1464 23.3321 21.2484Z\"\n        fill=\"black\"\n      />\n    </svg>\n  );\n}\n\nexport default AiMarkIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/jobRequirement/Generatejob.tsx"], "sourcesContent": ["\"use client\";\n\n// Internal libraries\nimport { useEffect, useRef, useState } from \"react\";\n\nimport Image from \"next/image\";\nimport ReactSelect from \"react-select\";\n\nimport { useTranslations } from \"next-intl\";\nimport { useDispatch } from \"react-redux\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\nimport { Controller, Resolver, SubmitHandler, useForm } from \"react-hook-form\";\nimport { redirect, useSearchParams, useRouter } from \"next/navigation\";\n\n// Components\nimport Loader from \"@/components/loader/Loader\";\nimport Button from \"@/components/formElements/Button\";\nimport Checkbox from \"@/components/formElements/Checkbox\";\nimport InfoIcon from \"@/components/svgComponents/InfoIcon\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport Select from \"@/components/formElements/Select\";\nimport Textbox from \"@/components/formElements/Textbox\";\nimport Textarea from \"@/components/formElements/Textarea\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\n\n// Services\nimport { generateJobSkills } from \"@/services/jobRequirements/generateJobServices\";\nimport { processPdfForFormFields } from \"@/services/jobRequirements/pdfUploadService\";\n\n// Redux, constants, interfaces, types, validations\nimport { IDepartment, ISkillData } from \"@/interfaces/jobRequirementesInterfaces\";\nimport { setJobDetails } from \"@/redux/slices/jobDetailsSlice\";\nimport { setSkillsData } from \"@/redux/slices/jobSkillsSlice\";\n\nimport { findDepartments } from \"@/services/departmentService\";\nimport { ExtendedFormValues, FormValues } from \"@/types/types\";\n\nimport generateJobValidation, { GenerateJobSchema } from \"@/validations/jobRequirementsValidations\";\nimport { toastMessageError, toastMessageSuccess } from \"@/utils/helper\";\nimport { initialState } from \"@/constants/commonConstants\";\n\nimport {\n  CATEGORY_OPTION,\n  COMPLIANCE_OPTIONS,\n  CURRENCY_SYMBOL,\n  FILE_SIZE_LIMIT,\n  FILE_TYPE,\n  LOCATION_TYPE_OPTIONS,\n  SALARY_REMOVE_SYMBOL_REGEX,\n  SALARY_CYCLE_OPTIONS,\n  TONE_STYLE_OPTIONS,\n  HIRING_TYPE,\n  EXPERIENCE_LEVEL_OPTIONS,\n  HIRING_TYPE_KEY,\n  FILE_NAME,\n  JOB_GENERATION_UPLOAD_MESSAGES,\n} from \"@/constants/jobRequirementConstant\";\n\n// Assets\nimport interviewFormIcon from \"@/../public/assets/images/interview-form-icon.svg\";\n\n// CSS\nimport \"react-tooltip/dist/react-tooltip.css\";\nimport style from \"@/styles/commonPage.module.scss\";\nimport ROUTES from \"@/constants/routes\";\nimport UploadBox from \"@/components/commonComponent/UploadBox\";\nimport AiMarkIcon from \"@/components/svgComponents/AiMarkIcon\";\n\nfunction Generatejob() {\n  // Form submission state\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadError, setUploadError] = useState(\"\");\n  console.log(\"uploadError===============================>\", uploadError);\n\n  // File upload state\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [jdLink, setJdLink] = useState(\"\");\n  const [fileToProcess, setFileToProcess] = useState<File | null>(null);\n  const [uploadBoxKey, setUploadBoxKey] = useState(0); // Key to force re-render of UploadBox\n\n  // Department Data state\n  const [departmentsOptions, setDepartmentsOptions] = useState<IDepartment[]>([]);\n\n  // Refs\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  // Refs (useRef) – Lifecycle or DOM Tracking\n  const initialFetchDone = useRef(false);\n\n  // React Router hooks\n  const searchParams = useSearchParams();\n  const hiringType = searchParams?.get(HIRING_TYPE_KEY) || \"\";\n\n  // Third-party/hooks library hooks\n  const t = useTranslations();\n  const tGenerate = useTranslations(\"jobRequirement\");\n  const router = useRouter();\n  const dispatch = useDispatch();\n\n  const [borderDangerFields, setBorderDangerFields] = useState({\n    job_title: false,\n    state: false,\n    city: false,\n    job_type: false,\n    location_type: false,\n    job_description: false,\n    responsibilities: false,\n    requirements: false,\n    education_required: false,\n    certifications: false,\n    skills_required: false,\n    benefits: false,\n    tone_style: false,\n    compliance_statement: false,\n    salary_range: false,\n    salary_cycle: false,\n    experience_level: false,\n    experience_required: false,\n    ideal_candidate_traits: false,\n    about_company: false,\n    additional_info: false,\n    department_id: false,\n  });\n\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    reset,\n    setError,\n  } = useForm<FormValues>({\n    defaultValues: {\n      ...initialState,\n    },\n    resolver: yupResolver(generateJobValidation(t)) as unknown as Resolver<FormValues>,\n    mode: \"onSubmit\",\n  });\n\n  // Helper function to clear file input\n  const clearFileInput = () => {\n    setSelectedFile(null);\n    setUploadBoxKey((prev) => prev + 1); // Force re-render of UploadBox\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n  };\n\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\n    setUploadError(\"\");\n    const file = e.target.files?.[0];\n    if (!file) {\n      return;\n    }\n\n    if (!file.name.includes(FILE_NAME)) {\n      toastMessageError(t(\"unsupported_file_type\"));\n      clearFileInput();\n      return;\n    }\n    // Validate file type (PDF only)\n    if (!FILE_TYPE.includes(file.type)) {\n      toastMessageError(t(\"pdf_only\"));\n      clearFileInput();\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > FILE_SIZE_LIMIT) {\n      console.log(\"file size not pdf===============================>\", file.size);\n      toastMessageError(t(\"pdf_size\"));\n      clearFileInput();\n      return;\n    }\n\n    setSelectedFile(file);\n    console.log(\"file after setSelectedFile===============================>\", file);\n    // Automatically upload the file after selection\n    await handleFileUpload(file);\n  };\n\n  const handleFileUpload = async (fileToUpload?: File) => {\n    const fileToProcess = fileToUpload || selectedFile;\n\n    if (!fileToProcess) {\n      setUploadError(t(\"pdf_select\"));\n      return;\n    }\n\n    try {\n      setIsUploading(true);\n      setUploadError(\"\");\n      setJdLink(\"\");\n\n      // Process the PDF to extract form fields using GPT\n      const response = await processPdfForFormFields(fileToProcess);\n      console.log(\"response===============================>\", response);\n      if (!response.data) {\n        toastMessageError(response.data.message);\n        return;\n      }\n\n      // Safely access data properties with fallback values\n      const responseData = response.data.data || {};\n      const formFields = responseData.formFields || {};\n      const jd_link = responseData.jd_link ? responseData.jd_link.split(\"?\")?.[0] : \"\";\n      // Store the JD link for later use when saving the job\n      setJdLink(jd_link);\n      setFileToProcess(fileToProcess);\n      clearFileInput(); // Clear the file input after successful processing\n\n      if (!formFields) {\n        throw new Error(t(\"pdf_extract\"));\n      }\n\n      if (formFields.department_id) {\n        setValue(\"department_id\", formFields.department_id);\n        setError(\"department_id\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, department_id: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, department_id: true }));\n      }\n      // Auto-fill form fields with extracted data based on exact form field names\n      // Handle basic text fields\n      if (formFields.job_title) {\n        setValue(\"title\", formFields.job_title);\n        setError(\"title\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, job_title: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, job_title: true }));\n      }\n\n      if (formFields.job_description) {\n        setValue(\"role_overview\", formFields.job_description);\n        setError(\"role_overview\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, job_description: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, job_description: true }));\n      }\n      if (formFields.state) {\n        setValue(\"state\", formFields.state);\n        setError(\"state\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, state: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, state: true }));\n      }\n      if (formFields.city) {\n        setValue(\"city\", formFields.city);\n        setError(\"city\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, city: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, city: true }));\n      }\n      if (formFields.experience_level) {\n        setValue(\"experience_level\", formFields.experience_level);\n        setError(\"experience_level\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, experience_level: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, experience_level: true }));\n      }\n\n      if (Array.isArray(formFields.compliance_statement) && formFields.compliance_statement.length > 0) {\n        // First enable the compliance section\n        setValue(\"show_compliance\", true);\n\n        // Validate that the values exist in our options and set the field value to string array\n        const validValues = formFields.compliance_statement.filter((value: string) => COMPLIANCE_OPTIONS.some((option) => option.value === value));\n\n        setValue(\"compliance_statement\", validValues);\n        setError(\"compliance_statement\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, compliance_statement: false }));\n      } else {\n        setValue(\"compliance_statement\", []);\n        setError(\"compliance_statement\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, compliance_statement: true }));\n      }\n\n      // Handle select fields (need to match with option values)\n      if (formFields.job_type) {\n        // Map job_type directly to employment_type since we're using standardized values\n        // Convert to appropriate format based on your form's requirements\n        if (formFields.job_type.toLowerCase().includes(\"full\")) {\n          setValue(\"employment_type\", \"full_time\");\n          setError(\"employment_type\", { type: \"manual\", message: \"\" });\n        } else if (formFields.job_type.toLowerCase().includes(\"part\")) {\n          setValue(\"employment_type\", \"part_time\");\n          setError(\"employment_type\", { type: \"manual\", message: \"\" });\n        } else if (formFields.job_type.toLowerCase().includes(\"contract\")) {\n          setValue(\"employment_type\", \"contract\");\n          setError(\"employment_type\", { type: \"manual\", message: \"\" });\n        } else if (formFields.job_type.toLowerCase().includes(\"intern\")) {\n          setValue(\"employment_type\", \"internship\");\n          setError(\"employment_type\", { type: \"manual\", message: \"\" });\n        } else if (formFields.job_type.toLowerCase().includes(\"free\")) {\n          setValue(\"employment_type\", \"freelance\");\n          setError(\"employment_type\", { type: \"manual\", message: \"\" });\n        }\n        setBorderDangerFields((prev) => ({ ...prev, job_type: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, job_type: true }));\n      }\n      if (formFields.location_type) {\n        const matchedLocationType = LOCATION_TYPE_OPTIONS.find((option) => option.value.toLowerCase() === formFields.location_type?.toLowerCase());\n        if (matchedLocationType) {\n          setValue(\"location_type\", matchedLocationType.value);\n          setError(\"location_type\", { type: \"manual\", message: \"\" });\n        }\n        setBorderDangerFields((prev) => ({ ...prev, location_type: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, location_type: true }));\n      }\n\n      if (formFields.salary_cycle) {\n        const matchedSalaryCycle = SALARY_CYCLE_OPTIONS.find((option) => option.value.toLowerCase() === formFields.salary_cycle?.toLowerCase());\n        if (matchedSalaryCycle) {\n          setValue(\"salary_cycle\", matchedSalaryCycle.value);\n          setError(\"salary_cycle\", { type: \"manual\", message: \"\" });\n        }\n        setBorderDangerFields((prev) => ({ ...prev, salary_cycle: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, salary_cycle: true }));\n      }\n\n      // Handle salary range if present\n      if (formFields.salary_range) {\n        setValue(\"salary_range\", formFields.salary_range);\n        setError(\"salary_range\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, salary_range: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, salary_range: true }));\n      }\n\n      // handle\n      if (formFields.responsibilities) {\n        setValue(\"responsibilities\", formFields.responsibilities);\n        setError(\"responsibilities\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, responsibilities: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, responsibilities: true }));\n      }\n\n      if (formFields.education_required) {\n        // Map requirements to educations_requirement\n        setValue(\"educations_requirement\", formFields.education_required);\n        setError(\"educations_requirement\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, education_required: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, requirements: true }));\n      }\n\n      if (formFields.skills_required) {\n        // Map skills_required to skills_and_software_expertise\n        setValue(\"skills_and_software_expertise\", formFields.skills_required);\n        setError(\"skills_and_software_expertise\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, skills_required: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, skills_required: true }));\n      }\n\n      // Uncomment and adjust the following lines based on your exact form field names for benefits\n      if (formFields.benefits) {\n        setValue(\"perks_benefits\", formFields.benefits);\n        setError(\"perks_benefits\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, benefits: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, benefits: true }));\n      }\n\n      // Handle tone style if present\n      if (formFields.tone_style) {\n        setValue(\"tone_style\", formFields.tone_style);\n        setError(\"tone_style\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, tone_style: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, tone_style: true }));\n      }\n\n      // Store experience data in a variable to use later\n      if (formFields.experience_required) {\n        setValue(\"experience_required\", formFields.experience_required);\n        setError(\"experience_required\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, experience_required: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, experience_required: true }));\n      }\n\n      if (formFields.experience_level) {\n        const matchedExperienceLevel = EXPERIENCE_LEVEL_OPTIONS.find(\n          (option) => option.value.toLowerCase() === formFields.experience_level?.toLowerCase()\n        );\n        if (matchedExperienceLevel) {\n          setValue(\"experience_level\", matchedExperienceLevel.value);\n          setError(\"experience_level\", { type: \"manual\", message: \"\" });\n        }\n        setBorderDangerFields((prev) => ({ ...prev, experience_level: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, experience_level: true }));\n      }\n      if (formFields.candidate_traits) {\n        setValue(\"ideal_candidate_traits\", formFields.candidate_traits);\n        setError(\"ideal_candidate_traits\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, ideal_candidate_traits: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, ideal_candidate_traits: true }));\n      }\n      if (formFields.about_company) {\n        setValue(\"about_company\", formFields.about_company);\n        setError(\"about_company\", { type: \"manual\", message: \"\" });\n        setBorderDangerFields((prev) => ({ ...prev, about_company: false }));\n      } else {\n        setBorderDangerFields((prev) => ({ ...prev, about_company: true }));\n      }\n      if (formFields.certifications) {\n        setValue(\"certifications\", formFields.certifications);\n      }\n      if (formFields.additional_info) {\n        setValue(\"additional_info\", formFields.additional_info);\n      }\n    } catch (error: unknown) {\n      console.error(\"Error processing PDF:\", error);\n      toastMessageError(t(\"pdf_error\"));\n      clearFileInput(); // Clear the file input on error\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const onSubmit: SubmitHandler<GenerateJobSchema> = async (data) => {\n    try {\n      setIsSubmitting(true);\n      if (jdLink && fileToProcess) {\n        const contentType = fileToProcess.type;\n        const myHeaders = new Headers({\n          \"Content-Type\": contentType,\n        });\n        await fetch(jdLink, {\n          method: \"PUT\",\n          headers: myHeaders,\n          body: fileToProcess,\n        });\n      }\n      const formDataForSubmission: ExtendedFormValues = {\n        ...data,\n        compliance_statement: (data.compliance_statement || []).filter((item): item is string => typeof item === \"string\"),\n        // Include the jd_link field if it exists\n        jd_link: jdLink || undefined,\n        hiring_type: hiringType || \"\",\n      };\n      // console.log(\"Form data for submission:\", data);\n      // return\n      const generateJobResult = await generateJobSkills(formDataForSubmission);\n      if (generateJobResult && generateJobResult?.data && generateJobResult.data?.success) {\n        toastMessageSuccess(t(generateJobResult.data.message as string));\n\n        // Type assertion for the response data\n        const responseData = generateJobResult.data?.data as {\n          careerSkills: ISkillData[];\n          roleSpecificSkills: ISkillData[];\n          cultureSpecificSkills: ISkillData[];\n        };\n\n        // Dispatch job skills data to Redux store\n        dispatch(\n          setSkillsData({\n            careerSkills: responseData.careerSkills,\n            roleSpecificSkills: responseData.roleSpecificSkills,\n            cultureSpecificSkills: responseData.cultureSpecificSkills,\n          })\n        );\n\n        // Dispatch job details to Redux store before API call\n        // We store original form data with proper type handling\n        dispatch(\n          setJobDetails({\n            ...formDataForSubmission,\n          })\n        );\n\n        // Redirect to career skills page\n        router.push(ROUTES.JOBS.CAREER_BASED_SKILLS);\n      } else {\n        toastMessageError(t(generateJobResult.data?.message as string) || t(\"something_went_wrong\"));\n      }\n\n      // Reset form if needed or redirect to another page\n      setIsSubmitting(false);\n    } catch (error) {\n      console.error(t(\"form_submission_error\"), error);\n      toastMessageError(t(\"something_went_wrong\"));\n      setIsSubmitting(false);\n    }\n  };\n\n  useEffect(() => {\n    if (!hiringType || (hiringType !== HIRING_TYPE.EXTERNAL && hiringType !== HIRING_TYPE.INTERNAL)) redirect(ROUTES.DASHBOARD);\n  }, [hiringType]);\n\n  useEffect(() => {\n    const fetchDepartments = async () => {\n      try {\n        const response = await findDepartments();\n        if (response && response.data && response.data.success) {\n          const formattedData = response.data.data.map((item) => ({\n            value: item.id,\n            label: item.name,\n          }));\n          setDepartmentsOptions(formattedData);\n        }\n      } catch (error) {\n        console.error(\"Error fetching departments:\", error);\n      }\n    };\n    if (initialFetchDone.current) return;\n    initialFetchDone.current = true;\n    fetchDepartments();\n  }, []);\n\n  return (\n    <div className={style.job_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                <BackArrowIcon onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)} />\n                Generate Job Requirements <span>Through S9 InnerView</span>\n              </h2>\n            </div>\n          </div>\n        </div>\n        <div className=\"job-generate-head\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <div className=\"job-generate-doc-card\">\n                <h3 className=\"sub-tittle mt-0 mb-2 d-flex align-items-center\">\n                  <AiMarkIcon className=\"me-2 p-1\" /> Create Job Description With AI\n                </h3>\n                <p className=\"mb-4\">Effortlessly autofill your interview details by uploading your JD here.</p>\n                <InputWrapper className=\"mb-0\">\n                  <UploadBox\n                    key={uploadBoxKey}\n                    UploadBoxClassName=\"upload-card-sm\"\n                    onChange={handleFileChange}\n                    inputRef={fileInputRef}\n                    isLoading={isUploading}\n                    uploadingMessages={JOB_GENERATION_UPLOAD_MESSAGES}\n                    messageInterval={2000} // Change message every 1.8 seconds\n                  />\n                </InputWrapper>\n              </div>\n            </div>\n            <div className=\"col-md-6\">\n              <Image src={interviewFormIcon} alt=\"interviewFormIcon\" className={style.interview_form_icon} />\n            </div>\n          </div>\n        </div>\n        <form onSubmit={handleSubmit(onSubmit)}>\n          <div className=\"row mt-5\">\n            <h2 className={style.section_heading}>\n              {tGenerate(\"basic_details\")}{\" \"}\n              <InfoIcon tooltip=\"Basic job title, location, type, and salary details.\" id=\"BasicDetails\" place=\"right\" />\n            </h2>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"title\" required>\n                  {tGenerate(\"job_title\")}\n                </InputWrapper.Label>\n                <Textbox\n                  className={`${borderDangerFields.job_title ? \"border-danger\" : \"\"} form-control`}\n                  control={control}\n                  name=\"title\"\n                  type=\"text\"\n                  placeholder={tGenerate(\"eneter_job_title\")}\n                ></Textbox>\n                {errors.title && <InputWrapper.Error message={errors.title.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"employment_type\" required>\n                  {tGenerate(\"employment_type\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Select\n                    control={control}\n                    name=\"employment_type\"\n                    options={CATEGORY_OPTION}\n                    className={`${borderDangerFields.job_type ? \"border-danger\" : \"\"} w-100`}\n                    placeholder={tGenerate(\"select_employment_type\")}\n                  />\n                </div>\n                {errors.employment_type && <InputWrapper.Error message={errors.employment_type.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"department_id\" required>\n                  {tGenerate(\"depeartment\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Select\n                    control={control}\n                    name=\"department_id\"\n                    options={departmentsOptions}\n                    className={`w-100 ${borderDangerFields.department_id ? \"border-danger\" : \"\"}`}\n                    placeholder=\"Select Department\"\n                  />\n                </div>\n                {errors.department_id && <InputWrapper.Error message={errors.department_id.message as string} />}\n              </InputWrapper>\n            </div>\n\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"salary_range\" required>\n                  {tGenerate(\"salary_range\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Textbox\n                    className={`form-control ${borderDangerFields.salary_range ? \"border-danger\" : \"\"}`}\n                    control={control}\n                    name=\"salary_range\"\n                    type=\"text\"\n                    placeholder={tGenerate(\"enter_salary_range\")}\n                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {\n                      // Format only on blur to avoid cursor position issues\n                      const value = e.target.value.trim();\n\n                      if (!value) return;\n\n                      // Remove all $ and space symbols to clean the input\n                      const cleanValue = value.replace(SALARY_REMOVE_SYMBOL_REGEX, \"\");\n\n                      // Split by hyphen to handle ranges\n                      const parts = cleanValue.split(\"-\");\n\n                      if (parts.length === 1) {\n                        // Single value - format as range with empty second part\n                        const cleanPart = parts[0].trim();\n                        if (cleanPart) {\n                          setValue(\"salary_range\", `${CURRENCY_SYMBOL}${cleanPart} - ${CURRENCY_SYMBOL}`, { shouldValidate: true });\n                        }\n                      } else if (parts.length === 2) {\n                        // Range - format both parts\n                        const formattedParts = parts.map((part) => {\n                          const cleanPart = part.trim();\n                          return cleanPart ? `${CURRENCY_SYMBOL}${cleanPart}` : `${CURRENCY_SYMBOL}`;\n                        });\n                        setValue(\"salary_range\", formattedParts.join(\" - \"), { shouldValidate: true });\n                      }\n                    }}\n                  />\n                  {errors.salary_range && <InputWrapper.Error message={errors.salary_range.message as string} />}\n                </div>\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"salary_cycle\" required>\n                  {tGenerate(\"salsry_cycle\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Select\n                    control={control}\n                    name=\"salary_cycle\"\n                    options={SALARY_CYCLE_OPTIONS}\n                    className={`w-100 ${borderDangerFields.salary_cycle ? \"border-danger\" : \"\"}`}\n                    placeholder={tGenerate(\"select_salary_cycle\")}\n                  />\n                  {errors.salary_cycle && <InputWrapper.Error message={errors.salary_cycle.message as string} />}\n                </div>\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"location_type\" required>\n                  {tGenerate(\"job_location\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Select\n                    control={control}\n                    name=\"location_type\"\n                    options={LOCATION_TYPE_OPTIONS}\n                    className={`${borderDangerFields.location_type ? \"border-danger\" : \"\"} w-100`}\n                    placeholder={tGenerate(\"select_location_type\")}\n                  />\n                </div>\n                {errors.location_type && <InputWrapper.Error message={errors.location_type.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"email\" required>\n                  {tGenerate(\"state\")}\n                </InputWrapper.Label>\n                <Textbox\n                  className={`${borderDangerFields.state ? \"border-danger\" : \"\"} form-control`}\n                  control={control}\n                  name=\"state\"\n                  type=\"text\"\n                  placeholder={tGenerate(\"enter_state\")}\n                ></Textbox>\n                {errors.state && <InputWrapper.Error message={errors.state.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6\">\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"email\" required>\n                  {tGenerate(\"city\")}\n                </InputWrapper.Label>\n                <Textbox\n                  className={`${borderDangerFields.city ? \"border-danger\" : \"\"} form-control`}\n                  control={control}\n                  name=\"city\"\n                  type=\"text\"\n                  placeholder={tGenerate(\"enter_city\")}\n                ></Textbox>\n                {errors.city && <InputWrapper.Error message={errors.city.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"role_overview\")}{\" \"}\n                <InfoIcon\n                  tooltip=\"Role summary of job purpose and core impact.\n\n\"\n                  id=\"RoleOverview\"\n                  place=\"right\"\n                />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"role_overview\" required>\n                  {tGenerate(\"overview\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"role_overview\"\n                  control={control}\n                  placeholder={tGenerate(\"overview_placeholder\")}\n                  className={`${borderDangerFields.job_description ? \"border-danger\" : \"\"} form-control`}\n                />\n                {errors.role_overview && <InputWrapper.Error message={errors.role_overview.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"experince_level_heading\")} <InfoIcon tooltip=\"Required experience level.\" id=\"ExperienceLevel\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"experience_level\" required>\n                  {tGenerate(\"experince_level\")}\n                </InputWrapper.Label>\n                <Select\n                  control={control}\n                  name=\"experience_level\"\n                  options={EXPERIENCE_LEVEL_OPTIONS}\n                  className={`w-100 ${borderDangerFields.experience_level ? \"border-danger\" : \"\"}`}\n                  placeholder={tGenerate(\"select_experience_level\")}\n                />\n                {errors.experience_level && <InputWrapper.Error message={errors.experience_level.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"key_responsibilities_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Key tasks and duties expected in the role.\" id=\"KeyResponsibilities\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"name\" required>\n                  {tGenerate(\"key_responsibilities\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"responsibilities\"\n                  control={control}\n                  placeholder={tGenerate(\"key_responsibilities_placeholder\")}\n                  className={`${borderDangerFields.responsibilities ? \"border-danger\" : \"\"} form-control`}\n                />\n                {errors.responsibilities && <InputWrapper.Error message={errors.responsibilities.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"required_skills_heading\")}{\" \"}\n                <InfoIcon\n                  tooltip=\"Required skills & essential skills, degrees, and certifications.\"\n                  id=\"RequiredSkillsQualifications\"\n                  place=\"right\"\n                />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"educations_requirement\" required>\n                  {tGenerate(\"educational_requirments\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"educations_requirement\"\n                  control={control}\n                  placeholder={tGenerate(\"educational_requirments_placeholder\")}\n                  className={`${borderDangerFields.requirements ? \"border-danger\" : \"\"} form-control`}\n                />\n                {errors.educations_requirement && <InputWrapper.Error message={errors.educations_requirement.message as string} />}\n              </InputWrapper>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"certifications\">\n                  {tGenerate(\"certifications\")} <span className=\"text-muted\">{tGenerate(\"optional\")}</span>\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"certifications\"\n                  control={control}\n                  placeholder={tGenerate(\"certifications_placeholder\")}\n                  className=\"form-control\"\n                />\n                {errors.certifications && <InputWrapper.Error message={errors.certifications.message as string} />}\n              </InputWrapper>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"skills_and_software_expertise\" required>\n                  {tGenerate(\"specfic_skills_or_software_knowledge\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"skills_and_software_expertise\"\n                  control={control}\n                  placeholder={tGenerate(\"specfic_skills_or_software_knowledge_placeholder\")}\n                  className={`${borderDangerFields.skills_required ? \"border-danger\" : \"\"} form-control`}\n                />\n                {errors.skills_and_software_expertise && <InputWrapper.Error message={errors.skills_and_software_expertise.message as string} />}\n              </InputWrapper>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"experience_required\" required>\n                  {tGenerate(\"years_of_experince\")}\n                </InputWrapper.Label>\n                <Textbox\n                  className={`form-control w-50 ${borderDangerFields.experience_required ? \"border-danger\" : \"\"}`}\n                  control={control}\n                  name=\"experience_required\"\n                  type=\"text\"\n                  placeholder={tGenerate(\"years_of_experince_placeholder\")}\n                ></Textbox>\n                {errors.experience_required && <InputWrapper.Error message={errors.experience_required.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"ideal_traits_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Ideal personality and workstyle traits preferred.\" id=\"IdealCandidateTraits\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"ideal_candidate_traits\" required>\n                  {tGenerate(\"ideal_traits\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"ideal_candidate_traits\"\n                  control={control}\n                  placeholder={tGenerate(\"ideal_traits_placeholder\")}\n                  className={`form-control ${borderDangerFields.ideal_candidate_traits ? \"border-danger\" : \"\"}`}\n                />\n                {errors.ideal_candidate_traits && <InputWrapper.Error message={errors.ideal_candidate_traits.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"about_company_heading\")}{\" \"}\n                <InfoIcon tooltip=\"About your brief description of company background.\" id=\"AboutYourCompany\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"about_company\" required>\n                  {tGenerate(\"about_company\")}\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"about_company\"\n                  control={control}\n                  placeholder={tGenerate(\"about_company_placeholder\")}\n                  className={`form-control ${borderDangerFields.about_company ? \"border-danger\" : \"\"}`}\n                />\n                {errors.about_company && <InputWrapper.Error message={errors.about_company.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"job_benifits_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Perks & benefits, insurances, and time-off offered.\" id=\"PerksBenefits\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"perks_benefits\">\n                  {tGenerate(\"job_benifits\")}\n                  <span className=\"text-muted\"> {tGenerate(\"optional\")}</span>\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"perks_benefits\"\n                  control={control}\n                  placeholder={tGenerate(\"job_benifits_placeholder\")}\n                  className={\"form-control\"}\n                />\n                {errors.perks_benefits && <InputWrapper.Error message={errors.perks_benefits.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-6 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"tone_and_style_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Tone & writing tone used in the job description.\" id=\"ToneStyle\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"tone_style\" required>\n                  {tGenerate(\"tone_and_style\")}\n                </InputWrapper.Label>\n                <div className=\"icon-align\">\n                  <Select\n                    control={control}\n                    name=\"tone_style\"\n                    options={TONE_STYLE_OPTIONS}\n                    className={`${borderDangerFields.tone_style ? \"border-danger\" : \"\"} w-100`}\n                    placeholder={tGenerate(\"tone_and_style_placeholder\")}\n                  />\n                </div>\n                {errors.tone_style && <InputWrapper.Error message={errors.tone_style.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"additional_info_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Extra info like growth or team culture.\" id=\"AdditionalInfo\" place=\"right\" />\n              </h2>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"additional_info\">\n                  {t(\"additional_info\")} <span className=\"text-muted\"> {tGenerate(\"optional\")}</span>\n                </InputWrapper.Label>\n                <Textarea\n                  rows={4}\n                  name=\"additional_info\"\n                  control={control}\n                  placeholder={tGenerate(\"additional_info_placeholder\")}\n                  className=\"form-control\"\n                />\n                {errors.additional_info && <InputWrapper.Error message={errors.additional_info.message as string} />}\n              </InputWrapper>\n            </div>\n            <div className=\"col-md-12 mt-4\">\n              <h2 className={style.section_heading}>\n                {tGenerate(\"compliance_statement_heading\")}{\" \"}\n                <InfoIcon tooltip=\"Legal policy and compliance statements.\" id=\"ComplianceStatements\" place=\"right\" />{\" \"}\n              </h2>\n              <InputWrapper>\n                <div className=\"mb-2\">\n                  <Checkbox\n                    control={control}\n                    name=\"show_compliance\"\n                    label={\n                      <>\n                        {\" \"}\n                        {tGenerate(\"compliance_statement_part1\")} <br /> {tGenerate(\"compliance_statement_part2\")}\n                        <a\n                          href={\"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\"}\n                          className=\"fs-5 color-primary text-decoration-underline\"\n                          target=\"_blank\"\n                        >\n                           Learn More\n                        </a>\n                      </>\n                    }\n                  />\n                  {errors.show_compliance && <InputWrapper.Error message={errors.show_compliance.message as string} />}\n                </div>\n                <div className=\"d-flex align-items-center justify-content-between mb-2\">\n                  <InputWrapper.Label htmlFor=\"compliance_statement\" required>\n                    {tGenerate(\"compliance_statement_placeholder\")}\n                  </InputWrapper.Label>\n                </div>\n                <div className=\"icon-align mb-3\">\n                  <Controller\n                    name=\"compliance_statement\"\n                    control={control}\n                    render={({ field }) => (\n                      <ReactSelect\n                        {...field}\n                        isMulti\n                        options={COMPLIANCE_OPTIONS}\n                        className={`w-100 form-control react-multi-select p-0 ${borderDangerFields.compliance_statement ? \"border-danger\" : \"\"} ${watch(\"show_compliance\") ? \"show-active\" : \"\"}`}\n                        classNamePrefix=\"select\"\n                        placeholder={tGenerate(\"compliance_statement_placeholder\")}\n                        isDisabled={!watch(\"show_compliance\")}\n                        styles={{\n                          menu: (provided) => ({ ...provided  , maxHeight: \"120px\", overflow: \"auto\" }),\n                          menuList: (provided) => ({ ...provided  , maxHeight: \"120px\", overflow: \"auto\" }),\n                        }}\n                        \n                        onChange={(selectedOptions) => {\n                          field.onChange(selectedOptions ? selectedOptions.map((option) => option.value) : []);\n                        }}\n                        value={COMPLIANCE_OPTIONS.filter((option) => field.value?.includes(option.value))}\n                      />\n                    )}\n                  />\n                  {errors.compliance_statement && <InputWrapper.Error message={errors.compliance_statement.message as string} />}\n                </div>\n              </InputWrapper>\n            </div>\n\n            <div className=\"col-12 pb-5\">\n              <div className=\"button-align mt-4 pb-5\">\n                <Button type=\"submit\" className=\"primary-btn rounded-md\" disabled={isSubmitting || isUploading}>\n                  {t(\"submit\")}\n                  {isSubmitting && <Loader />}\n                </Button>\n                <Button\n                  type=\"button\"\n                  className=\"dark-outline-btn rounded-md\"\n                  onClick={() => {\n                    reset();\n                  }}\n                  disabled={isSubmitting || isUploading}\n                >\n                  {t(\"cancel\")}\n                </Button>\n              </div>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n\nexport default Generatejob;\n"], "names": [], "mappings": ";;;;AAEA,qBAAqB;AACrB;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,WAAW;AACX;AACA;AAIA;AACA;AAEA;AAGA;AACA;AACA;AAEA;AAiBA,SAAS;AACT;AAIA;AACA;AACA;AACA;AAlEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,SAAS;IACP,wBAAwB;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,QAAQ,GAAG,CAAC,+CAA+C;IAE3D,oBAAoB;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,sCAAsC;IAE3F,wBAAwB;IACxB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAE9E,OAAO;IACP,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,cAAc,IAAI,0IAAA,CAAA,kBAAe,KAAK;IAEzD,kCAAkC;IAClC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,YAAY,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,WAAW;QACX,OAAO;QACP,MAAM;QACN,UAAU;QACV,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,iBAAiB;QACjB,UAAU;QACV,YAAY;QACZ,sBAAsB;QACtB,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,qBAAqB;QACrB,wBAAwB;QACxB,eAAe;QACf,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QACtB,eAAe;YACb,GAAG,mIAAA,CAAA,eAAY;QACjB;QACA,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,gJAAA,CAAA,UAAqB,AAAD,EAAE;QAC5C,MAAM;IACR;IAEA,sCAAsC;IACtC,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,gBAAgB,CAAC,OAAS,OAAO,IAAI,+BAA+B;QACpE,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,eAAe;QACf,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,0IAAA,CAAA,YAAS,GAAG;YAClC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;YACA;QACF;QACA,gCAAgC;QAChC,IAAI,CAAC,0IAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YAClC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;YACA;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,0IAAA,CAAA,kBAAe,EAAE;YAC/B,QAAQ,GAAG,CAAC,qDAAqD,KAAK,IAAI;YAC1E,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;YACA;QACF;QAEA,gBAAgB;QAChB,QAAQ,GAAG,CAAC,8DAA8D;QAC1E,gDAAgD;QAChD,MAAM,iBAAiB;IACzB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,gBAAgB,gBAAgB;QAEtC,IAAI,CAAC,eAAe;YAClB,eAAe,EAAE;YACjB;QACF;QAEA,IAAI;YACF,eAAe;YACf,eAAe;YACf,UAAU;YAEV,mDAAmD;YACnD,MAAM,WAAW,MAAM,CAAA,GAAA,sJAAA,CAAA,0BAAuB,AAAD,EAAE;YAC/C,QAAQ,GAAG,CAAC,4CAA4C;YACxD,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,IAAI,CAAC,OAAO;gBACvC;YACF;YAEA,qDAAqD;YACrD,MAAM,eAAe,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC;YAC5C,MAAM,aAAa,aAAa,UAAU,IAAI,CAAC;YAC/C,MAAM,UAAU,aAAa,OAAO,GAAG,aAAa,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG;YAC9E,sDAAsD;YACtD,UAAU;YACV,iBAAiB;YACjB,kBAAkB,mDAAmD;YAErE,uCAAiB;;YAEjB;YAEA,IAAI,WAAW,aAAa,EAAE;gBAC5B,SAAS,iBAAiB,WAAW,aAAa;gBAClD,SAAS,iBAAiB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACxD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAM,CAAC;YACpE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAK,CAAC;YACnE;YACA,4EAA4E;YAC5E,2BAA2B;YAC3B,IAAI,WAAW,SAAS,EAAE;gBACxB,SAAS,SAAS,WAAW,SAAS;gBACtC,SAAS,SAAS;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAChD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;oBAAM,CAAC;YAChE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;oBAAK,CAAC;YAC/D;YAEA,IAAI,WAAW,eAAe,EAAE;gBAC9B,SAAS,iBAAiB,WAAW,eAAe;gBACpD,SAAS,iBAAiB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACxD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAM,CAAC;YACtE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAK,CAAC;YACrE;YACA,IAAI,WAAW,KAAK,EAAE;gBACpB,SAAS,SAAS,WAAW,KAAK;gBAClC,SAAS,SAAS;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAChD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAM,CAAC;YAC5D,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAK,CAAC;YAC3D;YACA,IAAI,WAAW,IAAI,EAAE;gBACnB,SAAS,QAAQ,WAAW,IAAI;gBAChC,SAAS,QAAQ;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC/C,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAM,CAAC;YAC3D,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,MAAM;oBAAK,CAAC;YAC1D;YACA,IAAI,WAAW,gBAAgB,EAAE;gBAC/B,SAAS,oBAAoB,WAAW,gBAAgB;gBACxD,SAAS,oBAAoB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC3D,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAM,CAAC;YACvE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAK,CAAC;YACtE;YAEA,IAAI,MAAM,OAAO,CAAC,WAAW,oBAAoB,KAAK,WAAW,oBAAoB,CAAC,MAAM,GAAG,GAAG;gBAChG,sCAAsC;gBACtC,SAAS,mBAAmB;gBAE5B,wFAAwF;gBACxF,MAAM,cAAc,WAAW,oBAAoB,CAAC,MAAM,CAAC,CAAC,QAAkB,0IAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK;gBAEnI,SAAS,wBAAwB;gBACjC,SAAS,wBAAwB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC/D,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,sBAAsB;oBAAM,CAAC;YAC3E,OAAO;gBACL,SAAS,wBAAwB,EAAE;gBACnC,SAAS,wBAAwB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC/D,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,sBAAsB;oBAAK,CAAC;YAC1E;YAEA,0DAA0D;YAC1D,IAAI,WAAW,QAAQ,EAAE;gBACvB,iFAAiF;gBACjF,kEAAkE;gBAClE,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;oBACtD,SAAS,mBAAmB;oBAC5B,SAAS,mBAAmB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC5D,OAAO,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;oBAC7D,SAAS,mBAAmB;oBAC5B,SAAS,mBAAmB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC5D,OAAO,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa;oBACjE,SAAS,mBAAmB;oBAC5B,SAAS,mBAAmB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC5D,OAAO,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW;oBAC/D,SAAS,mBAAmB;oBAC5B,SAAS,mBAAmB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC5D,OAAO,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;oBAC7D,SAAS,mBAAmB;oBAC5B,SAAS,mBAAmB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC5D;gBACA,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAM,CAAC;YAC/D,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAK,CAAC;YAC9D;YACA,IAAI,WAAW,aAAa,EAAE;gBAC5B,MAAM,sBAAsB,0IAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,CAAC,WAAW,OAAO,WAAW,aAAa,EAAE;gBAC5H,IAAI,qBAAqB;oBACvB,SAAS,iBAAiB,oBAAoB,KAAK;oBACnD,SAAS,iBAAiB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC1D;gBACA,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAM,CAAC;YACpE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAK,CAAC;YACnE;YAEA,IAAI,WAAW,YAAY,EAAE;gBAC3B,MAAM,qBAAqB,0IAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,CAAC,WAAW,OAAO,WAAW,YAAY,EAAE;gBACzH,IAAI,oBAAoB;oBACtB,SAAS,gBAAgB,mBAAmB,KAAK;oBACjD,SAAS,gBAAgB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBACzD;gBACA,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAM,CAAC;YACnE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAK,CAAC;YAClE;YAEA,iCAAiC;YACjC,IAAI,WAAW,YAAY,EAAE;gBAC3B,SAAS,gBAAgB,WAAW,YAAY;gBAChD,SAAS,gBAAgB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACvD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAM,CAAC;YACnE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAK,CAAC;YAClE;YAEA,SAAS;YACT,IAAI,WAAW,gBAAgB,EAAE;gBAC/B,SAAS,oBAAoB,WAAW,gBAAgB;gBACxD,SAAS,oBAAoB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC3D,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAM,CAAC;YACvE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAK,CAAC;YACtE;YAEA,IAAI,WAAW,kBAAkB,EAAE;gBACjC,6CAA6C;gBAC7C,SAAS,0BAA0B,WAAW,kBAAkB;gBAChE,SAAS,0BAA0B;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACjE,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,oBAAoB;oBAAM,CAAC;YACzE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAK,CAAC;YAClE;YAEA,IAAI,WAAW,eAAe,EAAE;gBAC9B,uDAAuD;gBACvD,SAAS,iCAAiC,WAAW,eAAe;gBACpE,SAAS,iCAAiC;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACxE,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAM,CAAC;YACtE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,iBAAiB;oBAAK,CAAC;YACrE;YAEA,6FAA6F;YAC7F,IAAI,WAAW,QAAQ,EAAE;gBACvB,SAAS,kBAAkB,WAAW,QAAQ;gBAC9C,SAAS,kBAAkB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACzD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAM,CAAC;YAC/D,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAK,CAAC;YAC9D;YAEA,+BAA+B;YAC/B,IAAI,WAAW,UAAU,EAAE;gBACzB,SAAS,cAAc,WAAW,UAAU;gBAC5C,SAAS,cAAc;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACrD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAM,CAAC;YACjE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAK,CAAC;YAChE;YAEA,mDAAmD;YACnD,IAAI,WAAW,mBAAmB,EAAE;gBAClC,SAAS,uBAAuB,WAAW,mBAAmB;gBAC9D,SAAS,uBAAuB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBAC9D,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,qBAAqB;oBAAM,CAAC;YAC1E,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,qBAAqB;oBAAK,CAAC;YACzE;YAEA,IAAI,WAAW,gBAAgB,EAAE;gBAC/B,MAAM,yBAAyB,0IAAA,CAAA,2BAAwB,CAAC,IAAI,CAC1D,CAAC,SAAW,OAAO,KAAK,CAAC,WAAW,OAAO,WAAW,gBAAgB,EAAE;gBAE1E,IAAI,wBAAwB;oBAC1B,SAAS,oBAAoB,uBAAuB,KAAK;oBACzD,SAAS,oBAAoB;wBAAE,MAAM;wBAAU,SAAS;oBAAG;gBAC7D;gBACA,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAM,CAAC;YACvE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,kBAAkB;oBAAK,CAAC;YACtE;YACA,IAAI,WAAW,gBAAgB,EAAE;gBAC/B,SAAS,0BAA0B,WAAW,gBAAgB;gBAC9D,SAAS,0BAA0B;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACjE,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,wBAAwB;oBAAM,CAAC;YAC7E,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,wBAAwB;oBAAK,CAAC;YAC5E;YACA,IAAI,WAAW,aAAa,EAAE;gBAC5B,SAAS,iBAAiB,WAAW,aAAa;gBAClD,SAAS,iBAAiB;oBAAE,MAAM;oBAAU,SAAS;gBAAG;gBACxD,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAM,CAAC;YACpE,OAAO;gBACL,sBAAsB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,eAAe;oBAAK,CAAC;YACnE;YACA,IAAI,WAAW,cAAc,EAAE;gBAC7B,SAAS,kBAAkB,WAAW,cAAc;YACtD;YACA,IAAI,WAAW,eAAe,EAAE;gBAC9B,SAAS,mBAAmB,WAAW,eAAe;YACxD;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,kBAAkB,gCAAgC;QACpD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,WAA6C,OAAO;QACxD,IAAI;YACF,gBAAgB;YAChB,IAAI,UAAU,eAAe;gBAC3B,MAAM,cAAc,cAAc,IAAI;gBACtC,MAAM,YAAY,IAAI,QAAQ;oBAC5B,gBAAgB;gBAClB;gBACA,MAAM,MAAM,QAAQ;oBAClB,QAAQ;oBACR,SAAS;oBACT,MAAM;gBACR;YACF;YACA,MAAM,wBAA4C;gBAChD,GAAG,IAAI;gBACP,sBAAsB,CAAC,KAAK,oBAAoB,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,OAAyB,OAAO,SAAS;gBACzG,yCAAyC;gBACzC,SAAS,UAAU;gBACnB,aAAa,cAAc;YAC7B;YACA,kDAAkD;YAClD,SAAS;YACT,MAAM,oBAAoB,MAAM,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YAClD,IAAI,qBAAqB,mBAAmB,QAAQ,kBAAkB,IAAI,EAAE,SAAS;gBACnF,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,kBAAkB,IAAI,CAAC,OAAO;gBAEpD,uCAAuC;gBACvC,MAAM,eAAe,kBAAkB,IAAI,EAAE;gBAM7C,0CAA0C;gBAC1C,SACE,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE;oBACZ,cAAc,aAAa,YAAY;oBACvC,oBAAoB,aAAa,kBAAkB;oBACnD,uBAAuB,aAAa,qBAAqB;gBAC3D;gBAGF,sDAAsD;gBACtD,wDAAwD;gBACxD,SACE,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;oBACZ,GAAG,qBAAqB;gBAC1B;gBAGF,iCAAiC;gBACjC,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,mBAAmB;YAC7C,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,kBAAkB,IAAI,EAAE,YAAsB,EAAE;YACtE;YAEA,mDAAmD;YACnD,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,EAAE,0BAA0B;YAC1C,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,cAAe,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ,IAAI,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ,EAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE,0HAAA,CAAA,UAAM,CAAC,SAAS;IAC5H,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD;gBACrC,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;oBACtD,MAAM,gBAAgB,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;4BACtD,OAAO,KAAK,EAAE;4BACd,OAAO,KAAK,IAAI;wBAClB,CAAC;oBACD,sBAAsB;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;QACA,IAAI,iBAAiB,OAAO,EAAE;QAC9B,iBAAiB,OAAO,GAAG;QAC3B;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,QAAQ;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC,oJAAA,CAAA,UAAa;wCAAC,SAAS,IAAM,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;;;;;;oCAAK;kDAC5C,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKxC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,iJAAA,CAAA,UAAU;oDAAC,WAAU;;;;;;gDAAa;;;;;;;sDAErC,8OAAC;4CAAE,WAAU;sDAAO;;;;;;sDACpB,8OAAC,kJAAA,CAAA,UAAY;4CAAC,WAAU;sDACtB,cAAA,8OAAC,kJAAA,CAAA,UAAS;gDAER,oBAAmB;gDACnB,UAAU;gDACV,UAAU;gDACV,WAAW;gDACX,mBAAmB,0IAAA,CAAA,iCAA8B;gDACjD,iBAAiB;+CANZ;;;;;;;;;;;;;;;;;;;;;0CAWb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,8UAAA,CAAA,UAAiB;oCAAE,KAAI;oCAAoB,WAAW,yJAAA,CAAA,UAAK,CAAC,mBAAmB;;;;;;;;;;;;;;;;;;;;;;8BAIjG,8OAAC;oBAAK,UAAU,aAAa;8BAC3B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;oCACjC,UAAU;oCAAkB;kDAC7B,8OAAC,+IAAA,CAAA,UAAQ;wCAAC,SAAQ;wCAAuD,IAAG;wCAAe,OAAM;;;;;;;;;;;;0CAEnG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAQ,QAAQ;sDACzC,UAAU;;;;;;sDAEb,8OAAC,6IAAA,CAAA,UAAO;4CACN,WAAW,GAAG,mBAAmB,SAAS,GAAG,kBAAkB,GAAG,aAAa,CAAC;4CAChF,SAAS;4CACT,MAAK;4CACL,MAAK;4CACL,aAAa,UAAU;;;;;;wCAExB,OAAO,KAAK,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAkB,QAAQ;sDACnD,UAAU;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;gDACL,SAAS;gDACT,MAAK;gDACL,SAAS,0IAAA,CAAA,kBAAe;gDACxB,WAAW,GAAG,mBAAmB,QAAQ,GAAG,kBAAkB,GAAG,MAAM,CAAC;gDACxE,aAAa,UAAU;;;;;;;;;;;wCAG1B,OAAO,eAAe,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAG1F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAgB,QAAQ;sDACjD,UAAU;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;gDACL,SAAS;gDACT,MAAK;gDACL,SAAS;gDACT,WAAW,CAAC,MAAM,EAAE,mBAAmB,aAAa,GAAG,kBAAkB,IAAI;gDAC7E,aAAY;;;;;;;;;;;wCAGf,OAAO,aAAa,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAItF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAe,QAAQ;sDAChD,UAAU;;;;;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6IAAA,CAAA,UAAO;oDACN,WAAW,CAAC,aAAa,EAAE,mBAAmB,YAAY,GAAG,kBAAkB,IAAI;oDACnF,SAAS;oDACT,MAAK;oDACL,MAAK;oDACL,aAAa,UAAU;oDACvB,QAAQ,CAAC;wDACP,sDAAsD;wDACtD,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;wDAEjC,IAAI,CAAC,OAAO;wDAEZ,oDAAoD;wDACpD,MAAM,aAAa,MAAM,OAAO,CAAC,0IAAA,CAAA,6BAA0B,EAAE;wDAE7D,mCAAmC;wDACnC,MAAM,QAAQ,WAAW,KAAK,CAAC;wDAE/B,IAAI,MAAM,MAAM,KAAK,GAAG;4DACtB,wDAAwD;4DACxD,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI;4DAC/B,IAAI,WAAW;gEACb,SAAS,gBAAgB,GAAG,0IAAA,CAAA,kBAAe,GAAG,UAAU,GAAG,EAAE,0IAAA,CAAA,kBAAe,EAAE,EAAE;oEAAE,gBAAgB;gEAAK;4DACzG;wDACF,OAAO,IAAI,MAAM,MAAM,KAAK,GAAG;4DAC7B,4BAA4B;4DAC5B,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC;gEAChC,MAAM,YAAY,KAAK,IAAI;gEAC3B,OAAO,YAAY,GAAG,0IAAA,CAAA,kBAAe,GAAG,WAAW,GAAG,GAAG,0IAAA,CAAA,kBAAe,EAAE;4DAC5E;4DACA,SAAS,gBAAgB,eAAe,IAAI,CAAC,QAAQ;gEAAE,gBAAgB;4DAAK;wDAC9E;oDACF;;;;;;gDAED,OAAO,YAAY,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oDAAC,SAAS,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAItF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAe,QAAQ;sDAChD,UAAU;;;;;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4IAAA,CAAA,UAAM;oDACL,SAAS;oDACT,MAAK;oDACL,SAAS,0IAAA,CAAA,uBAAoB;oDAC7B,WAAW,CAAC,MAAM,EAAE,mBAAmB,YAAY,GAAG,kBAAkB,IAAI;oDAC5E,aAAa,UAAU;;;;;;gDAExB,OAAO,YAAY,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oDAAC,SAAS,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAItF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAgB,QAAQ;sDACjD,UAAU;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;gDACL,SAAS;gDACT,MAAK;gDACL,SAAS,0IAAA,CAAA,wBAAqB;gDAC9B,WAAW,GAAG,mBAAmB,aAAa,GAAG,kBAAkB,GAAG,MAAM,CAAC;gDAC7E,aAAa,UAAU;;;;;;;;;;;wCAG1B,OAAO,aAAa,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAGtF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAQ,QAAQ;sDACzC,UAAU;;;;;;sDAEb,8OAAC,6IAAA,CAAA,UAAO;4CACN,WAAW,GAAG,mBAAmB,KAAK,GAAG,kBAAkB,GAAG,aAAa,CAAC;4CAC5E,SAAS;4CACT,MAAK;4CACL,MAAK;4CACL,aAAa,UAAU;;;;;;wCAExB,OAAO,KAAK,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;sDACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAQ;4CAAQ,QAAQ;sDACzC,UAAU;;;;;;sDAEb,8OAAC,6IAAA,CAAA,UAAO;4CACN,WAAW,GAAG,mBAAmB,IAAI,GAAG,kBAAkB,GAAG,aAAa,CAAC;4CAC3E,SAAS;4CACT,MAAK;4CACL,MAAK;4CACL,aAAa,UAAU;;;;;;wCAExB,OAAO,IAAI,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;4CAAC,SAAS,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAAkB;0DAC7B,8OAAC,+IAAA,CAAA,UAAQ;gDACP,SAAQ;gDAGR,IAAG;gDACH,OAAM;;;;;;;;;;;;kDAGV,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAgB,QAAQ;0DACjD,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,GAAG,mBAAmB,eAAe,GAAG,kBAAkB,GAAG,aAAa,CAAC;;;;;;4CAEvF,OAAO,aAAa,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGtF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAA2B;0DAAC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAA6B,IAAG;gDAAkB,OAAM;;;;;;;;;;;;kDAEnH,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAmB,QAAQ;0DACpD,UAAU;;;;;;0DAEb,8OAAC,4IAAA,CAAA,UAAM;gDACL,SAAS;gDACT,MAAK;gDACL,SAAS,0IAAA,CAAA,2BAAwB;gDACjC,WAAW,CAAC,MAAM,EAAE,mBAAmB,gBAAgB,GAAG,kBAAkB,IAAI;gDAChF,aAAa,UAAU;;;;;;4CAExB,OAAO,gBAAgB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAG5F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAAiC;0DAC5C,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAA6C,IAAG;gDAAsB,OAAM;;;;;;;;;;;;kDAEhG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAO,QAAQ;0DACxC,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,GAAG,mBAAmB,gBAAgB,GAAG,kBAAkB,GAAG,aAAa,CAAC;;;;;;4CAExF,OAAO,gBAAgB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAG5F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAA4B;0DACvC,8OAAC,+IAAA,CAAA,UAAQ;gDACP,SAAQ;gDACR,IAAG;gDACH,OAAM;;;;;;;;;;;;kDAGV,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAyB,QAAQ;0DAC1D,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,GAAG,mBAAmB,YAAY,GAAG,kBAAkB,GAAG,aAAa,CAAC;;;;;;4CAEpF,OAAO,sBAAsB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,sBAAsB,CAAC,OAAO;;;;;;;;;;;;kDAEtG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;;oDACzB,UAAU;oDAAkB;kEAAC,8OAAC;wDAAK,WAAU;kEAAc,UAAU;;;;;;;;;;;;0DAExE,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAU;;;;;;4CAEX,OAAO,cAAc,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;kDAEtF,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAgC,QAAQ;0DACjE,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,GAAG,mBAAmB,eAAe,GAAG,kBAAkB,GAAG,aAAa,CAAC;;;;;;4CAEvF,OAAO,6BAA6B,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,6BAA6B,CAAC,OAAO;;;;;;;;;;;;kDAEpH,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAsB,QAAQ;0DACvD,UAAU;;;;;;0DAEb,8OAAC,6IAAA,CAAA,UAAO;gDACN,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,mBAAmB,GAAG,kBAAkB,IAAI;gDAC/F,SAAS;gDACT,MAAK;gDACL,MAAK;gDACL,aAAa,UAAU;;;;;;4CAExB,OAAO,mBAAmB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,mBAAmB,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGlG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAAyB;0DACpC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAAoD,IAAG;gDAAuB,OAAM;;;;;;;;;;;;kDAExG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAyB,QAAQ;0DAC1D,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,CAAC,aAAa,EAAE,mBAAmB,sBAAsB,GAAG,kBAAkB,IAAI;;;;;;4CAE9F,OAAO,sBAAsB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,sBAAsB,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAA0B;0DACrC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAAsD,IAAG;gDAAmB,OAAM;;;;;;;;;;;;kDAEtG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAgB,QAAQ;0DACjD,UAAU;;;;;;0DAEb,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW,CAAC,aAAa,EAAE,mBAAmB,aAAa,GAAG,kBAAkB,IAAI;;;;;;4CAErF,OAAO,aAAa,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGtF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAAyB;0DACpC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAAsD,IAAG;gDAAgB,OAAM;;;;;;;;;;;;kDAEnG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;;oDACzB,UAAU;kEACX,8OAAC;wDAAK,WAAU;;4DAAa;4DAAE,UAAU;;;;;;;;;;;;;0DAE3C,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAW;;;;;;4CAEZ,OAAO,cAAc,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGxF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAA2B;0DACtC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAAmD,IAAG;gDAAY,OAAM;;;;;;;;;;;;kDAE5F,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAa,QAAQ;0DAC9C,UAAU;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;oDACL,SAAS;oDACT,MAAK;oDACL,SAAS,0IAAA,CAAA,qBAAkB;oDAC3B,WAAW,GAAG,mBAAmB,UAAU,GAAG,kBAAkB,GAAG,MAAM,CAAC;oDAC1E,aAAa,UAAU;;;;;;;;;;;4CAG1B,OAAO,UAAU,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAGhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAA4B;0DACvC,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAA0C,IAAG;gDAAiB,OAAM;;;;;;;;;;;;kDAExF,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;;oDACzB,EAAE;oDAAmB;kEAAC,8OAAC;wDAAK,WAAU;;4DAAa;4DAAE,UAAU;;;;;;;;;;;;;0DAElE,8OAAC,8IAAA,CAAA,UAAQ;gDACP,MAAM;gDACN,MAAK;gDACL,SAAS;gDACT,aAAa,UAAU;gDACvB,WAAU;;;;;;4CAEX,OAAO,eAAe,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAS,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAG1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;;4CACjC,UAAU;4CAAiC;0DAC5C,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,SAAQ;gDAA0C,IAAG;gDAAuB,OAAM;;;;;;4CAAW;;;;;;;kDAEzG,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,UAAQ;wDACP,SAAS;wDACT,MAAK;wDACL,qBACE;;gEACG;gEACA,UAAU;gEAA8B;8EAAC,8OAAC;;;;;gEAAK;gEAAE,UAAU;8EAC5D,8OAAC;oEACC,MAAM;oEACN,WAAU;oEACV,QAAO;8EACR;;;;;;;;;;;;;oDAMN,OAAO,eAAe,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wDAAC,SAAS,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;0DAExF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oDAAC,SAAQ;oDAAuB,QAAQ;8DACxD,UAAU;;;;;;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8JAAA,CAAA,aAAU;wDACT,MAAK;wDACL,SAAS;wDACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,iLAAA,CAAA,UAAW;gEACT,GAAG,KAAK;gEACT,OAAO;gEACP,SAAS,0IAAA,CAAA,qBAAkB;gEAC3B,WAAW,CAAC,0CAA0C,EAAE,mBAAmB,oBAAoB,GAAG,kBAAkB,GAAG,CAAC,EAAE,MAAM,qBAAqB,gBAAgB,IAAI;gEACzK,iBAAgB;gEAChB,aAAa,UAAU;gEACvB,YAAY,CAAC,MAAM;gEACnB,QAAQ;oEACN,MAAM,CAAC,WAAa,CAAC;4EAAE,GAAG,QAAQ;4EAAI,WAAW;4EAAS,UAAU;wEAAO,CAAC;oEAC5E,UAAU,CAAC,WAAa,CAAC;4EAAE,GAAG,QAAQ;4EAAI,WAAW;4EAAS,UAAU;wEAAO,CAAC;gEAClF;gEAEA,UAAU,CAAC;oEACT,MAAM,QAAQ,CAAC,kBAAkB,gBAAgB,GAAG,CAAC,CAAC,SAAW,OAAO,KAAK,IAAI,EAAE;gEACrF;gEACA,OAAO,0IAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAC,SAAW,MAAM,KAAK,EAAE,SAAS,OAAO,KAAK;;;;;;;;;;;oDAIpF,OAAO,oBAAoB,kBAAI,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;wDAAC,SAAS,OAAO,oBAAoB,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAKtG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,WAAU;4CAAyB,UAAU,gBAAgB;;gDAChF,EAAE;gDACF,8BAAgB,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;sDAE1B,8OAAC,4IAAA,CAAA,UAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS;gDACP;4CACF;4CACA,UAAU,gBAAgB;sDAEzB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;uCAEe", "debugId": null}}, {"offset": {"line": 3358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
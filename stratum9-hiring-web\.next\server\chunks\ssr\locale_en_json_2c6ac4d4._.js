module.exports = {

"[project]/locale/en.json (json)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"upload_here\":\"Upload here\",\"create_new_event\":\"Create New Event\",\"title_req\":\"Title is required\",\"date_req\":\"Date is required\",\"start_time_req\":\"Start time is required\",\"end_time_req\":\"End time is required\",\"interview_type_req\":\"Interview type is required\",\"start_interview\":\"Start Interview\",\"job_id_desc\":\"Enter Job ID\",\"please_select_job\":\"Please select job\",\"job_title\":\"Job Title\",\"update_interview\":\"Update Interview\",\"interview_round_number\":\"Interview Round Number\",\"view_interview_questions\":\"View Interview Questions\",\"candidate_placeholder\":\"Please select candidate\",\"behavioral_info_max_1500_chars\":\"Behavioral info must be at most 1500 characters long\",\"job_application_is_not_approved\":\"Job application is not approved\",\"answer_max_2000_chars\":\"Answer must be at most 2000 characters long\",\"interview_date\":\"Interview Date\",\"interview_type\":\"Interview Type\",\"interview_duration\":\"Interview Duration\",\"job_title_req\":\"Job title is required\",\"please_select_job_title_first\":\"Please select job title first\",\"job_id_req\":\"Job ID is required\",\"candidate_req\":\"Candidate is required\",\"upload_doc\":\"Upload Document\",\"please_select_interview_type\":\"Please select interview type\",\"interview_is_ended\":\"Interview is already ended\",\"uploading\":\"Uploading...\",\"max_file_size\":\"maximum file size 10MB\",\"title\":\"Title\",\"describe_candidate_behaviours\":\"Describe Candidate’s Behaviours & Other Observations\",\"loading\":\"Loading...\",\"date\":\"Date\",\"document\":\"Document\",\"home\":\"Home\",\"valid_name\":\"Please enter valid name\",\"enter_question\":\"Enter Question\",\"add_question\":\"Add Question\",\"update_question\":\"Update Question\",\"conduct_interview\":\"Conduct Interview\",\"start_time\":\"Start Time\",\"invalid_file_format\":\"Invalid file format\",\"invalid_size_format\":\"File size should be less than 10MB\",\"invalid_image_size_format\":\"Image size should be less than 5MB\",\"end_time\":\"End Time\",\"interviewer\":\"Interviewer\",\"interviewer_req\":\"Interviewer is required\",\"please_select_interviewer\":\"Please select interviewer\",\"please_select_date\":\"Please select date\",\"cannot_add_interview_skill_question_after_ended\":\"Cannot add interview skill question after ended\",\"enter_start_time\":\"Enter start time\",\"cannot_update_interview_after_ended\":\"Cannot update interview after ended\",\"enter_end_time\":\"Enter end time\",\"interview_ended\":\"Interview ended successfully\",\"cannot_schedule_more_than_one_month_in_advance\":\"Cannot schedule more than one month in advance\",\"description_max_2000_chars\":\"Description must be at most 2000 characters long\",\"end_time_must_be_after_start_time\":\"End time must be after start time\",\"interview_must_be_at_least_10_min\":\"Interview must be at least 10 minutes long\",\"interview_must_not_exceed_2_hours\":\"Interview must not exceed 2 hours\",\"previously_scheduled_interview_is_not_ended\":\"Previously scheduled interview is not ended\",\"interview_not_found\":\"Interview not found\",\"add_new_question\":\"+ Add New Question\",\"next_skill_interview\":\"Next Skill Interview\",\"career\":\"Career\",\"save_next\":\"Save & Next\",\"role_based\":\"Role Based\",\"culture_based\":\"Culture Based\",\"candidate_not_aware\":\"Candidate is not aware of this skill or behaviour and don't understand how it impacts his performance.\",\"career_based\":\"Career Based\",\"score_range\":\"from 1 to 9. Give extreme as a score for a candidate who far exceeds your expectations for this skill.\",\"score\":\"Score\",\"stratum\":\"Stratum\",\"score_the_candidate_for\":\"Score the candidate for\",\"career_based_skills\":\"Career Based Skills\",\"title_min_5_chars\":\"Title must be at least 5 characters long\",\"title_max_100_chars\":\"Title must be at most 100 characters long\",\"edit_question\":\"Edit Question\",\"interview_description\":\"Enter event description\",\"title_desc\":\"Enter interview title\",\"additional_info_desc_\":\"Enter any additional info\",\"record_interview\":\"Record Interview\",\"culture_specific_performance_based_skills\":\"Culture Specific Performance Based Skills\",\"role_specific_performance_based_skills\":\"Role Specific Performance Based Skills\",\"for\":\"For\",\"extreme\":\"Extreme\",\"please_mark_stratum_score\":\"Please mark stratum score\",\"view_resume\":\"View Resume\",\"recording_in_progress\":\"Recording In-progress\",\"additional_document\":\"Additional Document\",\"interview_questions\":\"Interview Questions\",\"download_questions\":\"Download Questions\",\"interview_question_updated\":\"Interview question updated successfully\",\"interview_question_added\":\"Interview question added successfully\",\"question_not_found\":\"Question not found\",\"attachments\":\"Attachments\",\"stop_recording\":\"Stop Recording\",\"additional_notes\":\"Additional Notes\",\"question_req\":\"Question is required\",\"pre_interview_description\":\"Below, you'll find questions tailored for career-based, performance-based, and culture-based skills. Feel free to edit these questions or add new ones to fit your hiring needs.\",\"calendar\":\"Calendar\",\"dashboard_\":\"Dashboard\",\"interview_\":\" Interview\",\"question\":\"Question\",\"end_interview\":\"End Interview\",\"end_interview_desc\":\"You can either qualify this candidate for the next round or you can end the interview process for this candidate. Once submitted you cannot reverse your choice.\",\"question_max_500_chars\":\"Question must be at most 500 characters long\",\"pre_interview\":\"Pre-Interview\",\"questions_overview\":\"Questions Overview\",\"description\":\"Description\",\"max_questions_reached\":\"You have reached maximum limit to add new questions\",\"job_requirement_generations\":\"Job Requirement Generations\",\"one_on_one\":\"One-On-One\",\"video_call\":\"Video Call\",\"cannot_schedule_interview_past\":\"Cannot schedule interview for past date\",\"max_files_limit_msg\":\"Maximum file upload limit is 3 files\",\"hiring_manager_dashboard\":\"Hiring Manager Dashboard\",\"schedule_interview\":\"Schedule Interview\",\"archive_candidate\":\"Archive Candidate\",\"cancel\":\"Cancel\",\"proceed_to_interview\":\"Proceed To Interview\",\"interview_scheduled_successfully\":\"Interview scheduled successfully\",\"interview_already_scheduled\":\"Interview is already scheduled\",\"email_req_m\":\"Please enter your registered email\",\"email_val_msg\":\"Please enter a valid email address\",\"pass_not_match\":\"Password does not match\",\"pass_req\":\"Please enter your password\",\"pass_val\":\"Password must be between 8 and 16 characters long and include at least 1 uppercase letter, 1 lowercase letter, 1 numeric digit, and 1 special character\",\"confirm_pass_req\":\"Please re-enter your password\",\"otp_req\":\"Please enter the received verification code\",\"valid_otp\":\"Please enter valid OTP\",\"user_not_found\":\"User not found\",\"please_enter_a_valid_verification_code\":\"Please enter a valid verification code\",\"otp_has_expired_please_request_a_new_otp\":\"Entered verification code is expired! You can request a new one.\",\"something_went_wrong\":\"Something went wrong\",\"verification_code_sent\":\"We sent a 4-digit verification code to your registered email\",\"otp_sending_failed\":\"Verification code sending failed\",\"wrong_password\":\"Email or password is incorrect\",\"login_successful\":\"Login successful\",\"assessment_has_expired\":\"Assessment link has expired\",\"assessment_instructions\":\"Assessment Instructions\",\"please_read_the_following_instructions_carefully\":\"Please read the following instructions carefully\",\"all_questions_are_mandatory_to_answer\":\"All questions are mandatory to answer\",\"once_submitted_you_cannot_change_your_answers\":\"Once submitted, you cannot change your answers\",\"please_complete_the_assessment_in_one_sitting\":\"Please complete the assessment in one sitting\",\"ensure_you_have_a_stable_internet_connection\":\"Ensure you have a stable internet connection\",\"you_must_provide_a_valid_email_address_to_proceed\":\"You must provide a valid email address to proceed\",\"email_address\":\"Email Address\",\"verifying\":\"Verifying...\",\"start_assessment\":\"Start Assessment\",\"email_verified_successfully\":\"Email verified successfully\",\"failed_to_verify_email\":\"Failed to verify email\",\"an_unexpected_error_occurred_while_verifying_email\":\"An unexpected error occurred while verifying email\",\"no_final_assessment_id_found\":\"No assessment ID found\",\"password_updated\":\"Password updated successfully\",\"skill_name_required\":\"Skill name is required\",\"skill_description_required\":\"Description is required\",\"no_career_skills\":\"No career skills data available. Please generate job skills first.\",\"generate_job_requirement_failed\":\"Failed to generate job requirement\",\"generate_job_requirement_error\":\"Something went wrong. Please try again.\",\"no_description_available\":\"No description available\",\"error_fetching_skills\":\"Error fetching skills data\",\"form_submission_error\":\"Form Submission Error\",\"pdf_job_req_empty\":\"Job requirement cannot be empty. Please add content before downloading.\",\"pdf_generating\":\"Generating PDF...\",\"pdf_download_success\":\"PDF downloaded successfully!\",\"pdf_download_fail\":\"Failed to generate PDF. Please try again.\",\"pdf_generation_error\":\"PDF generation error\",\"assessment_data_is_missing\":\"Assessment data is missing\",\"please_answer_all_questions_before_submitting\":\"Please answer all questions before submitting\",\"assessment_submitted_successfully\":\"Assessment submitted successfully\",\"failed_to_submit_assessment\":\"Failed to submit assessment\",\"an_error_occurred_while_submitting_assessment\":\"An error occurred while submitting assessment\",\"loading_assessment_questions\":\"Loading assessment questions...\",\"no_assessment_data_found\":\"No assessment data found. Please check the assessment ID.\",\"thank_you\":\"Thank You!\",\"your_assessment_has_been_submitted_successfully\":\"Your assessment has been submitted successfully.\",\"we_will_review_your_answers_and_get_back_to_you_soon\":\"We will review your answers and get back to you soon.\",\"candidate\":\"Candidate\",\"assessment\":\"Assessment\",\"your_information\":\"Your Information\",\"previous_skill_assessment\":\"Previous Skill Assessment\",\"submitting\":\"Submitting...\",\"submit_assessment\":\"Submit Assessment\",\"invalid_or_missing_final_assessment_id\":\"Invalid or missing finalAssessmentId\",\"job_id_and_job_application_id_are_required\":\"Job ID and Job Application ID are required\",\"copy_job_req_empty\":\"Job requirement cannot be empty. Please add content before copying.\",\"copy_html_success\":\"Formatted content copied to clipboard!\",\"copy_text_success\":\"Content copied to clipboard (plain text only)!\",\"copy_success\":\"Content copied to clipboard!\",\"copy_fail\":\"Failed to copy content\",\"copy_error\":\"Copy error\",\"fallback_copy_error\":\"Fallback copy error\",\"save_job_empty_error\":\"Job requirement cannot be empty. Please add content before saving.\",\"save_job_failed\":\"Failed to save job requirement\",\"save_job_unknown_error\":\"Something went wrong. Please try again.\",\"save_job_error_log\":\"Save job error\",\"error_fetching_jobs\":\"Error fetching jobs\",\"archive_job_failed\":\"Failed to archive the job\",\"error_fetching_applications\":\"Error fetching applications\",\"invalid_job_params\":\"Invalid job parameters. Redirecting to Active Jobs.\",\"set_view_questionarie\":\"View/Set Questionnaire\",\"view_your_summary\":\"View Your Summary\",\"error_fetching_interviews\":\"Error fetching interviews\",\"error_fetching_dashboard_count\":\"Error fetching dashboard count\",\"final_assessment_created_successfully\":\"Final assessment created successfully\",\"failed_to_create_final_assessment\":\"Failed to create final assessment\",\"an_error_occurred_while_creating_the_final_assessment\":\"An error occurred while creating the final assessment\",\"interview_summary\":\"Interview Summary\",\"interview_summary_summary\":\"Summary\",\"processing\":\"Loading...\",\"generating_questions\":\"Generating Questions...\",\"create_final_assessment\":\"Create Final Assessment\",\"view_final_assessment_result\":\"View Final Assessment Result\",\"view_final_assessment\":\"View Final Assessment\",\"round_1_summary\":\"ROUND 1 Summary\",\"preview_candidate_resume\":\"Preview Candidate Resume\",\"aaron_salko\":\"Aaron Salko\",\"benedict_cumberbatch\":\"Benedict Cumberbatch\",\"operations_admin\":\"Operations Admin\",\"department_add\":\"Add Department\",\"department_update\":\"Update Department\",\"department_delete\":\"Delete Department\",\"invalid_time_format\":\"Invalid time format\",\"department_default\":\"Department\",\"department_name\":\"Department Name\",\"department_name_placeholder\":\"Enter department name\",\"department_delete_confirmation\":\"Are you sure you want to delete the department \\\"{name}\\\"?\",\"department_added\":\"Department added successfully\",\"department_updated\":\"Department updated successfully\",\"department_deleted\":\"Department deleted successfully\",\"department_operation_failed\":\"Failed to {operation} department. Please try again.\",\"department_auth_error\":\"Authentication error. Please refresh the page and try again.\",\"department_unexpected_error\":\"An unexpected error occurred. Please try again.\",\"department_name_change_hint\":\"Change the department name to enable update\",\"cancel_department_edit\":\"Cancel\",\"final_assessment\":\"Final Assessment\",\"add_employees\":\"Add Employees\",\"employee_number\":\"Employee {number}\",\"remove_employee\":\"Remove employee\",\"first_name\":\"First Name\",\"last_name\":\"Last Name\",\"email\":\"Email\",\"department\":\"Department\",\"role\":\"Role\",\"order_of_interview\":\"Order of Interview\",\"enter_first_name\":\"Enter First Name\",\"enter_last_name\":\"Enter Last Name\",\"enter_email\":\"Enter Email\",\"select_department\":\"Select Department\",\"select_role\":\"Select Role\",\"enter_order_of_interview\":\"Enter order of interview\",\"loading_departments\":\"Loading departments...\",\"loading_roles\":\"Loading roles...\",\"add_another_employee\":\"+ Add Another Employee\",\"adding\":\"Adding...\",\"add_employees_button\":\"Add Employees\",\"employees_added\":\"Employees added successfully!\",\"failed_load_roles\":\"Failed to load roles. Please try again.\",\"failed_load_departments\":\"Failed to load departments. Please try again.\",\"failed_load_data\":\"Failed to load required data. Please refresh the page.\",\"failed_add_employees\":\"Failed to add employees\",\"unexpected_error\":\"An unexpected error occurred. Please try again.\",\"failed_role_operation\":\"Failed to update Role\",\"no_final_assessment_id_found_in_url\":\"Invalid Assessment\",\"failed_to_fetch_assessment_questions\":\"Failed to fetch assessment questions\",\"an_error_occurred_while_fetching_assessment_questions\":\"An error occurred while fetching assessment questions\",\"employee_management\":\"Employee Management\",\"employee_management_department\":\"Employee Management: {department}\",\"search_placeholder\":\"Search using name, user role\",\"search_department\":\"Search department\",\"employee_add_btn\":\"Add Employees\",\"update_order_of_interview\":\"Update Order of Interview\",\"name\":\"Name\",\"user_role\":\"User Role\",\"interview_order\":\"Order of Interview\",\"search_using_jobId_jobTitle\":\"Search using job id, job title\",\"no_more_jobs_to_fetch\":\"No more jobs to fetch.\",\"job_id\":\"Job ID\",\"posted_on\":\"Posted Date\",\"application_submitted\":\"Application Submitted\",\"actions\":\"Actions\",\"loading_employees\":\"Loading employees...\",\"failed_load_employees\":\"Failed to load employees. Please try again.\",\"error_fetching_employees\":\"Error fetching employees\",\"retry_employee_load\":\"Retry\",\"no_employees_found\":\"No employees found in this department.\",\"assign_interview\":\"Assign Interview\",\"remove\":\"Remove\",\"join_meeting\":\"Join Meeting\",\"copy_link\":\"Copy Link\",\"employee_role_updated_success\":\"Employee role updated successfully\",\"failed_update_employee_role\":\"Failed to update employee role\",\"error_updating_employee_role\":\"Error updating employee role\",\"access_management\":\"Access\",\"role_action_success\":\"Role {actionType} successfully\",\"management\":\"Management\",\"user_roles\":\"User Roles\",\"user_permissions\":\"User Permissions\",\"search_user_role\":\"Search using user role\",\"add_new_role\":\"Add New Role\",\"no_roles_found\":\"No roles found. Add a new role to get started.\",\"permission_counts\":\"Permission Counts\",\"last_modified\":\"Last Modified\",\"interview_details\":\"Interview Details\",\"edit\":\"Edit\",\"user_roles_alt\":\"User roles image\",\"assessment_link_copied_to_clipboard\":\"Assessment link copied to clipboard\",\"failed_to_copy_link\":\"Failed to copy link\",\"assessment_shared_successfully\":\"Assessment shared successfully\",\"failed_to_share_assessment\":\"Failed to share assessment\",\"an_error_occurred_while_sharing_assessment\":\"An error occurred while sharing assessment\",\"share_final_assessment\":\"Share Final Assessment\",\"you_can_share_the_final_assessment_link_to_the_candidate_for_them_to_take_the_test_remotely_they_won_t_be_able_to_see_the_correct_answers\":\"You can share the final assessment link to the candidate for them to take the test remotely. They won't be able to see the correct answers.\",\"assessment_link\":\"Assessment Link\",\"share_assessment\":\"Share Assessment\",\"sharing\":\"Sharing...\",\"add_role\":\"Add Role\",\"minutes\":\"Minutes\",\"edit_role\":\"Edit Role\",\"delete_role\":\"Delete Role\",\"role_name\":\"Role Name\",\"role_name_required\":\"please enter role name\",\"role_name_placeholder\":\"Enter role name\",\"role_description\":\"Role Description\",\"role_description_placeholder\":\"Enter role description\",\"role_description_required\":\"Role description is required\",\"role_delete_confirmation\":\"Are you sure you want to delete this role?\",\"role_delete_warning\":\"This action cannot be undone. All users with this role will lose their associated permissions.\",\"submit\":\"Submit\",\"delete\":\"Delete\",\"saving\":\"Saving...\",\"deleting\":\"Deleting...\",\"role_added_success\":\"Role added successfully\",\"role_updated_success\":\"Role updated successfully\",\"role_deleted_success\":\"Role deleted successfully\",\"failed_add_role\":\"Failed to add role\",\"failed_update_user_role\":\"Failed to update role\",\"failed_delete_role\":\"Failed to delete role\",\"failed_update_role\":\"Failed to update role\",\"error_updating_role\":\"Error updating role\",\"edit_permissions_for\":\"Edit Permissions for {roleName}\",\"loading_permissions\":\"Loading permissions...\",\"failed_load_permissions\":\"Failed to load permissions. Please try again.\",\"permissions_description\":\"Here, you can modify the access levels and permissions associated with this role to match current organizational needs or individual job responsibilities. Each permission is listed with a brief description to help you make informed decisions.\",\"role_permissions\":\"Role Permissions\",\"select_all\":\"Select All\",\"no_permissions_found\":\"No permissions found for this role.\",\"make_changes_to_enable_save\":\"Make changes to permissions to enable save\",\"at_least_one_permission\":\"At least one permission must be selected\",\"save_permissions\":\"Save Permissions\",\"permissions_updated_success\":\"Permissions updated successfully\",\"failed_update_permissions\":\"Failed to update permissions. Please try again.\",\"enter_role_name\":\"Enter role name\",\"role_name_req\":\"Please enter role name\",\"min_name\":\"Role name must be at least 2 characters\",\"max_name\":\"Role name cannot exceed 50 characters\",\"department_name_req\":\"Please enter department name\",\"email_is_required\":\"Please enter email\",\"please_enter_a_valid_email\":\"Please enter a valid email\",\"please_select_valid_role\":\"Please select valid roles\",\"please_select_valid_department\":\"Please select valid departments\",\"first_name_req\":\"Please enter first name\",\"last_name_req\":\"Please enter last name\",\"email_req\":\"Please enter email\",\"department_req\":\"Please select department\",\"role_req\":\"Please select role\",\"order_interview_req\":\"Please enter order of interview\",\"error_fetching_departments\":\"Error fetching departments. Please try again.\",\"question_added_successfully\":\"Question added successfully\",\"skill_not_found\":\"Skill not found\",\"an_unexpected_error_occurred_while_adding_the_question\":\"An unexpected error occurred while adding the question\",\"question_type\":\"Question Type\",\"multiple_choice_question\":\"Multiple Choice Question\",\"true_false\":\"True/False\",\"select_question_type\":\"Select question type\",\"options\":\"Options\",\"correct_answer\":\"Correct Answer\",\"adding_question\":\"Adding Question\",\"select_correct_answer\":\"Select correct answer\",\"enter_your_question\":\"Enter your question\",\"question_is_required\":\"Please enter question\",\"question_type_is_required\":\"Please select question type\",\"option_id_is_required\":\"Please enter option ID\",\"option_text_is_required\":\"Please enter option\",\"options_are_required\":\"Options are required\",\"at_least_2_options_are_required\":\"At least 2 options are required\",\"correct_answer_is_required\":\"Please select correct answer\",\"please_enter_valid_email\":\"Please enter valid email\",\"invalid_data\":\"Invalid data\",\"assessment_already_submitted\":\"Assessment already submitted\",\"max_career_based_questions_reached\":\"Max career based questions reached\",\"max_role_specific_questions_reached\":\"Max role specific questions reached\",\"max_culture_specific_questions_reached\":\"Max culture specific questions reached\",\"candidate_not_found\":\"Candidate not found\",\"job_not_found\":\"Job not found\",\"assessment_already_shared\":\"Assessment already shared\",\"assessment_already_exists\":\"Assessment already exists\",\"assessment_created\":\"Assessment created successfully\",\"assessment_not_found\":\"Assessment not found\",\"no_questions_found\":\"No questions found\",\"final_assessment_questions_created\":\"Final assessment questions created successfully\",\"assessment_questions_not_found\":\"Assessment questions not found\",\"assessment_questions_deleted\":\"Assessment questions deleted successfully\",\"failed_to_send_assessment_email\":\"Failed to send assessment email\",\"invalid_assessment_link\":\"Invalid assessment link\",\"invalid_question_ids\":\"Invalid question IDs\",\"assessment_status_fetched\":\"Assessment status fetched successfully\",\"email_mismatch\":\"Email mismatch\",\"unauthorized_access\":\"Unauthorized access\",\"generating_assessment_url\":\"Generating assessment URL...\",\"no_assessment_token_found_in_url\":\"No assessment token found in URL\",\"not_authenticated\":\"Oops! This link belongs to a different email address.\",\"employee_added\":\"Employee registered successfully\",\"employee_already_registered\":\"Employee already registered for your organization\",\"employee_already_registered_with_diff_org\":\"Employee registered with different organization\",\"failed_to_add_employee\":\"Failed to register employee\",\"cannot_add_employee_user_registered_with_another_org\":\"Employee already associated with another organization\",\"complete_assessment\":\"Complete Assessment\",\"next_skill_assessment\":\"Next Skill Assessment\",\"group\":\"Group\",\"skill\":\"Skill\",\"share_assessment_link_to_candidate\":\"Share Assessment Link To Candidate\",\"failed_to_generate_assessment_link\":\"Failed to generate assessment link\",\"token_req_msg\":\"Failed to generate assessment link\",\"otp_expired\":\"otp expired please request a new otp\",\"email_exist\":\"email exist\",\"unsupported_email\":\"unsupported email\",\"success\":\"success\",\"otp_verified\":\"Verified Successfully\",\"submitted\":\"submitted successfully\",\"password_not_updated\":\"password not updated\",\"password_update_failed\":\"password update failed\",\"profile_updated\":\"profile updated\",\"user_not_exist\":\"user not exist\",\"forgot_user\":\"email entered is not associated with any registered account\",\"failed\":\"something went wrong\",\"user_session_deleted\":\"user session deleted\",\"cannot_update_role\":\"Administrator role cannot be update\",\"wrong_current_pass\":\"wrong current pass\",\"email_not_found\":\"email not found\",\"email_not_verified\":\"email not verified\",\"try_again\":\"Something wrong try again\",\"reset_password\":\"Reset password\",\"reset_password_error\":\"Reset password error\",\"wrong_otp\":\"Please enter a valid verification code\",\"presigned_url_generated\":\"Presigned url generated\",\"fetch_success\":\"Fetch success\",\"fetch_failed\":\"Fetch failed\",\"invalidParams\":\"Invalid params\",\"job_fetch_success\":\"Job fetch success\",\"user_roles_fetch\":\"User roles fetch\",\"user_role_added\":\"User role added successfully\",\"user_role_updated\":\"User role updated successfully\",\"user_role_deleted\":\"User role deleted successfully\",\"role_already_exists\":\"Role already exists\",\"role_not_found\":\"Role not found\",\"role_permissions_fetch\":\"Role permissions fetch\",\"role_permissions_updated\":\"Role permissions updated\",\"at_least_one_permission_required\":\"Please select at least one permission\",\"add_failed\":\"Add failed\",\"update_failed\":\"Update failed\",\"delete_failed\":\"Delete failed\",\"final_assessment_created\":\"Final assessment created successfully\",\"questions_fetched\":\"Questions fetched successfully\",\"departments_fetch\":\"Departments fetch\",\"department_already_exists\":\"Department already exists\",\"department_not_found\":\"Department not found\",\"department_has_employees\":\"Cannot delete department with active employees\",\"departments_by_organization_fetch\":\"Departments by organization fetch\",\"employees_fetch\":\"Employees fetch\",\"employee_role_updated\":\"Employee role updated successfully\",\"employee_deleted\":\"Employee deleted successfully\",\"employee_not_found\":\"Employee not found\",\"email_already_exists\":\"Email already exists\",\"department_id_required\":\"Department is required\",\"name_required\":\"Candidate name is required\",\"organization_id_required\":\"Organization id required\",\"no_employees_data\":\"No employees data\",\"no_update_data_provided\":\"No update data provided\",\"skills_generated\":\"Skills generated successfully\",\"skills_generation_failed\":\"Skills generation failed! Please try again.\",\"job_requirement_generated\":\"Job requirement generated successfully\",\"job_requirement_generation_failed\":\"Job requirement generation failed! Please try again.\",\"job_details_saved\":\"Job details saved successfully\",\"job_details_save_failed\":\"Job details save failed! Please try again.\",\"job_update_success\":\"Job updated successfully\",\"job_update_failed\":\"Job update failed! Please try again.\",\"pdf_generated_success\":\"PDF generated successfully in downloads folder\",\"pdf_generation_failed\":\"PDF generation failed! Please try again.\",\"pdf_only\":\"Only PDF files are allowed.\",\"pdf_size\":\"File size exceeds the maximum limit of 10 MB.\",\"pdf_select\":\"Please select a PDF file first.\",\"pdf_invalid\":\"Failed to process the PDF: Invalid response format\",\"pdf_extract\":\"Failed to extract data from PDF\",\"interviewers\":\"Interviewers\",\"candidate_resume_\":\"Candidate Resume\",\"pdf_error\":\"An error occurred while processing the PDF\",\"upload_assessment\":\"Upload Assessment\",\"experience_must_be_number\":\"Experience must be a number or decimal\",\"experience_min_value\":\"Experience must be greater than 0\",\"experience_max_value\":\"Experience must be 50 years or less\",\"experience_max_length\":\"Experience must be at most 4 characters long\",\"title_alpha_only\":\"Title can only contain letters, spaces, and common special characters (/, &, ,, ., ', (), +, #, -, :)\",\"state_alpha_only\":\"State can only contain letters and spaces\",\"city_alpha_only\":\"City can only contain letters and spaces\",\"common\":{\"home\":\"Home\",\"hm_dashboard\":\"HM Dashboard\",\"job_requirement_generations\":\"Job Requirement Generations\"},\"dashboard\":{\"hiring_manager_dashboard\":\"Hiring Manager Dashboard\",\"jobs_created\":\"Jobs Created\",\"active_jobs\":\"Active Jobs\",\"upcoming_interviews\":\"Upcoming Interviews\",\"candidates_hired\":\"Candidates Hired\",\"resumes_on_hold\":\"Resumes on Hold\",\"scheduled_interviews\":\"Scheduled Interviews\",\"resume_on_hold\":\"Resume On-Hold\",\"create\":\"Create\",\"set_view_questionarie\":\"Set/View Questionnaire\",\"view_your_summary\":\"View Your Summary\",\"a_new_job\":\"A New Job\",\"screen_resumes\":\"Screen Resumes\",\"conduct\":\"Conduct\",\"interviews\":\"Interviews\",\"screen\":\"Screen\",\"resumes\":\"Resumes\",\"past_interviews\":\"Past Interviews\",\"search_placeholder\":\"Search using name, user role, department etc\",\"candidate_resume\":\"Candidate Resume\",\"no_interviews_found\":\"No interviews found\"},\"header\":{\"logout\":\"Logout\",\"session_expired\":\"Session Expired\",\"job_requirement_generations\":\"Job Requirement Generations\",\"resume_screening\":\"Resume Screening\",\"conduct_interview\":\"Conduct Interview\",\"settings\":\"Settings\",\"my_profile\":\"My Profile\"},\"jobRequirement\":{\"select_hiring_type\":\"Select Hiring Type\",\"hiring_type\":\"Hiring Type\",\"select_the_type_of_hiring_below_to_proceed_with_the_job_description_creation\":\"Select the type of hiring below to proceed with the job description creation.\",\"internal_hiring\":\"Internal Hiring\",\"external_hiring\":\"External Hiring\",\"craft_job_descriptions_for_internal_openings_and_promote_opportunities_within_your_organization\":\"Craft job descriptions for internal openings and promote opportunities within your organization.\",\"craft_job_descriptions_for_external_openings_and_promote_opportunities_within_your_organization\":\"Craft job descriptions for external openings and promote opportunities within your organization.\",\"attract_top_talent_by_creating_compelling_job_descriptions_for_external_candidates\":\"Attract top talent by creating compelling job descriptions for external candidates.\",\"continue\":\"Continue\",\"failed_to_update_job_discription\":\"Failed to update job description\",\"have_job_discription\":\"Have an existing job description document?\",\"extracted_data\":\"We've extracted details from your uploaded job description to fill the form. Fields highlighted below require additional input as the data wasn't found in the document.\",\"basic_details\":\"1. Basic Details\",\"job_title\":\"Job Title\",\"eneter_job_title\":\"Enter Job Title\",\"employment_type\":\"Employment Type\",\"select_employment_type\":\"Select Employment Type\",\"depeartment\":\" Department\",\"salary_range\":\"Salary Range\",\"enter_salary_range\":\"e.g. 50000 - 70000 ($ Will be added automatically)\",\"salsry_cycle\":\"Salary Cycle\",\"select_salary_cycle\":\"Select Salary Cycle\",\"job_location\":\"Job Location Type\",\"select_job_location\":\"Select Job Location\",\"select_location_type\":\"Select location Type\",\"state\":\"State\",\"enter_state\":\"Enter State\",\"city\":\"City\",\"enter_city\":\"Enter City\",\"role_overview\":\"2. Role Overview\",\"overview\":\"Overview\",\"overview_placeholder\":\"Tell us about this position in one or two sentences. (What's the purpose of this role? What Impact will It have on your team or organization?)\",\"experince_level_heading\":\"3. Experience Level\",\"experince_level\":\"Experience Level\",\"select_experience_level\":\"Select Experience Level\",\"key_responsibilities_heading\":\"4. Key Responsibilities\",\"key_responsibilities\":\"Key Responsibilities\",\"key_responsibilities_placeholder\":\"List the main tasks and responsibilities for this role. (Pro tip: Start with action verbs like Manage,Coordinate,Ensure,Track etc.)\",\"required_skills_heading\":\"5. Required Skills & Qualifications\",\"educational_requirments\":\"Educational Requirements\",\"educational_requirments_placeholder\":\"(e.g., Bachelor's Degree in Business Administration, High School Diploma, etc.)\",\"certifications\":\"Certifications\",\"optional\":\"(Optional)\",\"certifications_placeholder\":\"(e.g., OSHA Certification, PMP, Six Sigma)\",\"specfic_skills_or_software_knowledge\":\"Specific Skills or Software Knowledge\",\"specfic_skills_or_software_knowledge_placeholder\":\"(e.g., Microsoft Office, ERP Systems, Time Management, etc,)\",\"years_of_experince\":\" Years of Experience Needed\",\"years_of_experince_placeholder\":\"(e.g., 3 or 3.5)\",\"ideal_traits_heading\":\" 6. Ideal Candidate Traits\",\"ideal_traits\":\"Ideal Candidate Traits\",\"ideal_traits_placeholder\":\"Describe the qualities you're looking for in the ideal candidate (e.g., Detail-oriented, proactive, excellent communicator, team player)\",\"about_company_heading\":\"7. About the Company\",\"about_company\":\"About the Company\",\"about_company_placeholder\":\"Provide a brief description of your company, its mission, and values. (e.g., Company culture, work environment, etc.)\",\"job_benifits_heading\":\"8. Perks & Benefits \",\"job_benifits\":\"Job Benefits\",\"job_benifits_placeholder\":\"Let us know what perks and benefits you offer to make this job attractive: (e.g. Flexible schedule, healthcare benefits, 401 (k), professional development opportunities)\",\"tone_and_style_heading\":\"9. Tone and Style\",\"tone_and_style\":\"Tone and Style\",\"tone_and_style_placeholder\":\"Select Tone & Style\",\"additional_information_heading\":\"10. Additional Information\",\"additional_information\":\"Additional Information\",\"additional_information_placeholder\":\"Any other information you want to include in the job description? (e.g., travel requirements, remote work options, etc.)\",\"additional_info_heading\":\"10. Additional Info \",\"additional_info_placeholder\":\"Is there anything specific you'd like included in the job post or description?\",\"compliance_statement_heading\":\"11. Compliance Statement\",\"select_compliance_statement\":\"Choose Compliance Statements\",\"learn_more\":\"Learn More\",\"compliance_statement_placeholder\":\"Select compliance statements\",\"compliance_statement_part1\":\"By using these compliance statements, you acknowledge that Stratum 9 is not responsible for their accuracy or legal compliance and\",\"compliance_statement_part2\":\"waive any liability against the platform. \",\"no_job_requirment_generated\":\"No job requirement generated yet. Please generate job requirement first.\",\"job_requirment_generation\":\"Job Requirement Generations\",\"edit_job_description\":\"Edit Job Description for\",\"job_requirment_for\":\"Job Requirement for\",\"top_performance_based_skills\":\"Top Performance-Based Skills Required In \",\"generate_job_requirement\":\"Generate Job Requirement\"},\"apiResponse\":{\"dashboard_counts_fetch_success\":\"Dashboard counts fetched successfully\",\"dashboard_counts_fetch_failed\":\"Dashboard counts fetch failed\",\"no_pdf_file_uploaded\":\"No PDF file uploaded\",\"internal_server_error\":\"Internal server error\",\"job_description_processed_successfully\":\"Job description processed successfully\",\"failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf\":\"Failed to process the PDF file. Please ensure it's a valid PDF.\",\"failed_to_extract_form_fields\":\"Failed to extract form fields\",\"job_description_processed_failed\":\"Job description processed failed\",\"job_not_found\":\"Job not found\",\"failed_to_update_job\":\"Failed to update job\",\"jobs_meta_fetch_success\":\"Jobs metadata fetched successfully\",\"failed_to_fetch_jobs_meta\":\"Failed to fetch jobs metadata\",\"open_ai_key_not_configured\":\"OpenAI API key not configured\",\"failed_to_initialize_openai_client\":\"Failed to initialize OpenAI client\",\"open_ai_client_not_initilezed\":\"OpenAI client not initialized\"},\"careerBasedSkills\":{\"skill_name_required\":\"Skill name is required\",\"skill_description_required\":\"Description is required\",\"skill_name_length\":\"Skill name must be at least 3 characters long and cannot exceed 150 characters\",\"skill_description_length\":\"Description must be at least 3 characters long and cannot exceed 150 characters\",\"skill_description_max_length\":\"Skill description must be at most 200 characters\",\"skill_name_not_numeric\":\"Skill name cannot be numeric\",\"skill_description_not_numeric\":\"Skill description cannot be numeric\",\"skill_name_exists\":\"Skill name already exists\",\"skill_name_max_length\":\"Skill name must be at most 40 characters long\"},\"view_all_candidates\":\"View All Candidates\",\"screen_resume_manually\":\"Screen Resume Manually\",\"view_applicarion_source\":\"View Application Source\",\"archive_job\":\"Archive \",\"edit_job\":\"Edit\",\"no_jobs_found\":\"No jobs found\",\"add_new_job\":\"Add New Job\",\"candidate_name\":\"Candidate Name\",\"date_submitted\":\"Date Submitted\",\"source\":\"Source\",\"archived_on\":\"Archived On\",\"reason_for_archiving\":\"Reason for Archiving\",\"analyze_candidate_resume\":\"Analyze Candidate Resume\",\"no_more_candidates_to_fetch\":\"No more candidates to fetch.\",\"no_candidates_found\":\"No candidates found\",\"no_applications_found\":\"No applications found\",\"back_to_jobs\":\"Back to Jobs\",\"back_to_start\":\"Back to Start\",\"save_and_next\":\"Save and Next\",\"no_role_specific_skills_found\":\"No role-specific skills data available. Please generate job skills first.\",\"no_desc_available\":\"No description available\",\"top\":\"Top\",\"hm_dashboard\":\"Hiring Manager Dashboard\",\"performance_skills_part1\":\"Based on the details you provided and the finalized job description. We have selected the following Performance-based Skills critical to high performance in the role of\",\"performance_skills_part2\":\"must have. Below you will find the Performance-based Skills critical to both the role and your company's culture.\",\"performance_skills_part3\":\"You can edit skills at your discretion\",\"edit_skills\":\"Edit skills\",\"add_change_performance_based_skills\":\"Add or Change Performance-based Skills\",\"double_click_to_edit\":\"Double click on any skill card to see its definition, as it pertains to high performance.\",\"note\":\"Note: \",\"your_selection\":\"Your Selection\",\"already_selected\":\"Already Selected\",\"available_for_selection\":\"Available for Selection\",\"role_specific_skills\":\"Role-Specific Skills\",\"culture_specific_skills\":\"Culture-Specific Skills\",\"no_culture_specific_skills_found\":\"No culture-specific skills data available. Please generate job skills first.\",\"carrer_based_skills\":\"Career-Based Skills\",\"discription\":\"Description\",\"skill_name\":\"Skill Name\",\"save_cahnges\":\"Save Changes\",\"no_carrer_skills_available\":\"No career skills data available. Please generate job skills first.\",\"resume_screening\":\"Resume Screening\",\"application_for\":\"Application for\",\"active_jobs\":\"Active Jobs\",\"no_active_jobs_found\":\"No active jobs found\",\"approved_by_s9\":\"Approved By S-9 Inner View\",\"rejected_by_s9\":\"Rejected By S-9 Inner View\",\"reasons_for_good_match\":\"Reasons why they are a good match:\",\"approved_candidates\":\"Approve Candidate\",\"rejected_candidates\":\"Reject Candidate\",\"back_to_screening\":\"Back To Screening\",\"candidates_for\":\"Candidates For \",\"top_ten_candidates\":\"Top Candidates\",\"based_on_interview_date\":\"Based on interviews to date.\",\"lined_up_for\":\" Lined-Up For\",\"candidates_analysis\":\"Candidates Analysis\",\"other_candidates\":\"Other Candidates\",\"rancked_by_resume\":\"Ranked by Resume/Job Description Analysis\",\"add_candidates_info\":\"Add Candidates Info\",\"promote_candidate\":\"Promote Candidate\",\"demote_candidate\":\"Demote Candidate\",\"manual_upload_resume\":\"Manual Upload Resume For\",\"view_panding_action\":\"View Pending Actions\",\"current\":\"Current\",\"completed\":\"Completed\",\"behavioral\":\"Behavioral\",\"performance\":\"Performance\",\"additional\":\"Additional\",\"questions\":\"Questions\",\"role_specific_interview\":\"Role-Specific Interview\",\"culture_specific_interview\":\"Culture-Specific Interview\",\"career_based_skills_and_general_interview\":\"Career-Based Skills (Hard Skills) and General Interview\",\"gender\":\"Gender\",\"select_gender\":\"Please select gender\",\"upload_resume\":\"Upload Resume\",\"upload_assesment\":\"Upload Assessment\",\"additional_info_\":\"Additional Information\",\"additional_info_desc\":\"Is there anything specific you'd like included in the job post or description?\",\"your_notes\":\"Your Notes\",\"add_candidates_resume\":\"+ Add Another Candidate's Resume\",\"analyze\":\" Analyze\",\"candidate_answer\":\"Candidate Answer\",\"reset\":\"Reset\",\"manual_resume_upload_for\":\"Manual resume upload for\",\"operational_admin\":\"Operational Admin\",\"additional_information\":\"Enter additional information*\",\"candidates\":\"Candidates\",\"enter_additional_info_about_candidate\":\"Add any remarks, context, or observations about the candidate (Optional)\",\"confirm_delete_role\":\"Are you sure you want to delete this role?\",\"role_has_employees\":\"This role has employees assigned to it. Please remove the employees before deleting the role.\",\"update_interview_order\":\"Update Interview Order\",\"updating_order_for\":\"Updating order for\",\"enter_interview_order\":\"Enter interview order\",\"update_order\":\"Update Order\",\"update_interview_order_success\":\"Interview order updated successfully\",\"update_interview_order_error\":\"Failed to update interview order\",\"default_department_cannot_be_deleted\":\"Default department cannot be deleted\",\"add_new_department\":\"Add New Department\",\"no_departments_found\":\"No departments found. Add a new department to get started.\",\"same_as_current_order\":\"Same as current order\",\"employee_interview_order_updated\":\"Employee interview order updated successfully\",\"failed_update_interview_order\":\"Failed to update interview order\",\"invalid_sort_order\":\"Invalid sort order\",\"some_error_occurred\":\"Some error occurred\",\"generate_final_candidate_assessment\":\"Generate Final Candidate Assessment\",\"before_proceeding_please_review_the_candidates_overall_performance\":\"Before proceeding, please review the candidate's overall performance from the interview rounds, skills evaluation, and behavioral feedback. The final assessment will summarize the candidate's qualifications and overall fit for the role.\",\"final_assessment_warning_message\":\"Once the final assessment is generated for this candidate, they will no longer be eligible to participate in any additional interview rounds. If any interview round remains incomplete, please ensure that it is conducted before proceeding with the final assessment for this candidate.\",\"information_preview\":\"Information Preview\",\"interview_round_scores\":\"Interview Round Scores\",\"skills_evaluation\":\"Skills Evaluation\",\"behavioral_feedback\":\"Behavioral Feedback\",\"candidate_fit\":\"Candidate Fit\",\"ats_score\":\"ATS Score\",\"cultural_fit\":\"Cultural Fit\",\"qualifications\":\"Qualifications\",\"key_strengths_and_weaknesses\":\"Key Strengths and Weaknesses\",\"from_previous_rounds\":\"From Previous Rounds\",\"generate_final_assessment\":\"Generate Final Assessment\",\"back\":\"Back\",\"creating_final_assessment\":\"Creating Final Assessment\",\"title_required\":\"Job title is required\",\"title_min\":\"Title must be at least 3 characters\",\"title_max\":\"Title must be at most 50 characters\",\"employment_type_required\":\"Employment type is required\",\"salary_range_required\":\"Salary range is required\",\"salary_range_format\":\"Salary range must be in the format $40000 - $60000\",\"salary_cycle_required\":\"Salary cycle is required\",\"location_type_required\":\"Location type is required\",\"state_required\":\"State is required\",\"state_min\":\"State must be at least 3 characters\",\"state_max\":\"State must be at most 50 characters\",\"city_required\":\"City is required\",\"city_min\":\"City must be at least 3 characters\",\"city_max\":\"City must be at most 50 characters\",\"role_overview_required\":\"Overview is required\",\"role_overview_min\":\"Role overview must be at least 3 characters\",\"role_overview_max\":\"Role overview must be at most 150 characters\",\"experience_level_required\":\"Experience level is required\",\"responsibilities_required\":\"Responsibilities are required\",\"responsibilities_min\":\"Responsibilities must be at least 3 characters\",\"responsibilities_max\":\"Responsibilities must be at most 150 characters\",\"education_required\":\"Education requirements are required\",\"education_min\":\"Education requirements must be at least 3 characters\",\"education_max\":\"Education requirements must be at most 150 characters\",\"certifications_max\":\"Certifications must be at most 150 characters\",\"skills_required\":\"Software knowledge or skills is required\",\"skills_min\":\"Skills and software expertise must be at least 3 characters\",\"skills_max\":\"Skills and software expertise must be at most 150 characters\",\"experience_required_required\":\"Experience is required\",\"experience_required_min\":\"Experience required must be at least 3 characters\",\"experience_required_max\":\"Experience required must be at most 150 characters\",\"traits_required\":\"Candidate traits are required\",\"traits_min\":\"Ideal candidate traits must be at least 3 characters\",\"traits_max\":\"Ideal candidate traits must be at most 150 characters\",\"company_required\":\"Company description is required\",\"company_min\":\"About company must be at least 3 characters\",\"company_max\":\"About company must be at most 150 characters\",\"perks_max\":\"Perks and benefits must be at most 150 characters\",\"tone_required\":\"Tone & style is required\",\"additional_max\":\"Additional info must be at most 200 characters\",\"compliance_required\":\"Compliance statements are required\",\"compliance_min\":\"Please select at least one compliance statement\",\"compliance_type_error\":\"Please select at least one compliance statement\",\"show_compliance_required\":\"You must agree to the compliance statement disclaimer\",\"resume_required\":\"Upload resume is required\",\"resume_type\":\"Resume must be a PDF\",\"resume_size\":\"Resume size must be less than 5MB\",\"unsupported_file_type\":\"The selected file type is not supported. Please upload a PDF file.\",\"assessment_type\":\"Assessment must be a PDF\",\"assessment_size\":\"Assessment size must be less than 5MB\",\"name_min\":\"Name must be at least 2 characters\",\"name_alpha_only\":\"Numbers and special characters are not allowed\",\"email_required\":\"Email is required\",\"email_valid\":\"Candidate email must be a valid email\",\"gender_required\":\"Gender is required\",\"gender_valid\":\"Please select a valid gender\",\"at_least_one_candidate\":\"At least one candidate is required\",\"no_active_job_found\":\"No active job found.\",\"additional_info\":\"Additional Info\",\"resume_analysis\":\"Resume Analysis For\",\"cannot_update_default_department\":\"Administrator department cannot be update\",\"job_application_not_found\":\"Job application not found\",\"password\":\"Password\",\"forgot_password\":\"Forgot Password?\",\"login\":\"Login\",\"enter_your_email\":\"Enter your email\",\"enter_your_password\":\"Enter your password\",\"send_verification_code\":\"Send Verification Code\",\"back_to_login\":\"Back To Login\",\"hello\":\"Hello\",\"welcome_back\":\"Welcome\",\"forgot\":\"Forgot\",\"enter_verification_code\":\"Enter Verification Code\",\"verify\":\"Verify\",\"resend_otp\":\"Resend OTP\",\"code\":\"Code\",\"otp_verified_successfully\":\"OTP verified successfully\",\"resend_access_code_in\":\"Resend access code in\",\"seconds\":\"seconds\",\"new_password\":\"New Password\",\"confirm_password\":\"Confirm Password\",\"reset_password_success\":\"Your password has been reset successfully.\",\"reset_password_failed\":\"Failed to reset password. Please check your OTP or try again.\",\"new_password_required\":\"New password is required\",\"new_password_min\":\"New password must be at least 8 characters\",\"new_password_max\":\"New password must not exceed 20 characters\",\"confirm_password_required\":\"Confirm password is required\",\"confirm_password_mismatch\":\"Passwords do not match\",\"password_placeholder\":\"Enter new password\",\"confirm_password_placeholder\":\"Confirm new password\",\"reset_heading\":\"Reset\",\"password_heading\":\"Password\",\"archive\":\"Archive\",\"restore\":\"Restore\",\"you_dont_have_permission_to_view_archive_content\":\"You don't have permission to view archive content\",\"jobs\":\"Jobs\",\"pdf_parsing_failed\":\"Error in parsing PDF file\",\"job_requirements_generated\":\"Job requirements generated successfully\",\"profile_updated_successfully\":\"Profile updated successfully\",\"failed_to_update_profile\":\"Failed to update profile\",\"an_error_occurred_while_updating_profile\":\"An error occurred while updating profile\",\"edit_profile\":\"Edit Profile\",\"change_your_profile_details\":\"Change your profile details\",\"upload_picture\":\"Upload Picture\",\"save_changes\":\"Save Changes\",\"saving_changes\":\"Saving Changes\",\"organization_name\":\"Organization Name\",\"change_profile_picture\":\"Change Profile Picture\",\"error_uploading_image\":\"Error uploading image\",\"failed_to_upload_image\":\"Failed to upload image\",\"error_updating_profile\":\"Error updating profile\",\"last_name_required\":\"Please enter last name\",\"last_name_min\":\"Last name must be at least 2 characters\",\"last_name_max\":\"Last name must be at most 50 characters\",\"first_name_required\":\"Please enter first name\",\"first_name_min\":\"First name must be at least 2 characters\",\"first_name_max\":\"First name must be at most 50 characters\",\"organization_code\":\"Organization Code\",\"account_created_on\":\"Account Created On\",\"failed_to_load_profile\":\"Failed to load profile\",\"update\":\"Update\",\"picture\":\"Picture\",\"my_interviews\":\"My Interviews\",\"name_max\":\"Candidate name must be at most 50 characters\",\"email_max\":\"Candidate email must be at most 50 characters\",\"pdf_name\":\"File name must be at most 50 characters\",\"no_more_jobs_found\":\"No more active jobs found\",\"add_candidate\":\"Add Candidate\",\"additional_info_submission\":\"Additional Info Submission\",\"before_hiring\":\"Before Hiring\",\"additional_info_description\":\"You can provide additional information about the candidate below so that S9-Innerview can give a final verdict about the candidate being a right fit.\",\"view_candidate_info\":\"View Candidate Info\",\"additional_info_placeholder\":\"Enter additional information about the candidate\",\"additional_info_max_1000_chars\":\"Additional info must be at most 1000 characters long\",\"additional_info_min_10_chars\":\"Additional info must be at least 10 characters long\",\"candidates_uploaded_successfully\":\"Candidates uploaded successfully!\",\"promoted\":\"Promoted\",\"demoted\":\"Demoted\",\"additional_info_submitted_successfully\":\"Additional info submitted successfully\",\"upload_document\":\"Upload Document\",\"additional_info_required\":\"Additional info is required\",\"please_upload_a_document\":\"Please upload a document\",\"update_application_status_success\":\"Application status updated successfully\",\"update_application_status_failed\":\"Failed to update application status\",\"top_candidates_retrieved\":\"Top candidates retrieved successfully\",\"get_top_candidates_failed\":\"Failed to Get top candidates\",\"candidate_application_not_found\":\"Candidate application not found\",\"update_rank_status_failed\":\"Failed to update rank status\",\"update_rank_status_success\":\"Successfully updated rank status\",\"fetch_candidate_details_failed\":\"Failed to fetch candidate details\",\"candidate_not_found_for_org\":\"Candidate not found\",\"additional_info_saved\":\"Additional info saved successfully\",\"save_additional_info_failed\":\"Failed to save additional info\",\"unknown_error\":\"Unknown error occurred\",\"candidates_fetched\":\"Candidates fetched successfully\",\"get_all_candidates_failed\":\"Failed to get all candidates\",\"somthing_wrong\":\"Something went wrong\",\"updated_application_status_success\":\"Candidate status updated successfully\",\"additional_info_max_200_chars\":\"Additional info must be at most 200 characters long\",\"pdf_size_five_mb\":\"File size exceeds the maximum limit of 5 MB.\"}"));}}),

};
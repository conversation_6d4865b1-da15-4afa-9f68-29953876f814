{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/styles/commonPage.module.scss.module.css"], "sourcesContent": [".job_page .skills_info_box ul,.job_page .skills_info_box .skills_tab,.dashboard_page .dashboard_inner_head .header_tab{list-style:none;padding:0;margin:0}.dashboard__stats{background:#fff;border-radius:25px;padding:2.5rem;margin-bottom:28px;box-shadow:0 2px 9px rgba(0,0,0,.0901960784);display:flex;overflow:hidden}.dashboard__stats .dashboard__stat{display:flex;flex-direction:column;position:relative;width:20%;padding-left:1.2rem}.dashboard__stats .dashboard__stat::after{content:\"\";height:100%;position:absolute;right:0;top:0;width:1px;background-color:#e5e7eb;z-index:1;margin:auto 0}.dashboard__stats .dashboard__stat:first-child{padding-left:0}.dashboard__stats .dashboard__stat.border_none{padding-right:0}.dashboard__stats .dashboard__stat.border_none::after{display:none}.dashboard__stats .dashboard__stat .dashboard__stat_label{color:#333;font-size:1.4rem;font-weight:700;padding-bottom:1.2rem}.dashboard__stats .dashboard__stat .dashboard__stat_value{font-size:2rem;color:#436eb6;font-weight:700}.dashboard__stats .dashboard__stat_design{position:relative;min-width:135px}.dashboard__stats .dashboard__stat_design .dashboard__stat_image{width:230px;min-width:230px;height:140px;object-fit:contain;position:absolute;z-index:2;top:-30px;right:-30px}.job_page .job_info{border-radius:8px;background:rgba(67,110,182,.1);padding:5px 15px;font-size:1.4rem;font-weight:500;color:#333}.job_page .job_info.text_xs{font-size:1.3rem}.job_page .job_info a{color:#436eb6;text-decoration:underline !important;font-size:1.4rem}.job_page .section_heading{font-size:1.6rem;font-weight:700;color:#333;margin-bottom:16px}.job_page .section_heading span{color:#436eb6}.job_page .interview_form_icon{width:100%;height:260px;margin-top:-20px;padding:0px 20px}.job_page .inner_heading{font-size:2.4rem;font-weight:700;color:#333;margin-bottom:20px}.job_page .inner_heading span{color:#cb9932}.job_page .skills_info_box{display:flex;justify-content:space-between;align-items:center;margin-bottom:30px}.job_page .skills_info_box ul{display:flex;gap:4rem;align-items:center;margin-bottom:0}.job_page .skills_info_box ul li{font-size:1.4rem;font-weight:500;color:#333;display:flex;align-items:center;gap:1rem}.job_page .skills_info_box ul li span{width:20px;height:20px;border-radius:100%;display:block}.job_page .skills_info_box ul li span.selecting{background:#cb9932}.job_page .skills_info_box ul li span.selected{background:#436eb6}.job_page .skills_info_box ul li span.selection{background:rgba(67,110,182,.1)}.job_page .skills_info_box .skills_tab{display:flex;align-items:center;border:1px solid #333;border-radius:14px;display:inline-flex;overflow:hidden}.job_page .skills_info_box .skills_tab li{font-size:1.6rem;font-weight:500;color:#333;cursor:pointer;padding:8px 30px;position:relative;margin:0;text-align:center;min-width:155px}.job_page .skills_info_box .skills_tab li.active{position:relative;color:#fff;background:#436eb6}.job_page .career-skill-card{min-height:280px}.dashboard_page .dashboard_inner_head{display:flex;align-items:center;justify-content:space-between;margin-top:30px;margin-bottom:20px}.dashboard_page .dashboard_inner_head .header_tab{display:flex;align-items:center;border:1px solid #333;border-radius:14px;display:inline-flex;overflow:hidden}.dashboard_page .dashboard_inner_head .header_tab li{font-size:1.6rem;font-weight:500;color:#333;cursor:pointer;padding:8px 30px;position:relative;margin:0;text-align:center;min-width:155px}.dashboard_page .dashboard_inner_head .header_tab li.active{position:relative;color:#fff;background:#436eb6}.dashboard_page .dashboard_inner_head .search_box{width:35%}.resume_page.upload_resume_page .inner_page .operation_admins_img{width:100%;height:auto;object-fit:contain}.resume_page.manual_upload_resume .inner_page .input_type_file input{border:1px solid hsla(0,0%,100%,.6);padding:11px 15px;font-size:1.4rem;border-radius:12px;background:rgba(51,51,51,.05);color:#333;width:100%}.resume_page.manual_upload_resume .inner_page .candidate_card{border-radius:30px;border:2px solid rgba(0,0,0,.2);background:hsla(0,0%,100%,.05);padding:20px;margin-bottom:20px}.resume_page.manual_upload_resume .inner_page .candidate_card .candidate_card_header{margin-bottom:15px}.resume_page.manual_upload_resume .inner_page .candidate_card .candidate_card_header h3{color:#333;font-size:18px;font-style:normal;font-weight:700;line-height:normal;margin:0px}.resume_page.manual_upload_resume .inner_page .add_another_candidate_link{text-align:left}.resume_page.candidate_qualification_page .inner_page .approved_status_indicator{display:flex;gap:30px;margin-top:10px}.resume_page.candidate_qualification_page .inner_page .approved_status_indicator p{font-size:1.4rem;font-weight:500;color:#333;display:flex;align-items:center;gap:5px}.resume_page.candidate_qualification_page .inner_page .approved_status_indicator p span{background:rgba(229,190,83,.1);border:1px solid #e5be53;width:24px;min-width:24px;height:24px;border-radius:50%}.resume_page.candidate_qualification_page .inner_page .approved_status_indicator p:nth-child(2) span{background:rgba(208,0,0,.1);border:1px solid #d00000}.resume_page.candidates_list_page .candidates_list_section{margin-bottom:20px}.resume_page.candidates_list_page .candidates_list_section .section_name{margin-bottom:20px}.resume_page.candidates_list_page .candidates_list_section .section_name h3{font-size:22px;font-weight:700;color:#333;margin-bottom:6px}.resume_page.candidates_list_page .candidates_list_section .section_name p{font-size:16px;font-weight:500;color:#333;margin:0px}"], "names": [], "mappings": "AAAA;;;;;;AAA0J;;;;;;;;;;AAAiK;;;;;;;;AAAsH;;;;;;;;;;;;AAA4J;;;;AAA8D;;;;AAA+D;;;;AAAmE;;;;;;;AAA4H;;;;;;AAAuG;;;;;AAA4E;;;;;;;;;;;AAA+K;;;;;;;;;AAAkI;;;;AAA6C;;;;;;AAA0F;;;;;;;AAA0F;;;;AAA8C;;;;;;;AAAyF;;;;;;;AAAwF;;;;AAA4C;;;;;;;AAA4G;;;;;;;AAAuF;;;;;;;;;AAAsH;;;;;;;AAA8F;;;;AAAmE;;;;AAAkE;;;;AAA+E;;;;;;;;;AAAoJ;;;;;;;;;;;;AAAmL;;;;;;AAAiG;;;;AAA8C;;;;;;;;AAAuI;;;;;;;;;AAA+J;;;;;;;;;;;;AAA8L;;;;;;AAA4G;;;;AAA4D;;;;;;AAA4G;;;;;;;;;;AAAmN;;;;;;;;AAAgL;;;;AAAwG;;;;;;;;;AAAkL;;;;AAA0F;;;;;;AAAuH;;;;;;;;;AAAuK;;;;;;;;;AAAwM;;;;;AAA0J;;;;AAA8E;;;;AAA4F;;;;;;;AAAwI", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/node_modules/react-loading-skeleton/dist/skeleton.css"], "sourcesContent": ["@keyframes react-loading-skeleton {\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n.react-loading-skeleton {\n  --base-color: #ebebeb;\n  --highlight-color: #f5f5f5;\n  --animation-duration: 1.5s;\n  --animation-direction: normal;\n  --pseudo-element-display: block; /* Enable animation */\n\n  background-color: var(--base-color);\n\n  width: 100%;\n  border-radius: 0.25rem;\n  display: inline-flex;\n  line-height: 1;\n\n  position: relative;\n  user-select: none;\n  overflow: hidden;\n}\n\n.react-loading-skeleton::after {\n  content: ' ';\n  display: var(--pseudo-element-display);\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background-repeat: no-repeat;\n  background-image: var(\n    --custom-highlight-background,\n    linear-gradient(\n      90deg,\n      var(--base-color) 0%,\n      var(--highlight-color) 50%,\n      var(--base-color) 100%\n    )\n  );\n  transform: translateX(-100%);\n\n  animation-name: react-loading-skeleton;\n  animation-direction: var(--animation-direction);\n  animation-duration: var(--animation-duration);\n  animation-timing-function: ease-in-out;\n  animation-iteration-count: infinite;\n}\n\n@media (prefers-reduced-motion) {\n  .react-loading-skeleton {\n    --pseudo-element-display: none; /* Disable animation */\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;;;AA2BA;EACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_42d43a42._.js", {

"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = BackArrowIcon;
const __TURBOPACK__default__export__ = BackArrowIcon;
var _c;
__turbopack_context__.k.register(_c, "BackArrowIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/PreviewResumeIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function PreviewResumeIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "24",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.4419 1.28516V1.28613C19.546 1.28464 19.6494 1.30297 19.7456 1.34277L19.8169 1.37695C19.8854 1.41401 19.948 1.46142 20.0024 1.51758L27.5933 9.10742V9.1084L27.645 9.16602C27.7603 9.30714 27.8241 9.48419 27.8247 9.66797V27.8438C27.8247 28.605 27.5222 29.3347 26.9839 29.873C26.4456 30.4114 25.7159 30.7138 24.9546 30.7139H7.04346C6.37733 30.7139 5.73491 30.483 5.22412 30.0645L5.01416 29.873C4.47584 29.3347 4.17334 28.6051 4.17334 27.8438V4.15527C4.17338 3.39401 4.47586 2.66428 5.01416 2.12598L5.22412 1.93457C5.7349 1.5161 6.37736 1.28517 7.04346 1.28516H19.4419ZM7.04541 2.87012C6.7472 2.87016 6.45962 2.97377 6.23096 3.16113L6.13721 3.24707C5.89621 3.48807 5.76029 3.81446 5.76025 4.15527V27.8438C5.76026 28.1846 5.89617 28.5119 6.13721 28.7529L6.23096 28.8379C6.45963 29.0253 6.74717 29.1289 7.04541 29.1289H24.9546L25.0806 29.123C25.2063 29.1109 25.3296 29.0796 25.4468 29.0312C25.6029 28.9668 25.7452 28.8723 25.8647 28.7529C25.9842 28.6336 26.0794 28.4919 26.144 28.3359L26.186 28.2168C26.2227 28.096 26.2417 27.9704 26.2417 27.8438V10.4609H21.1753C20.5059 10.4609 19.8635 10.195 19.3901 9.72168C18.9168 9.2483 18.6509 8.60595 18.6509 7.93652V2.87012H7.04541ZM20.2349 7.93652C20.2349 8.18554 20.3332 8.4245 20.5093 8.60059L20.5786 8.66309C20.7456 8.79983 20.9556 8.87597 21.1733 8.87598H25.1196L20.2349 3.99121V7.93652Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12.3069 6.4873C12.7821 6.33047 13.2913 6.29936 13.7844 6.39746L13.9934 6.44727C14.4755 6.58088 14.9162 6.83691 15.2717 7.19238L15.4182 7.34961C15.7449 7.72854 15.9685 8.18664 16.0667 8.67969L16.1008 8.8916C16.162 9.38833 16.0941 9.89378 15.9016 10.3584C15.7093 10.8228 15.4003 11.2282 15.0061 11.5361L14.8313 11.6621C14.3537 11.9812 13.7924 12.1513 13.218 12.1514H13.217C12.5433 12.1504 11.8938 11.9165 11.3772 11.4932L11.1643 11.2998C10.6198 10.7553 10.3138 10.0171 10.3127 9.24707L10.3206 9.03223C10.3575 8.5332 10.5227 8.05091 10.802 7.63281L10.928 7.45801C11.2359 7.06383 11.6414 6.75485 12.1057 6.5625L12.3069 6.4873ZM13.0872 7.93359C12.7852 7.964 12.5009 8.097 12.2844 8.31348C12.037 8.56093 11.8982 8.89713 11.8977 9.24707L11.9124 9.44043C11.9409 9.63256 12.0116 9.81665 12.1204 9.97949L12.2395 10.1338C12.3699 10.2775 12.5314 10.3909 12.7122 10.4658L12.8977 10.5273C13.0859 10.5743 13.283 10.5791 13.4749 10.541L13.6624 10.4893C13.8453 10.4238 14.0121 10.3182 14.1506 10.1797C14.3352 9.99509 14.461 9.75994 14.512 9.50391L14.5364 9.31055C14.5426 9.18148 14.5296 9.05212 14.4983 8.92676L14.4368 8.74121C14.3618 8.56046 14.2485 8.39894 14.1047 8.26855L13.9504 8.14941C13.7335 8.00453 13.4788 7.92685 13.218 7.92676L13.0872 7.93359Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M14.0181 12.252L14.2183 12.2568C15.2193 12.3076 16.1692 12.7274 16.8813 13.4395L17.02 13.585C17.693 14.328 18.0686 15.2965 18.0698 16.3037V16.7266C18.0698 16.9104 18.0056 17.0875 17.8901 17.2285L17.8374 17.2871C17.6888 17.4356 17.487 17.5186 17.2769 17.5186C17.0931 17.5185 16.9158 17.4552 16.7749 17.3398L16.7163 17.2871C16.5677 17.1385 16.4849 16.9367 16.4849 16.7266V16.3037L16.4722 16.0605C16.4236 15.5765 16.2328 15.1174 15.9243 14.7412L15.7612 14.5605C15.3566 14.1559 14.8257 13.9064 14.2612 13.8496L14.0171 13.8369H12.4175C11.8453 13.8377 11.2937 14.0369 10.855 14.3965L10.6743 14.5605C10.2119 15.023 9.95162 15.6497 9.95068 16.3037V16.7266C9.95062 16.9104 9.8865 17.0875 9.771 17.2285L9.71826 17.2871C9.56964 17.4356 9.36781 17.5186 9.15771 17.5186C8.97399 17.5185 8.7967 17.4552 8.65576 17.3398L8.59717 17.2871C8.44859 17.1385 8.3658 16.9367 8.36572 16.7266V16.3037L8.37061 16.1025C8.42148 15.1015 8.84113 14.1515 9.55322 13.4395L9.69873 13.3018C10.4418 12.6286 11.4101 12.2532 12.4175 12.252H14.0181Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M17.1772 21.002L17.2554 21.0059C17.4369 21.0238 17.6077 21.1034 17.7378 21.2334C17.8864 21.382 17.9701 21.5838 17.9702 21.7939C17.9702 22.0041 17.8864 22.2058 17.7378 22.3545C17.6076 22.4847 17.437 22.5651 17.2554 22.583L17.1772 22.5869H8.77197C8.58813 22.5869 8.411 22.5227 8.27002 22.4072L8.21143 22.3545C8.06294 22.2059 7.97998 22.004 7.97998 21.7939C7.98005 21.5838 8.06284 21.382 8.21143 21.2334L8.27002 21.1807C8.41096 21.0653 8.58824 21.002 8.77197 21.002H17.1772Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21.9321 24.6504C22.1422 24.6504 22.3441 24.7334 22.4927 24.8818L22.5454 24.9404C22.6609 25.0814 22.725 25.2585 22.7251 25.4424C22.7251 25.6526 22.6413 25.8543 22.4927 26.0029C22.344 26.1516 22.1423 26.2354 21.9321 26.2354H8.77197C8.58813 26.2353 8.411 26.1712 8.27002 26.0557L8.21143 26.0029C8.06294 25.8543 7.97998 25.6525 7.97998 25.4424C7.98005 25.2323 8.06284 25.0304 8.21143 24.8818L8.27002 24.8291C8.41096 24.7137 8.58824 24.6505 8.77197 24.6504H21.9321Z",
                stroke: "#436EB6",
                strokeWidth: "0.2"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/PreviewResumeIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = PreviewResumeIcon;
const __TURBOPACK__default__export__ = PreviewResumeIcon;
var _c;
__turbopack_context__.k.register(_c, "PreviewResumeIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
_c = InputWrapper;
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
var _c;
__turbopack_context__.k.register(_c, "InputWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
;
function Textarea({ control, name, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
        control: control,
        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                ...props,
                value: field.value,
                onChange: field.onChange,
                "aria-label": ""
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Textarea.tsx",
                lineNumber: 13,
                columnNumber: 30
            }, void 0),
        name: name,
        defaultValue: ""
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/Textarea.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
}
_c = Textarea;
var _c;
__turbopack_context__.k.register(_c, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/ArrowDownIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function ArrowDownIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "15",
        height: "12",
        viewBox: "0 0 18 11",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M2 9L9 2L16 9",
            strokeWidth: "2.5",
            strokeLinecap: "round",
            strokeLinejoin: "round"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/ArrowDownIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/ArrowDownIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = ArrowDownIcon;
const __TURBOPACK__default__export__ = ArrowDownIcon;
var _c;
__turbopack_context__.k.register(_c, "ArrowDownIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/conductInterview/ProgressTracker.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
const ProgressTracker = ({ isRecording })=>{
    _s();
    // Initialize progress state
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const [progressState, setProgressState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        career: {
            totalSkills: 0,
            completedSkills: 0
        },
        culture: {
            totalSkills: 0,
            totalQuestions: 0,
            completedQuestions: 0,
            skillProgress: {}
        },
        role: {
            totalSkills: 0,
            totalQuestions: 0,
            completedQuestions: 0,
            skillProgress: {}
        }
    });
    // Add state for tracking recording time
    const [elapsedTime, setElapsedTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Get interview data from Redux store
    const interviewData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "ProgressTracker.useSelector[interviewData]": (state)=>state.interview
    }["ProgressTracker.useSelector[interviewData]"]);
    // Calculate progress percentages
    const careerProgress = progressState.career.totalSkills > 0 ? progressState.career.completedSkills / progressState.career.totalSkills * 100 : 0;
    const cultureProgress = progressState.culture.totalQuestions > 0 ? progressState.culture.completedQuestions / progressState.culture.totalQuestions * 100 : 0;
    const roleProgress = progressState.role.totalQuestions > 0 ? progressState.role.completedQuestions / progressState.role.totalQuestions * 100 : 0;
    // Calculate overall progress (average of the three sections)
    const overallProgress = (careerProgress + cultureProgress + roleProgress) / 3;
    // Update progress state based on interview data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProgressTracker.useEffect": ()=>{
            if (!interviewData) return;
            const newProgressState = {
                career: {
                    totalSkills: 0,
                    completedSkills: 0
                },
                culture: {
                    totalSkills: 0,
                    totalQuestions: 0,
                    completedQuestions: 0,
                    skillProgress: {}
                },
                role: {
                    totalSkills: 0,
                    totalQuestions: 0,
                    completedQuestions: 0,
                    skillProgress: {}
                }
            };
            // Process career-based questions
            if (interviewData.careerBasedQuestions) {
                const careerSkills = Object.keys(interviewData.careerBasedQuestions);
                newProgressState.career.totalSkills = careerSkills.length;
                // Count completed skills (a skill is completed if it has at least one answered question)
                newProgressState.career.completedSkills = careerSkills.filter({
                    "ProgressTracker.useEffect": ()=>{
                        const questions = interviewData.careerBasedQuestions.questions || [];
                        return questions.some({
                            "ProgressTracker.useEffect": (q)=>q.answer && q.answer.trim() !== ""
                        }["ProgressTracker.useEffect"]);
                    }
                }["ProgressTracker.useEffect"]).length;
            }
            // Process culture-based questions
            if (interviewData.cultureSpecificQuestions) {
                const cultureSkills = Object.keys(interviewData.cultureSpecificQuestions);
                newProgressState.culture.totalSkills = cultureSkills.length;
                let completedQuestions = 0;
                let totalQuestions = 0;
                cultureSkills.forEach({
                    "ProgressTracker.useEffect": (skill)=>{
                        const questions = interviewData.cultureSpecificQuestions[skill].questions || [];
                        totalQuestions += questions.length;
                        const skillCompletedQuestions = questions.filter({
                            "ProgressTracker.useEffect": (q)=>q.answer && q.answer.trim() !== ""
                        }["ProgressTracker.useEffect"]).length;
                        completedQuestions += skillCompletedQuestions;
                        newProgressState.culture.skillProgress[skill] = skillCompletedQuestions;
                    }
                }["ProgressTracker.useEffect"]);
                newProgressState.culture.totalQuestions = totalQuestions;
                newProgressState.culture.completedQuestions = completedQuestions;
            }
            // Process role-based questions
            if (interviewData.roleSpecificQuestions) {
                const roleSkills = Object.keys(interviewData.roleSpecificQuestions);
                newProgressState.role.totalSkills = roleSkills.length;
                let completedQuestions = 0;
                let totalQuestions = 0;
                roleSkills.forEach({
                    "ProgressTracker.useEffect": (skill)=>{
                        const questions = interviewData.roleSpecificQuestions[skill].questions || [];
                        totalQuestions += questions.length;
                        const skillCompletedQuestions = questions.filter({
                            "ProgressTracker.useEffect": (q)=>q.answer && q.answer.trim() !== ""
                        }["ProgressTracker.useEffect"]).length;
                        completedQuestions += skillCompletedQuestions;
                        newProgressState.role.skillProgress[skill] = skillCompletedQuestions;
                    }
                }["ProgressTracker.useEffect"]);
                newProgressState.role.totalQuestions = totalQuestions;
                newProgressState.role.completedQuestions = completedQuestions;
            }
            // Update state and save to localStorage
            setProgressState(newProgressState);
            localStorage.setItem("interviewProgress", JSON.stringify(newProgressState));
        }
    }["ProgressTracker.useEffect"], [
        interviewData
    ]);
    // Add timer functionality when recording starts/stops
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProgressTracker.useEffect": ()=>{
            let timerId = null;
            if (isRecording) {
                // Start the timer when recording begins
                timerId = setInterval({
                    "ProgressTracker.useEffect": ()=>{
                        setElapsedTime({
                            "ProgressTracker.useEffect": (prevTime)=>prevTime + 1
                        }["ProgressTracker.useEffect"]);
                    }
                }["ProgressTracker.useEffect"], 1000);
            } else {
                // Reset the timer when recording stops
                setElapsedTime(0);
            }
            // Cleanup the interval when component unmounts or isRecording changes
            return ({
                "ProgressTracker.useEffect": ()=>{
                    if (timerId) clearInterval(timerId);
                }
            })["ProgressTracker.useEffect"];
        }
    }["ProgressTracker.useEffect"], [
        isRecording
    ]);
    // Format the elapsed time as HH:MM:SS
    const formatTime = (timeInSeconds)=>{
        const hours = Math.floor(timeInSeconds / 3600);
        const minutes = Math.floor(timeInSeconds % 3600 / 60);
        const seconds = timeInSeconds % 60;
        return [
            hours,
            minutes,
            seconds
        ].map((val)=>val.toString().padStart(2, "0")).join(":");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "progress-container",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "d-flex justify-content-between align-items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "time",
                        children: formatTime(elapsedTime)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                        lineNumber: 182,
                        columnNumber: 9
                    }, this),
                    isRecording ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "status",
                        children: t("recording_in_progress")
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                        lineNumber: 183,
                        columnNumber: 24
                    }, this) : null
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                lineNumber: 181,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "progress-tracker",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bar-container",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bar",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "progress",
                                    style: {
                                        width: `${overallProgress}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 188,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "marker active",
                                    style: {
                                        left: "0%",
                                        opacity: 0
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 189,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "marker active",
                                    style: {
                                        left: "33.3333%"
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 190,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "marker active",
                                    style: {
                                        left: "66.6667%"
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "marker",
                                    style: {
                                        left: "100%",
                                        opacity: 0
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 192,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                            lineNumber: 187,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "labels",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "label",
                                    style: {
                                        left: "0%"
                                    },
                                    children: t("career_based") + t("interview_")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 195,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "label",
                                    style: {
                                        left: "33.3333%"
                                    },
                                    children: t("role_based") + t("interview_")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 198,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "label",
                                    style: {
                                        left: "66.6667%"
                                    },
                                    children: t("culture_based") + t("interview_")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 201,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "label",
                                    style: {
                                        left: "100%"
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                                    lineNumber: 204,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                            lineNumber: 194,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                    lineNumber: 186,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
                lineNumber: 185,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/views/conductInterview/ProgressTracker.tsx",
        lineNumber: 180,
        columnNumber: 5
    }, this);
};
_s(ProgressTracker, "hz5YSsft0fCOwwjRNB40yxaDn34=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"]
    ];
});
_c = ProgressTracker;
const __TURBOPACK__default__export__ = ProgressTracker;
var _c;
__turbopack_context__.k.register(_c, "ProgressTracker");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/RecIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function RecIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "20",
        height: "20",
        viewBox: "0 0 32 32",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                clipPath: "url(#clip0_9893_7870)",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M16 1C7.72656 1 1 7.72656 1 16C1 24.2734 7.72656 31 16 31C24.2734 31 31 24.2734 31 16C31 7.72656 24.2734 1 16 1ZM16 30.0625C8.24805 30.0625 1.9375 23.752 1.9375 16C1.9375 8.24805 8.24805 1.9375 16 1.9375C23.752 1.9375 30.0625 8.24805 30.0625 16C30.0625 23.752 23.752 30.0625 16 30.0625ZM14.6992 13.5684V15.5312H18.0508C18.3086 15.5312 18.5195 15.7422 18.5195 16C18.5195 16.2578 18.3086 16.4688 18.0508 16.4688H14.6992V18.4316H18.0508C18.3086 18.4316 18.5195 18.6426 18.5195 18.9004C18.5195 19.1582 18.3086 19.3691 18.0508 19.3691H14.2246C13.9668 19.3691 13.7559 19.1582 13.7559 18.9004V13.0996C13.7559 12.8418 13.9668 12.6309 14.2246 12.6309H18.0508C18.3086 12.6309 18.5195 12.8418 18.5195 13.0996C18.5195 13.3574 18.3086 13.5684 18.0508 13.5684H14.6992ZM20.5586 14.7344V17.2598C20.5586 17.9043 21.0801 18.4258 21.7246 18.4258H22.2812C22.9141 18.4258 23.4355 17.9102 23.4473 17.2832C23.4531 17.0254 23.6641 16.8145 23.9277 16.8203C24.1855 16.8262 24.3906 17.0371 24.3906 17.3008C24.3672 18.4375 23.4238 19.3633 22.2871 19.3633H21.7305C20.5703 19.3633 19.627 18.4199 19.627 17.2598V14.7344C19.627 13.5742 20.5703 12.6309 21.7305 12.6309H22.2871C23.4238 12.6309 24.3672 13.5566 24.3906 14.6934C24.3965 14.9512 24.1914 15.168 23.9277 15.1738C23.6641 15.1797 23.4531 14.9746 23.4473 14.7109C23.4355 14.0781 22.9141 13.5684 22.2812 13.5684H21.7246C21.0801 13.5684 20.5586 14.0957 20.5586 14.7344ZM10.4336 12.6309H8.08984C7.83203 12.6309 7.62109 12.8418 7.62109 13.0996V16.4277V18.8945C7.62109 19.1523 7.83203 19.3633 8.08984 19.3633C8.34766 19.3633 8.55859 19.1523 8.55859 18.8945V16.8965H9.29688L11.0195 19.0938C11.1133 19.2109 11.248 19.2754 11.3887 19.2754C11.4883 19.2754 11.5938 19.2402 11.6758 19.1758C11.8809 19.0176 11.916 18.7188 11.7578 18.5195L10.4863 16.9023C11.6406 16.873 12.5664 15.9297 12.5664 14.7695C12.5723 13.5918 11.6113 12.6309 10.4336 12.6309ZM10.4336 15.9648H8.55859V13.5742H10.4336C11.0957 13.5742 11.6289 14.1074 11.6289 14.7695C11.6289 15.4258 11.0957 15.9648 10.4336 15.9648Z",
                    strokeWidth: "0.6"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
                    lineNumber: 7,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_9893_7870",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "32",
                        height: "32",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
                        lineNumber: 14,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
                    lineNumber: 13,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/RecIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = RecIcon;
const __TURBOPACK__default__export__ = RecIcon;
var _c;
__turbopack_context__.k.register(_c, "RecIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/LetterFoldIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function LetterFoldIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "50",
        height: "66",
        viewBox: "0 0 50 66",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M0 0.580553L49.5778 0.224609V65.0572L0 0.580553Z",
            fill: "black",
            fillOpacity: "0.1"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/LetterFoldIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/LetterFoldIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = LetterFoldIcon;
const __TURBOPACK__default__export__ = LetterFoldIcon;
var _c;
__turbopack_context__.k.register(_c, "LetterFoldIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/conductInterview.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "additional": "conductInterview-module-scss-module__yztraq__additional",
  "completed": "conductInterview-module-scss-module__yztraq__completed",
  "conduct_interview": "conductInterview-module-scss-module__yztraq__conduct_interview",
  "conduct_interview_page": "conductInterview-module-scss-module__yztraq__conduct_interview_page",
  "current": "conductInterview-module-scss-module__yztraq__current",
  "question_info_box": "conductInterview-module-scss-module__yztraq__question_info_box",
  "summary_card_height": "conductInterview-module-scss-module__yztraq__summary_card_height",
});
}}),
"[project]/src/services/interviewServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addInterviewSkillQuestion": (()=>addInterviewSkillQuestion),
    "conductInterviewStaticInformation": (()=>conductInterviewStaticInformation),
    "endInterview": (()=>endInterview),
    "getCandidateList": (()=>getCandidateList),
    "getInterviewSkillQuestions": (()=>getInterviewSkillQuestions),
    "getInterviewers": (()=>getInterviewers),
    "getInterviews": (()=>getInterviews),
    "getJobList": (()=>getJobList),
    "getMyInterviews": (()=>getMyInterviews),
    "upcomigOrPastInterview": (()=>upcomigOrPastInterview),
    "updateInterviewAnswers": (()=>updateInterviewAnswers),
    "updateInterviewSkillQuestion": (()=>updateInterviewSkillQuestion),
    "updateOrScheduleInterview": (()=>updateOrScheduleInterview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
;
;
const updateOrScheduleInterview = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);
};
const getInterviews = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEWS, data);
};
const getInterviewers = (searchString, jobId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEWERS, {
        searchString,
        jobId
    });
};
const upcomigOrPastInterview = (params)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_UPCOMING_OR_PAST_INTERVIEW, {
        ...params
    });
};
const getMyInterviews = (monthYear)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_MY_INTERVIEWS, {
        monthYear
    });
};
const getInterviewSkillQuestions = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_INTERVIEW_SKILL_QUESTIONS, data);
};
const updateInterviewSkillQuestion = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);
};
const addInterviewSkillQuestion = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.ADD_INTERVIEW_SKILL_QUESTION, data);
};
const getJobList = (searchString)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_JOB_LIST, {
        searchString
    });
};
const getCandidateList = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.GET_CANDIDATE_LIST, data);
};
const updateInterviewAnswers = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.UPDATE_INTERVIEW_ANSWERS, data);
};
const endInterview = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.END_INTERVIEW, data);
};
const conductInterviewStaticInformation = ()=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const ModalCloseIcon = (props)=>{
    const { className } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "40",
        height: "41",
        viewBox: "0 0 40 41",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "20.0003",
                cy: "20.5",
                r: "18.209",
                fill: "white"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 5,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z",
                fill: "#333333"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/ModalCloseIcon.tsx",
        lineNumber: 4,
        columnNumber: 5
    }, this);
};
_c = ModalCloseIcon;
const __TURBOPACK__default__export__ = ModalCloseIcon;
var _c;
__turbopack_context__.k.register(_c, "ModalCloseIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/commonModals/EndInterViewModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ModalCloseIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const EndInterViewModal = ({ onClickCancel, onClickEndInterview, disabled })=>{
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header justify-content-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: t("end_interview") + "?"
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                                lineNumber: 20,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "textMd w100",
                                children: t("end_interview_desc")
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                                lineNumber: 21,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "modal-close-btn",
                                onClick: onClickCancel,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ModalCloseIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                                    lineNumber: 23,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                                lineNumber: 22,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                        lineNumber: 19,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body pt-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "action-btn",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "primary-btn rounded-md w-100",
                                onClick: onClickEndInterview,
                                disabled: disabled,
                                children: t("end_interview")
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                                lineNumber: 28,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                            lineNumber: 27,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
                lineNumber: 18,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/EndInterViewModal.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
};
_s(EndInterViewModal, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = EndInterViewModal;
const __TURBOPACK__default__export__ = EndInterViewModal;
var _c;
__turbopack_context__.k.register(_c, "EndInterViewModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/validations/interviewValidations.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addAnswerValidation": (()=>addAnswerValidation),
    "addUpdateQuestionValidation": (()=>addUpdateQuestionValidation),
    "scheduleInterviewValidation": (()=>scheduleInterviewValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yup/index.esm.js [app-client] (ecmascript)");
;
const scheduleInterviewValidation = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])({
        eventTitle: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("title_req")).min(5, translation("title_min_5_chars")).max(100, translation("title_max_100_chars")),
        jobTitle: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["number"])().required(translation("job_title_req")),
        interviewType: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("interview_type_req")),
        jobId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("job_id_req")),
        date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("date_req")).test("is-not-in-past", translation("cannot_schedule_interview_past"), function(dateStr) {
            if (!dateStr) return true;
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const inputDate = new Date(dateStr);
            inputDate.setHours(0, 0, 0, 0);
            return inputDate >= today;
        }).test("not-more-than-month-in-advance", translation("cannot_schedule_more_than_one_month_in_advance"), function(dateStr) {
            if (!dateStr) return true;
            const oneMonthFromNow = new Date();
            oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
            oneMonthFromNow.setHours(0, 0, 0, 0);
            const inputDate = new Date(dateStr);
            inputDate.setHours(0, 0, 0, 0);
            return inputDate <= oneMonthFromNow;
        }),
        startTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("start_time_req")).matches(/^([01]\d|2[0-3]):([0-5]\d)$/, translation("invalid_time_format")),
        description: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().nullable().optional().max(2000, translation("description_max_2000_chars")),
        endTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("end_time_req")).matches(/^([01]\d|2[0-3]):([0-5]\d)$/, translation("invalid_time_format")).test("is-greater-than-start-time", translation("end_time_must_be_after_start_time"), function(endTime) {
            const { startTime } = this.parent;
            if (!startTime || !endTime) return true;
            // Compare times
            return endTime > startTime;
        }).test("is-at-least-30-min", translation("interview_must_be_at_least_10_min"), function(endTime) {
            const { startTime } = this.parent;
            if (!startTime || !endTime) return true;
            // Parse hours and minutes
            const [startHour, startMin] = startTime.split(":").map(Number);
            const [endHour, endMin] = endTime.split(":").map(Number);
            // Calculate total minutes
            const startTotalMins = startHour * 60 + startMin;
            const endTotalMins = endHour * 60 + endMin;
            const diffMins = endTotalMins - startTotalMins;
            // Ensure at least 30 minutes difference
            return diffMins >= 30;
        }).test("max-duration", translation("interview_must_not_exceed_2_hours"), function(endTime) {
            const { startTime } = this.parent;
            if (!startTime || !endTime) return true;
            // Parse hours and minutes
            const [startHour, startMin] = startTime.split(":").map(Number);
            const [endHour, endMin] = endTime.split(":").map(Number);
            // Calculate total minutes
            const startTotalMins = startHour * 60 + startMin;
            const endTotalMins = endHour * 60 + endMin;
            const diffMins = endTotalMins - startTotalMins;
            // Ensure interview is no longer than 2 hours (120 minutes)
            return diffMins <= 120;
        }),
        interviewer: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["number"])().required(translation("interviewer_req")),
        candidate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["number"])().test("job-title-required", translation("please_select_job_title_first"), function() {
            const { jobTitle } = this.parent;
            return !!jobTitle && jobTitle > 0;
        }).required(translation("candidate_req"))
    });
const addUpdateQuestionValidation = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        question: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(translation("question_req")).max(500, translation("question_max_500_chars"))
    });
const addAnswerValidation = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        behavioralInfo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().optional().max(1500, translation("behavioral_info_max_1500_chars"))
    }).test("dynamic-answer-validation", "", function(values) {
        console.log("values========", values);
        // Get all field names from the form values
        const fieldNames = Object.keys(values || {});
        // Create a new schema object to add dynamic validations
        let dynamicSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({});
        // Add validation for each answer field
        fieldNames.forEach((fieldName)=>{
            if (fieldName.startsWith("answer-")) {
                // Add validation for this specific answer field
                dynamicSchema = dynamicSchema.shape({
                    [fieldName]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().optional().max(2000, translation("answer_max_2000_chars"))
                });
            }
        });
        try {
            dynamicSchema.validateSync(values, {
                abortEarly: false
            });
            return true;
        } catch (error) {
            console.log("error", error);
            return false;
        }
    });
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/conductInterview/InterviewQuestion.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
// import Link from "next/link";
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$PreviewResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/PreviewResumeIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ArrowDownIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/ArrowDownIcon.tsx [app-client] (ecmascript)");
// import RightGreenIcon from "@/components/svgComponents/RightGreenIcon";
// import WrongRedIcon from "@/components/svgComponents/WrongRedIcon";
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$ProgressTracker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/conductInterview/ProgressTracker.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RecIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/RecIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LetterFoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/LetterFoldIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/conductInterview.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/interviewServices.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/interviewSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$EndInterViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/EndInterViewModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/yup/dist/yup.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$validations$2f$interviewValidations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/validations/interviewValidations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const InterviewQuestion = ({ params })=>{
    _s();
    const searchParamsPromise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["use"])(params);
    const interviewId = +searchParamsPromise.interviewId;
    const resumeLink = searchParamsPromise.resumeLink;
    const interviewQuestionsData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "InterviewQuestion.useSelector[interviewQuestionsData]": (state)=>state.interview
    }["InterviewQuestion.useSelector[interviewQuestionsData]"]);
    const authData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "InterviewQuestion.useSelector[authData]": (state)=>state.auth.authData
    }["InterviewQuestion.useSelector[authData]"]);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const socketRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mediaRecorderRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const streamRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isRecording, setIsRecording] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { control, handleSubmit, reset, getValues, watch, formState: { errors } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        mode: "onChange",
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yupResolver"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$validations$2f$interviewValidations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addAnswerValidation"])(t))
    });
    const [openQuestions, setOpenQuestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loader, setLoader] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showEndInterviewModal, setShowEndInterviewModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedTab, setSelectedTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED);
    const [selectedSkill, setSelectedSkill] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [skillMarked, setSkillMarked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [currentCultureSkillIndex, setCurrentCultureSkillIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [currentRoleSkillIndex, setCurrentRoleSkillIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const roleSkills = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InterviewQuestion.useMemo[roleSkills]": ()=>Object.keys(interviewQuestionsData.roleSpecificQuestions || {})
    }["InterviewQuestion.useMemo[roleSkills]"], [
        interviewQuestionsData.roleSpecificQuestions
    ]);
    const cultureSkills = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InterviewQuestion.useMemo[cultureSkills]": ()=>Object.keys(interviewQuestionsData.cultureSpecificQuestions || {})
    }["InterviewQuestion.useMemo[cultureSkills]"], [
        interviewQuestionsData.cultureSpecificQuestions
    ]);
    console.log("===========errors", control._formState.errors);
    console.log("====selectedSkill", selectedSkill);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InterviewQuestion.useEffect": ()=>{
            prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
            setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
        }
    }["InterviewQuestion.useEffect"], []);
    // Function to initialize socket with the token
    const initializeSocket = (token)=>{
        if (socketRef.current) {
            socketRef.current.disconnect(); // Disconnect the previous socket connection
        }
        // Initialize the socket with the new token
        socketRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])("http://localhost:3001", {
            path: "/conduct-interview",
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            reconnectionDelayMax: 5000,
            extraHeaders: {
                Authorization: `Bearer ${token}`
            },
            query: {
                interviewId: interviewId.toString()
            }
        });
        // Add socket event listeners
        socketRef.current?.on("connect", ()=>{
            console.log("Connected to server");
        });
        socketRef.current?.on("disconnect", (reason, details)=>{
            console.log("Disconnected from server:", reason, details);
        });
        socketRef.current?.on("connect_error", (error)=>{
            console.log("Connect error:", error);
        });
    };
    const startSocketConnection = async ()=>{
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSession"])();
        const parsedSession = {
            ...session
        };
        const token = parsedSession?.user?.data?.token;
        console.log("token", token);
        if (token) {
            initializeSocket(token);
        }
        return ()=>{
            if (socketRef.current) {
                socketRef.current.disconnect();
            }
        };
    };
    const handleOpen = (questionId)=>{
        setOpenQuestions((prev)=>{
            // If the question is already open, close it
            if (prev.includes(questionId)) {
                return prev.filter((id)=>id !== questionId);
            }
            // Otherwise, add it to the open questions array
            return [
                ...prev,
                questionId
            ];
        });
    };
    async function openMicrophone(microphone, socket) {
        console.log("microphone======", microphone);
        return new Promise((resolve)=>{
            let buffer = [];
            const bufferInterval = 1000;
            microphone.onstart = ()=>{
                resolve();
            };
            microphone.onstop = ()=>{
                if (buffer.length > 0 && socket?.connected) {
                    const finalBlob = new Blob(buffer, {
                        type: "audio/webm"
                    });
                    socket.emit("message", finalBlob);
                    buffer = [];
                }
            };
            microphone.ondataavailable = (event)=>{
                console.log("event======", event);
                if (event.data.size > 0) {
                    buffer.push(event.data);
                }
            };
            const sendInterval = setInterval(()=>{
                if (buffer.length > 0 && socket?.connected) {
                    const audioBlob = new Blob(buffer, {
                        type: "audio/webm"
                    });
                    socket.emit("message", audioBlob);
                    buffer = [];
                }
            }, bufferInterval);
            microphone.start(500);
            console.log("buffer======", buffer);
            const cleanup = ()=>{
                clearInterval(sendInterval);
                if (microphone.state !== "inactive") {
                    microphone.stop();
                }
            };
            socket?.on("disconnect", cleanup);
            socket?.on("error", cleanup);
        });
    }
    const startTranscription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InterviewQuestion.useCallback[startTranscription]": async ()=>{
            try {
                setIsRecording(true);
                await startSocketConnection();
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });
                console.log("stream======", stream);
                streamRef.current = stream;
                const options = {
                    mimeType: "audio/webm"
                };
                const recorder = new MediaRecorder(stream, options);
                console.log("recorder======", recorder);
                mediaRecorderRef.current = recorder;
                await openMicrophone(recorder, socketRef.current);
            } catch (error) {
                console.error("Error starting transcription:", error);
            }
        }
    }["InterviewQuestion.useCallback[startTranscription]"], []);
    const stopTranscription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InterviewQuestion.useCallback[stopTranscription]": async ()=>{
            setIsRecording(false);
            if (mediaRecorderRef.current) {
                if (mediaRecorderRef.current.state !== "inactive") {
                    mediaRecorderRef.current.stop();
                }
                mediaRecorderRef.current = null;
            }
            if (streamRef.current) {
                streamRef.current.getTracks().forEach({
                    "InterviewQuestion.useCallback[stopTranscription]": (track)=>track.stop()
                }["InterviewQuestion.useCallback[stopTranscription]"]);
                streamRef.current = null;
            }
            // so that previous audio is processed
            setTimeout({
                "InterviewQuestion.useCallback[stopTranscription]": ()=>{
                    if (socketRef.current) {
                        socketRef.current.close();
                        socketRef.current = null;
                    }
                }
            }["InterviewQuestion.useCallback[stopTranscription]"], 5000);
        }
    }["InterviewQuestion.useCallback[stopTranscription]"], []);
    console.log(control._formValues, "control._formValues===========");
    const handleSaveAndNext = async (data)=>{
        try {
            console.log("inside handleSaveAndNext");
            if (skillMarked === 0) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("please_mark_stratum_score"));
                return;
            }
            setLoading(true);
            const { behavioralInfo, ...rest } = data;
            console.log("==========behavioralInfo", behavioralInfo);
            // Transform the data into the required format
            const answers = Object.entries(rest).map(([key, value])=>{
                // Extract the ID from the key (e.g., "answer--->>148" -> 148)
                const questionId = parseInt(key.replace("answer-", ""));
                return {
                    questionId,
                    answer: value
                };
            });
            let skillId;
            let jobSkillId;
            if (selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC) {
                skillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].skillId;
                jobSkillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].jobSkillId;
            } else if (selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC) {
                skillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].skillId;
                jobSkillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].jobSkillId;
            }
            const payload = {
                interviewId: interviewId,
                skillId,
                jobSkillId,
                skillMarked: skillMarked,
                skillType: selectedTab,
                answers
            };
            console.log("==========payload", payload);
            // After saving, handle navigation based on current section and skill
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateInterviewAnswers"])(payload);
            const interviewerName = `${authData?.first_name.charAt(0).toUpperCase()}${authData?.last_name.charAt(0).toUpperCase()}`;
            console.log("interviewerName", interviewerName);
            console.log("api response====", response);
            if (response?.data?.success) {
                console.log("interviewerName", interviewerName);
                switch(selectedTab){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateQuestionAnswer"])({
                            questionType: selectedTab,
                            questionAnswers: answers,
                            stratumScore: skillMarked
                        }));
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateQuestionAnswer"])({
                            questionType: selectedTab,
                            category: selectedSkill,
                            questionAnswers: answers,
                            stratumScore: skillMarked,
                            interviewerName
                        }));
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateQuestionAnswer"])({
                            questionType: selectedTab,
                            category: selectedSkill,
                            questionAnswers: answers,
                            stratumScore: skillMarked,
                            interviewerName
                        }));
                        break;
                }
            }
            setTimeout(()=>{
                handleNextSkillInterview();
            }, 100);
        } catch  {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setLoading(false);
        }
    };
    const handleEndInterview = async ()=>{
        try {
            setLoader(true);
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$interviewServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endInterview"])({
                interviewId,
                behaviouralNotes: getValues("behavioralInfo") ?? ""
            });
            if (response?.data?.success) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(response?.data?.message));
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$interviewSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearInterview"])());
                router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(response?.data?.message));
            }
        } catch  {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setLoader(false);
        }
    };
    const handlePreviousSkillInterview = ()=>{
        setOpenQuestions([]);
        const behavioralInfo = getValues("behavioralInfo");
        reset({
            behavioralInfo: behavioralInfo
        });
        switch(selectedTab){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                // Check if we're on the first role skill
                if (currentRoleSkillIndex === 0) {
                    // Move from Role-specific back to Career-based
                    setSelectedTab(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED);
                    prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
                    setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
                } else {
                    // Move to previous role skill
                    const prevIndex = currentRoleSkillIndex - 1;
                    setCurrentRoleSkillIndex(prevIndex);
                    setSelectedSkill(roleSkills[prevIndex]);
                    prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].questions || []);
                    setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].score);
                }
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                // Check if we're on the first culture skill
                if (currentCultureSkillIndex === 0) {
                    // Move from Culture-specific to Role-specific
                    setSelectedTab(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC);
                    if (roleSkills.length > 0) {
                        setSelectedSkill(roleSkills[roleSkills.length - 1]);
                        setCurrentRoleSkillIndex(roleSkills.length - 1);
                        prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].questions || []);
                        setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].score);
                    }
                } else {
                    // Move to previous culture skill
                    const prevIndex = currentCultureSkillIndex - 1;
                    setCurrentCultureSkillIndex(prevIndex);
                    setSelectedSkill(cultureSkills[prevIndex]);
                    prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].questions || []);
                    setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].score);
                }
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                break;
        }
    };
    const handleNextSkillInterview = ()=>{
        setOpenQuestions([]);
        const behavioralInfo = getValues("behavioralInfo");
        reset({
            behavioralInfo: behavioralInfo
        });
        switch(selectedTab){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                // Move from Career-based to Role-specific
                setSelectedTab(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC);
                if (roleSkills.length > 0) {
                    setSelectedSkill(roleSkills[0]);
                    prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].questions || []);
                    setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].score);
                }
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                // Check if there are more role skills to navigate to
                if (currentRoleSkillIndex < roleSkills.length - 1) {
                    // Move to next role skill
                    const nextIndex = currentRoleSkillIndex + 1;
                    setCurrentRoleSkillIndex(nextIndex);
                    setSelectedSkill(roleSkills[nextIndex]);
                    prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].questions || []);
                    setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].score);
                } else {
                    // Move from Role-specific to Culture-specific
                    setSelectedTab(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC);
                    setCurrentRoleSkillIndex(0);
                    if (cultureSkills.length > 0) {
                        setSelectedSkill(cultureSkills[0]);
                        setCurrentCultureSkillIndex(0);
                        prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].questions || []);
                        setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].score);
                    }
                }
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                // Check if there are more culture skills to navigate to
                if (currentCultureSkillIndex < cultureSkills.length - 1) {
                    // Move to next culture skill
                    const nextIndex = currentCultureSkillIndex + 1;
                    setCurrentCultureSkillIndex(nextIndex);
                    setSelectedSkill(cultureSkills[nextIndex]);
                    prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].questions || []);
                    setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].score);
                }
                break;
        }
    };
    const isLastSkillInSection = ()=>{
        switch(selectedTab){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                return true; // Career-based doesn't have sub-skills
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                return currentCultureSkillIndex >= cultureSkills.length - 1;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                return currentRoleSkillIndex >= roleSkills.length - 1;
            default:
                return false;
        }
    };
    const isLastSkillOverall = ()=>{
        return selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC && isLastSkillInSection();
    };
    const isSkillCompleted = (skillType, skillName)=>{
        switch(skillType){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                return interviewQuestionsData.careerBasedQuestions?.questions?.some((question)=>question.answer !== undefined && question.answer !== "") || !!interviewQuestionsData.careerBasedQuestions.score;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                return interviewQuestionsData.roleSpecificQuestions?.[skillName]?.questions?.some((question)=>question.answer !== undefined && question.answer !== "") || !!interviewQuestionsData.roleSpecificQuestions[skillName].score;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                return interviewQuestionsData.cultureSpecificQuestions?.[skillName]?.questions?.some((question)=>question.answer !== undefined && question.answer !== "") || !!interviewQuestionsData.cultureSpecificQuestions[skillName].score;
            default:
                return false;
        }
    };
    const prefillFormData = (questions)=>{
        if (!questions || questions.length === 0) {
            return;
        }
        // For each question, prefill the form if we have data
        questions.forEach((question)=>{
            if (question.answer) {
                // Set the form value for this question
                const fieldName = `answer-${question.id}`;
                // Use reset to set the value while preserving other form values
                reset({
                    ...control._formValues,
                    [fieldName]: question.answer
                });
                // Open the question card
                if (!openQuestions.includes(question.id)) {
                    setOpenQuestions((prev)=>[
                            ...prev,
                            question.id
                        ]);
                }
            }
        });
    };
    const handleSkillSelection = (skill)=>{
        setSelectedSkill(skill);
        // update additionalInfo field
        reset({
            ...control._formValues,
            behavioralInfo: getValues("behavioralInfo")
        });
        // Update the corresponding index tracker based on the selected tab
        switch(selectedTab){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                const roleIndex = roleSkills.findIndex((s)=>s === skill);
                if (roleIndex !== -1) {
                    setCurrentRoleSkillIndex(roleIndex);
                }
                // Prefill form data for role-specific skills
                prefillFormData(interviewQuestionsData.roleSpecificQuestions[skill].questions || []);
                setSkillMarked(interviewQuestionsData.roleSpecificQuestions[skill].score);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                const cultureIndex = cultureSkills.findIndex((s)=>s === skill);
                if (cultureIndex !== -1) {
                    setCurrentCultureSkillIndex(cultureIndex);
                }
                // Prefill form data for culture-specific skills
                prefillFormData(interviewQuestionsData.cultureSpecificQuestions[skill].questions || []);
                setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[skill].score);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                // Prefill form data for career-based skills
                prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
                setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
                break;
            default:
                break;
        }
    };
    const renderQuestion = (question, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "interview-question-card ",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    onClick: ()=>handleOpen(question.id),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "tittle",
                            children: [
                                t("question"),
                                " ",
                                index < 9 ? `0${index + 1}` : index + 1,
                                " ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$ArrowDownIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    className: openQuestions.includes(question.id) ? "rotate" : ""
                                }, void 0, false, {
                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                    lineNumber: 567,
                                    columnNumber: 69
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                            lineNumber: 566,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                            children: question.question
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                            lineNumber: 569,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                    lineNumber: 565,
                    columnNumber: 7
                }, this),
                openQuestions.includes(question.id) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "question-body ",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                htmlFor: `answer-${question.id}`,
                                required: true,
                                children: t("your_notes")
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 574,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                rows: 3,
                                name: `answer-${question.id}`,
                                control: control,
                                placeholder: t("additional_info_desc"),
                                className: "form-control"
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 577,
                                columnNumber: 13
                            }, this),
                            watch(`answer-${question.id}`)?.length > 2000 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error, {
                                message: t("answer_max_2000_chars")
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 578,
                                columnNumber: 62
                            }, this) : null
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                        lineNumber: 573,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                    lineNumber: 572,
                    columnNumber: 9
                }, this) : null
            ]
        }, question.id, true, {
            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
            lineNumber: 564,
            columnNumber: 5
        }, this);
    const renderAllQuestions = (type)=>{
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED:
                return interviewQuestionsData.careerBasedQuestions?.questions?.map((question, index)=>renderQuestion(question, index)) || [];
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC:
                return interviewQuestionsData.roleSpecificQuestions?.[selectedSkill]?.questions?.map((question, index)=>renderQuestion(question, index)) || [];
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC:
                return interviewQuestionsData.cultureSpecificQuestions?.[selectedSkill]?.questions?.map((question, index)=>renderQuestion(question, index)) || [];
            default:
                return [];
        }
    };
    const getSkills = (type)=>type === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC ? cultureSkills : roleSkills;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].conduct_interview_page,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "common-page-header",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                className: "danger-outline-btn rounded-md py-3 px-4 mb-5",
                                onClick: ()=>{
                                    if (isRecording) {
                                        stopTranscription();
                                    } else {
                                        startTranscription();
                                    }
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$RecIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "me-3"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 632,
                                        columnNumber: 13
                                    }, this),
                                    isRecording ? t("stop_recording") : t("record_interview")
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 622,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$ProgressTracker$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                isRecording: isRecording
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 636,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "common-page-head-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "main-heading",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: ()=>{
                                                        if (selectedTab !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED) {
                                                            handlePreviousSkillInterview();
                                                        }
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                    lineNumber: 641,
                                                    columnNumber: 17
                                                }, this),
                                                selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        t("career_based_skills_and_general_interview"),
                                                        " ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: t("questions")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 648,
                                                            columnNumber: 70
                                                        }, this)
                                                    ]
                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC ? t("role_specific_interview") : t("culture_specific_interview"),
                                                        " ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: t("questions")
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 653,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                            lineNumber: 640,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "clear-btn text-btn primary p-0 m-0",
                                            onClick: ()=>window.open(resumeLink, "_blank"),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$PreviewResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    className: "me-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                    lineNumber: 658,
                                                    columnNumber: 17
                                                }, this),
                                                t("preview_candidate_resume")
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                            lineNumber: 657,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                    lineNumber: 639,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 638,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                        lineNumber: 621,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                        onSubmit: handleSubmit((data)=>handleSaveAndNext(data)),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inner-section",
                                children: [
                                    selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC || selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CULTURE_SPECIFIC ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].question_info_box,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].current
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 671,
                                                                    columnNumber: 23
                                                                }, this),
                                                                t("current")
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 670,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$conductInterview$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].completed
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 675,
                                                                    columnNumber: 23
                                                                }, this),
                                                                t("completed")
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 674,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                    lineNumber: 669,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 668,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: "interview-topic-list",
                                                "aria-label": "Job roles list",
                                                children: getSkills(selectedTab)?.map((skill, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: `topic-item ${skill === selectedSkill ? "current" : isSkillCompleted(selectedTab, skill) ? "completed" : ""}`,
                                                        tabIndex: index,
                                                        onClick: ()=>handleSkillSelection(skill),
                                                        children: [
                                                            skill,
                                                            isSkillCompleted(selectedTab, skill) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "interviewer-name",
                                                                children: selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].ROLE_SPECIFIC ? interviewQuestionsData.roleSpecificQuestions[skill]?.interviewerName : interviewQuestionsData.cultureSpecificQuestions[skill]?.interviewerName
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                lineNumber: 694,
                                                                columnNumber: 25
                                                            }, this) : null
                                                        ]
                                                    }, skill, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                        lineNumber: 686,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 684,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true) : null,
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "col-md-8",
                                                children: renderAllQuestions(selectedTab)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 706,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "col-md-4",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "behavioral-letter-card",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                            children: [
                                                                t("behavioral"),
                                                                " ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    children: t("performance")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 710,
                                                                    columnNumber: 39
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 709,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: "behavioralInfo",
                                                                    children: t("describe_candidate_behaviours")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 713,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    rows: 5,
                                                                    name: "behavioralInfo",
                                                                    control: control,
                                                                    placeholder: t("describe_candidate_behaviours"),
                                                                    className: "form-control"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 714,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error, {
                                                                    message: errors.behavioralInfo?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                                    lineNumber: 721,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 712,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$LetterFoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "fold-svg"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                            lineNumber: 723,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                    lineNumber: 708,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 707,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 705,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "section-heading",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                children: [
                                                    t("score"),
                                                    " ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "primary",
                                                        children: [
                                                            selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED ? t("career") : selectedSkill,
                                                            " ",
                                                            t("stratum")
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                        lineNumber: 730,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 728,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: [
                                                    t("score_the_candidate_for"),
                                                    " ",
                                                    selectedTab === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUESTION_TYPES"].CAREER_BASED ? t("career_based") : selectedSkill.toLowerCase(),
                                                    " ",
                                                    t("stratum"),
                                                    " ",
                                                    t("score_range")
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 734,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 727,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "number-task",
                                        children: Array.from({
                                            length: 10
                                        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: index > 8 ? index + 1 === skillMarked ? "extreme active" : "extreme" : index + 1 === skillMarked ? "active" : "",
                                                onClick: ()=>{
                                                    setSkillMarked(index + 1 === skillMarked ? 0 : index + 1);
                                                },
                                                children: index > 8 ? t("extreme") : index + 1
                                            }, index, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 741,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 739,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "interview-question-card",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                            children: t("candidate_not_aware")
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                            lineNumber: 753,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 752,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 665,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "button-align justify-content-between pt-4 pb-5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "button-align ",
                                        children: [
                                            !isLastSkillOverall() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "primary-btn rounded-md",
                                                type: "button",
                                                disabled: loading || loader,
                                                onClick: handleNextSkillInterview,
                                                children: t("next_skill_interview")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 759,
                                                columnNumber: 17
                                            }, this) : null,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "dark-outline-btn rounded-md",
                                                type: "submit",
                                                disabled: loading || loader,
                                                loading: loading,
                                                children: t("save_next")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                                lineNumber: 763,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 757,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "danger-btn rounded-md",
                                        type: "button",
                                        onClick: ()=>setShowEndInterviewModal(true),
                                        loading: loader,
                                        disabled: loading || loader,
                                        children: t("end_interview")
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                        lineNumber: 767,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                                lineNumber: 756,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                        lineNumber: 664,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                lineNumber: 620,
                columnNumber: 7
            }, this),
            showEndInterviewModal ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$EndInterViewModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onClickCancel: ()=>setShowEndInterviewModal(false),
                onClickEndInterview: handleEndInterview,
                disabled: loading || loader
            }, void 0, false, {
                fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
                lineNumber: 780,
                columnNumber: 9
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/views/conductInterview/InterviewQuestion.tsx",
        lineNumber: 619,
        columnNumber: 5
    }, this);
};
_s(InterviewQuestion, "Q+8amqQWKuYbIvcdIGWhQ/uqqiU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = InterviewQuestion;
const __TURBOPACK__default__export__ = InterviewQuestion;
var _c;
__turbopack_context__.k.register(_c, "InterviewQuestion");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/interview-question/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$InterviewQuestion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/conductInterview/InterviewQuestion.tsx [app-client] (ecmascript)");
"use client";
;
;
const page = ({ searchParams })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$conductInterview$2f$InterviewQuestion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            params: searchParams
        }, void 0, false, {
            fileName: "[project]/src/app/interview-question/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/interview-question/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_42d43a42._.js.map
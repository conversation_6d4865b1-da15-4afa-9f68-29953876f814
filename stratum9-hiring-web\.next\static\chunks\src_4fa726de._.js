(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_4fa726de._.js", {

"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = BackArrowIcon;
const __TURBOPACK__default__export__ = BackArrowIcon;
var _c;
__turbopack_context__.k.register(_c, "BackArrowIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/jobRequirements/jobServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "fetchJobsMeta": (()=>fetchJobsMeta),
    "generatePDF": (()=>generatePDF),
    "getJobHtmlDescription": (()=>getJobHtmlDescription),
    "saveJobDetails": (()=>saveJobDetails),
    "updateJobDescription": (()=>updateJobDescription)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const saveJobDetails = (formData)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].jobRequirements.SAVE_JOB_DETAILS, formData);
};
const fetchJobsMeta = (data)=>{
    // Use 'params' to send query parameters in axios GET
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].jobRequirements.GET_JOBS_META, {
        ...data
    });
};
const getJobHtmlDescription = (id)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].jobRequirements.GET_JOB_HTML_DESCRIPTION, {
        id
    });
};
const updateJobDescription = (htmlData)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["put"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].jobRequirements.UPDATE_JOB_DESCRIPTION, htmlData);
};
const generatePDF = (data)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["post"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].jobRequirements.GENERATE_PDF, data);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_STATUS": (()=>APPLICATION_STATUS),
    "CATEGORY_OPTION": (()=>CATEGORY_OPTION),
    "COMPLIANCE_LINK": (()=>COMPLIANCE_LINK),
    "COMPLIANCE_OPTIONS": (()=>COMPLIANCE_OPTIONS),
    "CURRENCY_SYMBOL": (()=>CURRENCY_SYMBOL),
    "CURSOR_POINT": (()=>CURSOR_POINT),
    "DEPARTMENT_OPTION": (()=>DEPARTMENT_OPTION),
    "EXPERIENCE_LEVEL_OPTIONS": (()=>EXPERIENCE_LEVEL_OPTIONS),
    "FILE_NAME": (()=>FILE_NAME),
    "FILE_SIZE_LIMIT": (()=>FILE_SIZE_LIMIT),
    "FILE_TYPE": (()=>FILE_TYPE),
    "HIRING_TYPE": (()=>HIRING_TYPE),
    "HIRING_TYPE_KEY": (()=>HIRING_TYPE_KEY),
    "JOB_GENERATION_UPLOAD_MESSAGES": (()=>JOB_GENERATION_UPLOAD_MESSAGES),
    "LOCATION_TYPE_OPTIONS": (()=>LOCATION_TYPE_OPTIONS),
    "MAX_FILE_SIZE": (()=>MAX_FILE_SIZE),
    "SALARY_CYCLE_OPTIONS": (()=>SALARY_CYCLE_OPTIONS),
    "SALARY_REMOVE_SYMBOL_REGEX": (()=>SALARY_REMOVE_SYMBOL_REGEX),
    "SKILL_CATEGORY": (()=>SKILL_CATEGORY),
    "SKILL_TYPE": (()=>SKILL_TYPE),
    "SUN_EDITOR_BUTTON_LIST": (()=>SUN_EDITOR_BUTTON_LIST),
    "TONE_STYLE_OPTIONS": (()=>TONE_STYLE_OPTIONS)
});
const CATEGORY_OPTION = [
    {
        label: "Full time",
        value: "full_time"
    },
    {
        label: "Part time",
        value: "part_time"
    },
    {
        label: "Contract",
        value: "contract"
    },
    {
        label: "Internship",
        value: "internship"
    },
    {
        label: "Freelance",
        value: "freelance"
    }
];
const SALARY_CYCLE_OPTIONS = [
    {
        label: "Per Hour",
        value: "per hour"
    },
    {
        label: "Per Month",
        value: "per month"
    },
    {
        label: "Per Annum",
        value: "per annum"
    }
];
const LOCATION_TYPE_OPTIONS = [
    {
        label: "Remote",
        value: "remote"
    },
    {
        label: "Hybrid",
        value: "hybrid"
    },
    {
        label: "On-site",
        value: "onsite"
    }
];
const TONE_STYLE_OPTIONS = [
    {
        label: "Professional & Formal",
        value: "Professional_Formal"
    },
    {
        label: "Conversational & Approachable",
        value: "Conversational_Approachable"
    },
    {
        label: "Bold & Energetic",
        value: "Bold_Energetic"
    },
    {
        label: "Inspirational & Mission-Driven",
        value: "Inspirational_Mission-Driven"
    },
    {
        label: "Technical & Precise",
        value: "Technical_Precise"
    },
    {
        label: "Creative & Fun",
        value: "Creative_Fun"
    },
    {
        label: "Inclusive & Human-Centered",
        value: "Inclusive_Human-Centered"
    },
    {
        label: "Minimalist & Straightforward",
        value: "Minimalist_Straightforward"
    }
];
const COMPLIANCE_OPTIONS = [
    {
        label: "Equal Employment Opportunity (EEO) Statement",
        value: "Equal Employment Opportunity (EEO) Statement"
    },
    {
        label: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
        value: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"
    },
    {
        label: "Disability Accommodation Statement",
        value: "Disability Accommodation Statement"
    },
    {
        label: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
        value: "Veterans Preference Statement (For Government Agencies and Federal Contractors)"
    },
    {
        label: "Diversity & Inclusion Commitment",
        value: "Diversity & Inclusion Commitment"
    },
    {
        label: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
        value: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)"
    },
    {
        label: "Background Check and Drug-Free Workplace Policy (If Applicable)",
        value: "Background Check and Drug-Free Workplace Policy (If Applicable)"
    },
    {
        label: "Work Authorization & Immigration Statement",
        value: "Work Authorization & Immigration Statement"
    }
];
const EXPERIENCE_LEVEL_OPTIONS = [
    {
        label: "General",
        value: "General"
    },
    {
        label: "No experience necessary",
        value: "No experience necessary"
    },
    {
        label: "Entry-Level Position",
        value: "Entry-Level Position"
    },
    {
        label: "Mid-Level Professional",
        value: "Mid-Level Professional"
    },
    {
        label: "Senior/Experienced Professional",
        value: "Senior/Experienced Professional"
    },
    {
        label: "Managerial/Executive Level",
        value: "Managerial/Executive Level"
    },
    {
        label: "Specialized Expert",
        value: "Specialized Expert"
    }
];
const DEPARTMENT_OPTION = [
    {
        label: "IT",
        value: "IT"
    },
    {
        label: "HR",
        value: "HR"
    },
    {
        label: "Marketing",
        value: "Marketing"
    },
    {
        label: "Finance",
        value: "Finance"
    },
    {
        label: "Sales",
        value: "Sales"
    }
];
const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const FILE_TYPE = "application/pdf";
const FILE_NAME = ".pdf";
const SALARY_REMOVE_SYMBOL_REGEX = /[\$\s]/g;
const CURRENCY_SYMBOL = "$";
const SUN_EDITOR_BUTTON_LIST = [
    [
        "font",
        "fontSize",
        "formatBlock"
    ],
    [
        "bold",
        "underline",
        "italic"
    ],
    [
        "fontColor",
        "hiliteColor"
    ],
    [
        "align",
        "list",
        "lineHeight"
    ]
];
const HIRING_TYPE = {
    INTERNAL: "internal",
    EXTERNAL: "external"
};
const SKILL_CATEGORY = {
    Personal_Health: "Personal Health",
    Social_Interaction: "Social Interaction",
    Mastery_Of_Emotions: "Mastery of Emotions",
    Mentality: "Mentality",
    Cognitive_Abilities: "Cognitive Abilities"
};
const APPLICATION_STATUS = {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
    HIRED: "Hired",
    ON_HOLD: "On-Hold",
    FINAL_REJECT: "Final-Reject"
};
const SKILL_TYPE = {
    ROLE: "role",
    CULTURE: "culture"
};
const HIRING_TYPE_KEY = "hiringType";
const CURSOR_POINT = {
    cursor: "pointer"
};
const COMPLIANCE_LINK = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";
const JOB_GENERATION_UPLOAD_MESSAGES = [
    "Analyzing your job description...",
    "Extracting key requirements...",
    "Processing document content...",
    "Identifying skills and qualifications...",
    "Parsing job details...",
    "Almost ready..."
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "commonPage-module-scss-module__em0r7a__active",
  "add_another_candidate_link": "commonPage-module-scss-module__em0r7a__add_another_candidate_link",
  "approved_status_indicator": "commonPage-module-scss-module__em0r7a__approved_status_indicator",
  "border_none": "commonPage-module-scss-module__em0r7a__border_none",
  "candidate_card": "commonPage-module-scss-module__em0r7a__candidate_card",
  "candidate_card_header": "commonPage-module-scss-module__em0r7a__candidate_card_header",
  "candidate_qualification_page": "commonPage-module-scss-module__em0r7a__candidate_qualification_page",
  "candidates_list_page": "commonPage-module-scss-module__em0r7a__candidates_list_page",
  "candidates_list_section": "commonPage-module-scss-module__em0r7a__candidates_list_section",
  "career-skill-card": "commonPage-module-scss-module__em0r7a__career-skill-card",
  "dashboard__stat": "commonPage-module-scss-module__em0r7a__dashboard__stat",
  "dashboard__stat_design": "commonPage-module-scss-module__em0r7a__dashboard__stat_design",
  "dashboard__stat_image": "commonPage-module-scss-module__em0r7a__dashboard__stat_image",
  "dashboard__stat_label": "commonPage-module-scss-module__em0r7a__dashboard__stat_label",
  "dashboard__stat_value": "commonPage-module-scss-module__em0r7a__dashboard__stat_value",
  "dashboard__stats": "commonPage-module-scss-module__em0r7a__dashboard__stats",
  "dashboard_inner_head": "commonPage-module-scss-module__em0r7a__dashboard_inner_head",
  "dashboard_page": "commonPage-module-scss-module__em0r7a__dashboard_page",
  "header_tab": "commonPage-module-scss-module__em0r7a__header_tab",
  "inner_heading": "commonPage-module-scss-module__em0r7a__inner_heading",
  "inner_page": "commonPage-module-scss-module__em0r7a__inner_page",
  "input_type_file": "commonPage-module-scss-module__em0r7a__input_type_file",
  "interview_form_icon": "commonPage-module-scss-module__em0r7a__interview_form_icon",
  "job_info": "commonPage-module-scss-module__em0r7a__job_info",
  "job_page": "commonPage-module-scss-module__em0r7a__job_page",
  "manual_upload_resume": "commonPage-module-scss-module__em0r7a__manual_upload_resume",
  "operation_admins_img": "commonPage-module-scss-module__em0r7a__operation_admins_img",
  "resume_page": "commonPage-module-scss-module__em0r7a__resume_page",
  "search_box": "commonPage-module-scss-module__em0r7a__search_box",
  "section_heading": "commonPage-module-scss-module__em0r7a__section_heading",
  "section_name": "commonPage-module-scss-module__em0r7a__section_name",
  "selected": "commonPage-module-scss-module__em0r7a__selected",
  "selecting": "commonPage-module-scss-module__em0r7a__selecting",
  "selection": "commonPage-module-scss-module__em0r7a__selection",
  "skills_info_box": "commonPage-module-scss-module__em0r7a__skills_info_box",
  "skills_tab": "commonPage-module-scss-module__em0r7a__skills_tab",
  "text_xs": "commonPage-module-scss-module__em0r7a__text_xs",
  "upload_resume_page": "commonPage-module-scss-module__em0r7a__upload_resume_page",
});
}}),
"[project]/src/components/svgComponents/CopyIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function CopyIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "25",
        height: "25",
        viewBox: "0 0 32 32",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M26.5713 2.16016H9.69728C7.90088 2.16016 6.43948 3.62596 6.43948 5.42776V6.43946H5.41698C3.62058 6.43946 2.15918 7.90526 2.15918 9.70806V26.5724C2.15918 28.3742 3.62058 29.84 5.41698 29.84H22.2915C24.0937 29.84 25.5596 28.3742 25.5596 26.5724V25.5607H26.5713C28.3735 25.5607 29.8399 24.0949 29.8399 22.2921V5.42766C29.8398 3.62596 28.3735 2.16016 26.5713 2.16016ZM23.5596 26.5723C23.5596 27.2715 22.9908 27.8399 22.2915 27.8399H5.41698C4.72358 27.8399 4.15918 27.2715 4.15918 26.5723V9.70796C4.15918 9.00876 4.72368 8.43936 5.41698 8.43936H7.43948H22.2915C22.9907 8.43936 23.5596 9.00866 23.5596 9.70796V24.5605V26.5723ZM27.8398 22.292C27.8398 22.9912 27.271 23.5606 26.5712 23.5606H25.5595V9.70796C25.5595 7.90526 24.0937 6.43936 22.2914 6.43936H8.43938V5.42766C8.43938 4.72846 9.00388 4.16006 9.69718 4.16006H26.5712C27.2709 4.16006 27.8398 4.72846 27.8398 5.42766V22.292Z",
            fill: "#436EB6"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/CopyIcon.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/CopyIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = CopyIcon;
const __TURBOPACK__default__export__ = CopyIcon;
var _c;
__turbopack_context__.k.register(_c, "CopyIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/DownloadResumeIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function DownloadResumeIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "24",
        height: "23",
        viewBox: "0 0 32 32",
        fill: "#436EB6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M20.0625 -0.162109C20.3006 -0.161974 20.5276 -0.0563589 20.6914 0.116211H20.6904L28.5752 8.38672L28.5771 8.38867L28.6768 8.51855C28.7671 8.65777 28.8261 8.82269 28.8262 8.99609V27.7402C28.8262 30.0992 26.8799 32.0371 24.5215 32.0371H7.5791C5.22064 32.0371 3.27441 30.0992 3.27441 27.7402V4.13477C3.27441 1.77607 5.22064 -0.162109 7.5791 -0.162109H20.0625ZM7.5791 1.58301C6.18101 1.58301 5.01953 2.74487 5.01953 4.13477V27.7402C5.01953 29.1378 6.18845 30.292 7.5791 30.292H24.5215C25.9198 30.292 27.0811 29.138 27.0811 27.7402V9.85352H22.6904C20.7573 9.85352 19.1963 8.30187 19.1963 6.36816V1.58301H7.5791ZM20.9424 6.36816C20.9424 7.33265 21.7248 8.1084 22.6904 8.1084H25.8887L20.9424 2.91504V6.36816Z",
                        strokeWidth: "0.2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                        lineNumber: 7,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M22.4121 24.959C22.8993 24.9592 23.292 25.351 23.292 25.832C23.2917 26.3119 22.8998 26.7038 22.4199 26.7041H9.68945C9.20934 26.7041 8.81667 26.3121 8.81641 25.832C8.81641 25.3515 9.2092 24.959 9.68945 24.959H22.4121Z",
                        strokeWidth: "0.2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                        lineNumber: 11,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M16.0498 10.4824C16.5301 10.4824 16.9229 10.8749 16.9229 11.3555V19.542L19.9424 16.3027C20.2651 15.9542 20.8224 15.928 21.1729 16.2598L21.2354 16.3223C21.5055 16.6292 21.5268 17.0958 21.2744 17.4238L21.2168 17.4912L16.6875 22.3525L16.6865 22.3535C16.5222 22.5264 16.2954 22.6318 16.0498 22.6318C15.8044 22.6317 15.5775 22.5264 15.4131 22.3535V22.3525L10.8906 17.4912V17.4902C10.5584 17.1393 10.5871 16.5904 10.9336 16.2607C11.2626 15.9481 11.767 15.9532 12.1006 16.2412L12.165 16.3027L12.166 16.3037L15.1768 19.54V11.3555C15.1768 10.875 15.5697 10.4826 16.0498 10.4824Z",
                        strokeWidth: "0.2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                        lineNumber: 15,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_9593_1697",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "32",
                        height: "32",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                        lineNumber: 22,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                    lineNumber: 21,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
                lineNumber: 20,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/DownloadResumeIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = DownloadResumeIcon;
const __TURBOPACK__default__export__ = DownloadResumeIcon;
var _c;
__turbopack_context__.k.register(_c, "DownloadResumeIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/jobRequirement/PdfGenerator.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$buffer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/buffer/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html2pdf$2e$js$2f$dist$2f$html2pdf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/html2pdf.js/dist/html2pdf.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DownloadResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/DownloadResumeIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
const PdfGenerator = ({ content, fileName, onLoadingChange, title, subtitle, companyLogo, watermark, theme = "default", pageBreaks = true, footerLogo })=>{
    _s();
    const [isLoading, setIsLoading] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [footerLogoBase64, setFooterLogoBase64] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useState("");
    console.log("footerLogoBase64", footerLogoBase64);
    const convertImageUrlToBase64 = async (url)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].get(url, {
                responseType: "arraybuffer"
            });
            const base64 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$buffer$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Buffer"].from(response.data, "binary").toString("base64");
            return `data:image/png;base64,${base64}`;
        } catch (error) {
            console.error("Error converting image to base64:", error);
            return ""; // Return empty string if conversion fails
        }
    };
    const getThemeStyles = (theme)=>{
        const baseStyles = {
            fontFamily: "Arial, sans-serif",
            lineHeight: "1.6",
            color: "#333",
            padding: "15mm 20mm 20mm 20mm",
            boxSizing: "border-box",
            width: "100%",
            minHeight: "100vh",
            position: "relative"
        };
        const themes = {
            default: {
                ...baseStyles,
                fontSize: "11pt",
                backgroundColor: "#ffffff"
            },
            professional: {
                ...baseStyles,
                fontSize: "10pt",
                fontFamily: "Georgia, serif",
                backgroundColor: "#fafafa",
                borderLeft: "4px solid #2563eb",
                paddingLeft: "24mm"
            },
            modern: {
                ...baseStyles,
                fontSize: "11pt",
                fontFamily: "Helvetica, Arial, sans-serif",
                backgroundColor: "#ffffff",
                borderTop: "3px solid #10b981"
            },
            minimal: {
                ...baseStyles,
                fontSize: "12pt",
                fontFamily: "Helvetica, Arial, sans-serif",
                backgroundColor: "#ffffff",
                padding: "20mm"
            }
        };
        return themes[theme] || themes.default;
    };
    const createStyledContent = (content)=>{
        const themeStyles = getThemeStyles(theme);
        return `
      <div style="${Object.entries(themeStyles).map(([key, value])=>`${key.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${value}`).join("; ")}">
        
        ${companyLogo ? `
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${companyLogo}" alt="Company Logo" style="max-height: 40px; max-width: 150px;" />
          </div>
        ` : ""}
        
        ${title ? `
          <div style="text-align: center; margin-bottom: 15px;">
            <h1 style="
              margin: 0; 
              font-size: ${theme === "minimal" ? "18pt" : "16pt"}; 
              font-weight: bold; 
              color: ${theme === "professional" ? "#1f2937" : theme === "modern" ? "#10b981" : "#2563eb"};
              border-bottom: ${theme === "minimal" ? "none" : "2px solid #e5e7eb"};
              padding-bottom: ${theme === "minimal" ? "0" : "10px"};
            ">
              ${title}
            </h1>
          </div>
        ` : ""}
        
        ${subtitle ? `
          <div style="text-align: center; margin-bottom: 25px;">
            <p style="
              margin: 0; 
              font-size: 12pt; 
              color: #6b7280; 
              font-style: italic;
            ">
              ${subtitle}
            </p>
          </div>
        ` : ""}
        
        <div style="position: relative; z-index: 1;">
          ${content}
        </div>
        
        ${watermark ? `
          <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48pt;
            color: rgba(0, 0, 0, 0.05);
            z-index: 0;
            pointer-events: none;
            font-weight: bold;
            white-space: nowrap;
          ">
            ${watermark}
          </div>
        ` : ""}
        
        <div style="
          position: fixed;
          bottom: 10mm;
          right: 20mm;
          font-size: 8pt;
          color: #9ca3af;
          z-index: 2;
        ">
          Generated on ${new Date().toLocaleDateString()}
        </div>
      </div>

      ${footerLogoBase64 ? `
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${footerLogoBase64}" alt="Footer Logo" style="max-height: 40px; max-width: 150px;" />
          </div>
        ` : ""}
      
      <style>
        @media print {
          ${pageBreaks ? `
            h1, h2, h3 { 
              page-break-after: avoid; 
              page-break-inside: avoid; 
            }
            
            p, li { 
              page-break-inside: avoid; 
            }
            
            .page-break { 
              page-break-before: always; 
            }
            
            .no-break { 
              page-break-inside: avoid; 
            }
          ` : ""}
          
          table { 
            border-collapse: collapse; 
            width: 100%; 
            margin: 10px 0; 
          }
          
          table, th, td { 
            border: 1px solid #ddd; 
          }
          
          th, td { 
            padding: 8px; 
            text-align: left; 
          }
          
          th { 
            background-color: #f8f9fa; 
            font-weight: bold; 
          }
          
          ul, ol { 
            margin: 10px 0; 
            padding-left: 25px; 
          }
          
          li { 
            margin-bottom: 5px; 
          }
          
          blockquote {
            margin: 15px 0;
            padding: 10px 15px;
            border-left: 4px solid #e5e7eb;
            background-color: #f9fafb;
            font-style: italic;
          }
          
          code {
            background-color: #f3f4f6;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 90%;
          }
          
          pre {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 90%;
            line-height: 1.4;
          }
        }
      </style>
    `;
    };
    const generatePdf = async ()=>{
        if (!content) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("No content to generate PDF");
            return;
        }
        try {
            setIsLoading(true);
            if (onLoadingChange) onLoadingChange(true);
            // Create a temporary div with the styled content
            const element = document.createElement("div");
            element.innerHTML = createStyledContent(content);
            let tempFooterLogoBase64 = "";
            if (footerLogo) {
                tempFooterLogoBase64 = await convertImageUrlToBase64(footerLogo);
                setFooterLogoBase64(tempFooterLogoBase64);
            }
            // Enhanced PDF options
            const options = {
                filename: `${fileName.replace(/[^a-zA-Z0-9]/g, "_")}_${new Date().toISOString().slice(0, 19).replace(/[-:T]/g, "")}.pdf`,
                image: {
                    type: "jpeg",
                    quality: 0.98
                },
                html2canvas: {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: "#ffffff",
                    removeContainer: true,
                    logging: false
                },
                jsPDF: {
                    unit: "mm",
                    format: "a4",
                    orientation: "portrait",
                    compress: true
                },
                pagebreak: pageBreaks ? {
                    mode: [
                        "avoid-all",
                        "css",
                        "legacy"
                    ],
                    before: ".page-break",
                    after: ".page-break-after",
                    avoid: ".no-break"
                } : undefined
            };
            // Generate and download PDF
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$html2pdf$2e$js$2f$dist$2f$html2pdf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().set(options).from(element).save();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("PDF generated successfully");
        } catch (error) {
            console.error("Error generating PDF:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to generate PDF. Please try again.");
        } finally{
            setIsLoading(false);
            if (onLoadingChange) onLoadingChange(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "clear-btn p-0 ms-3",
        disabled: !content || isLoading,
        onClick: generatePdf,
        title: isLoading ? "Generating PDF..." : "Download PDF",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DownloadResumeIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/components/views/jobRequirement/PdfGenerator.tsx",
            lineNumber: 351,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/views/jobRequirement/PdfGenerator.tsx",
        lineNumber: 345,
        columnNumber: 5
    }, this);
};
_s(PdfGenerator, "6R7CWoFhbKjSuBtcQgLuq9zjx2I=");
_c = PdfGenerator;
const __TURBOPACK__default__export__ = PdfGenerator;
var _c;
__turbopack_context__.k.register(_c, "PdfGenerator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/jobRequirement/JobEditor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
// Internal libraries
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
// Components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)");
// Services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$jobRequirements$2f$jobServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/jobRequirements/jobServices.ts [app-client] (ecmascript)");
// Redux, constants, interfaces
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobRequirementSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobDetailsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/jobSkillsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/jobRequirementConstant.ts [app-client] (ecmascript)");
// CSS
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$CopyIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/CopyIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$jobRequirement$2f$PdfGenerator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/jobRequirement/PdfGenerator.tsx [app-client] (ecmascript)");
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Import SunEditor dynamically to avoid SSR issues
const SunEditor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/node_modules/suneditor-react/dist/index.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/node_modules/suneditor-react/dist/index.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c = SunEditor;
;
;
;
;
;
/**
 * JobEditor component for creating and editing job requirements
 * @component
 * @description A rich text editor component for creating and managing job requirements
 * @returns {JSX.Element} The JobEditor component
 */ function JobEditor() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const jobRequirement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectJobRequirement"]);
    const jobDetails = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectJobDetails"]);
    // State for edit mode and job details
    const [isEditMode, setIsEditMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [jobId, setJobId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [jobTitle, setJobTitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [editorContent, setEditorContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const editorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const cultureSpecificSkills = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectCultureSpecificSkills"]) || [];
    const careerSkills = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectCareerSkills"]) || [];
    const roleSpecificSkills = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["selectRoleSpecificSkills"]) || [];
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isEditorLoading, setIsEditorLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    const tGenerate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("jobRequirement");
    /**
   * Initializes editor content from Redux store
   * @function
   * @description Sets the editor content from job requirement data when available
   */ // Parse URL parameters for edit mode
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "JobEditor.useEffect": ()=>{
            // Check URL parameters for edit mode
            const searchParams = new URLSearchParams(window.location.search);
            const id = searchParams.get("jobId");
            // Define an async function inside useEffect
            const loadJobData = {
                "JobEditor.useEffect.loadJobData": async ()=>{
                    try {
                        // Set edit mode
                        setIsEditMode(true);
                        // Make sure id is not null before parsing
                        if (id) {
                            setJobId(parseInt(id, 10));
                            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$jobRequirements$2f$jobServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getJobHtmlDescription"])(id);
                            console.log(result);
                            if (result && result.data && result.data.success) {
                                const decoder = document.createElement("textarea");
                                decoder.innerHTML = result.data.data.htmlDescription;
                                const decodedHtml = decoder.value;
                                setEditorContent(decodedHtml);
                                setJobTitle(result.data.data.title);
                            }
                        }
                    } catch (error) {
                        console.error("Error loading job data:", error);
                    } finally{
                        setIsEditorLoading(false);
                    }
                }
            }["JobEditor.useEffect.loadJobData"];
            // Only require id to activate edit mode
            if (id) {
                // Call the async function
                loadJobData();
            } else if (jobRequirement && jobRequirement.content) {
                // Normal mode - set from Redux
                setEditorContent(jobRequirement.content);
                // Set a timeout to simulate loading and ensure proper initialization
                const timer = setTimeout({
                    "JobEditor.useEffect.timer": ()=>{
                        setIsEditorLoading(false);
                    }
                }["JobEditor.useEffect.timer"], 800);
                return ({
                    "JobEditor.useEffect": ()=>clearTimeout(timer)
                })["JobEditor.useEffect"];
            } else {
                // Just initialize the editor
                const timer = setTimeout({
                    "JobEditor.useEffect.timer": ()=>{
                        setIsEditorLoading(false);
                    }
                }["JobEditor.useEffect.timer"], 800);
                return ({
                    "JobEditor.useEffect": ()=>clearTimeout(timer)
                })["JobEditor.useEffect"];
            }
            // Set a timeout to simulate loading and ensure proper initialization
            const timer = setTimeout({
                "JobEditor.useEffect.timer": ()=>{
                    setIsEditorLoading(false);
                }
            }["JobEditor.useEffect.timer"], 800);
            return ({
                "JobEditor.useEffect": ()=>clearTimeout(timer)
            })["JobEditor.useEffect"];
        }
    }["JobEditor.useEffect"], [
        jobRequirement
    ]);
    /**
   * Updates editor content state
   * @function handleEditorChange
   * @param {string} content - The new content from the editor
   * @description Updates the editor content state with new content
   */ const handleEditorChange = (content)=>{
        setEditorContent(content);
    };
    /**
   * Checks if editor content is empty or contains only whitespace
   * @function isEditorEmpty
   * @param {string} content - The content to check
   * @returns {boolean} True if content is empty or contains only whitespace
   * @description Handles various empty content patterns including HTML tags
   */ const isEditorEmpty = (content)=>{
        if (!content) return true;
        const trimmed = content.trim();
        if (trimmed === "") return true;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EMPTY_CONTENT_PATTERNS"].includes(trimmed)) return true;
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || "";
        return textContent.trim() === "";
    };
    /**
   * Saves job requirement data to backend
   * @function handleSave
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If save operation fails
   * @description Validates content, formats job data, and saves to backend
   */ const handleSave = async ()=>{
        try {
            if (isEditorEmpty(editorContent)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("enter_job_requirement"));
                return;
            }
            setIsLoading(true);
            if (isEditMode && jobId) {
                // Edit mode - prepare data for updating existing job
                const updateData = {
                    jobId: jobId,
                    finalJobDescriptionHtml: editorContent
                };
                // Call API to update job details
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$jobRequirements$2f$jobServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateJobDescription"])(updateData); // Using type assertion for now
                if (result && result.data && result.data.success) {
                    // Show success message
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(result.data.message));
                    // empty job editor
                    setEditorContent("");
                    // Navigate to active jobs page
                    router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS);
                } else {
                    throw new Error(t("failed_to_update_job_discription"));
                }
            } else {
                // Regular mode - use existing JobEditor logic with proper typing
                console.log("jdlink", jobDetails.jd_link);
                const requestData = {
                    title: jobDetails.title,
                    employment_type: jobDetails.employment_type,
                    salary_range: jobDetails.salary_range,
                    salary_cycle: jobDetails.salary_cycle,
                    location_type: jobDetails.location_type,
                    state: jobDetails.state,
                    city: jobDetails.city,
                    role_overview: jobDetails.role_overview,
                    experience_level: jobDetails.experience_level,
                    responsibilities: jobDetails.responsibilities,
                    educations_requirement: jobDetails.educations_requirement,
                    certifications: jobDetails.certifications,
                    skills_and_software_expertise: jobDetails.skills_and_software_expertise,
                    experience_required: jobDetails.experience_required,
                    ideal_candidate_traits: jobDetails.ideal_candidate_traits,
                    about_company: jobDetails.about_company,
                    perks_benefits: jobDetails.perks_benefits,
                    tone_style: jobDetails.tone_style,
                    additional_info: jobDetails.additional_info,
                    compliance_statement: jobDetails.compliance_statement,
                    show_compliance: jobDetails.show_compliance,
                    final_job_description_html: editorContent,
                    hiring_type: jobDetails.hiring_type,
                    department_id: jobDetails.department_id,
                    jd_link: jobDetails.jd_link,
                    career_skills: careerSkills.map((skill)=>({
                            name: skill.name,
                            description: skill.description
                        })),
                    role_specific_skills: roleSpecificSkills.map((skill)=>({
                            id: skill.id,
                            name: skill.name,
                            description: skill.description
                        })),
                    culture_specific_skills: cultureSpecificSkills.map((skill)=>({
                            id: skill.id,
                            name: skill.name,
                            description: skill.description
                        }))
                };
                const saveJobResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$jobRequirements$2f$jobServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["saveJobDetails"])(requestData);
                if (saveJobResponse && saveJobResponse.data && saveJobResponse.data.success) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t(saveJobResponse.data.message));
                    // Empty redux store
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobRequirementSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearJobRequirement"])());
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobDetailsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearJobDetails"])());
                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$jobSkillsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearSkillsData"])());
                    router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS);
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t(saveJobResponse?.data?.message) || t("save_job_failed"));
                }
            }
        } catch (error) {
            console.error(t("save_job_error_log"), error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(isEditMode ? t("update_job_description_error") : t("save_job_unknown_error"));
        } finally{
            setIsLoading(false);
        }
    };
    /**
   * Downloads job requirement as PDF
   * @function handleDownloadPDF
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If PDF generation fails
   * @description Generates and downloads job requirement content as PDF
   */ // we will use later if need or remove
    // const handleDownloadPDF = async () => {
    //   if (isEditorEmpty(editorContent)) {
    //     toastMessageError(t("pdf_job_req_empty"));
    //     return;
    //   }
    //   try {
    //     // Show loading toast
    //     toast.loading(t("pdf_generating"));
    //     const generatePDFResponse = await generatePDF({
    //       jobTitle: jobDetails.title,
    //       editorContent: editorContent,
    //     });
    //     if (generatePDFResponse && generatePDFResponse.data && generatePDFResponse.data.success) {
    //       toast.dismiss();
    //       toastMessageSuccess(t(generatePDFResponse.data.message));
    //     } else {
    //       toast.dismiss();
    //       toastMessageError(t("pdf_generation_failed"));
    //     }
    //   } catch (error) {
    //     console.error("Error generating PDF:", error);
    //     toast.dismiss();
    //     toastMessageError(t("pdf_generation_failed"));
    //   }
    // };
    // Handle copy content
    const handleCopyContent = async ()=>{
        if (isEditorEmpty(editorContent)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("copy_job_req_empty"));
            return;
        }
        try {
            // We'll implement two methods of copying to support both plain text and rich text
            // Method 1: Copy as rich text (HTML)
            // Create a blob with HTML mime type
            const htmlBlob = new Blob([
                editorContent
            ], {
                type: "text/html"
            });
            // Create a temporary div to extract text content as fallback
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = editorContent;
            const textContent = tempDiv.innerText || tempDiv.textContent || "";
            const textBlob = new Blob([
                textContent
            ], {
                type: "text/plain"
            });
            // Try the modern API first (which supports HTML)
            if (navigator.clipboard.write) {
                await navigator.clipboard.write([
                    new ClipboardItem({
                        "text/html": htmlBlob,
                        "text/plain": textBlob
                    })
                ]);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t("copy_html_success"));
            } else {
                // Fallback to the text-only method
                await navigator.clipboard.writeText(textContent);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t("copy_text_success"));
            }
        } catch (error) {
            console.error("Copy error:", error);
            // Last resort fallback - if the modern approach fails
            try {
                // Create element to copy from
                const tempDiv = document.createElement("div");
                tempDiv.innerHTML = editorContent;
                document.body.appendChild(tempDiv);
                // Select the content
                const range = document.createRange();
                range.selectNodeContents(tempDiv);
                const selection = window.getSelection();
                selection?.removeAllRanges();
                selection?.addRange(range);
                // Execute copy command
                document.execCommand("copy");
                // Clean up
                selection?.removeAllRanges();
                document.body.removeChild(tempDiv);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(t("copy_success"));
            } catch (fallbackError) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("copy_fail"));
                console.error(t("copy_error"), fallbackError);
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].job_page,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "common-page-header",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "common-page-head-section",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "main-heading",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            onClick: ()=>isEditMode ? router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS) : router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.CULTURE_BASED_SKILLS}`)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                            lineNumber: 376,
                                            columnNumber: 19
                                        }, this),
                                        isEditMode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                jobTitle ? tGenerate("edit_job_description") : "",
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: jobTitle || ""
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                                    lineNumber: 381,
                                                    columnNumber: 73
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                jobDetails.title ? tGenerate("job_requirment_for") : "",
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: jobDetails?.title || ""
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                                    lineNumber: 385,
                                                    columnNumber: 79
                                                }, this)
                                            ]
                                        }, void 0, true)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                    lineNumber: 375,
                                    columnNumber: 15
                                }, this),
                                editorContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "d-flex",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            className: "clear-btn p-0 m-0",
                                            onClick: ()=>handleCopyContent(),
                                            disabled: !editorContent || isLoading,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$CopyIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                                lineNumber: 392,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                            lineNumber: 391,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$jobRequirement$2f$PdfGenerator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            content: editorContent,
                                            fileName: `job_requirement_${jobTitle}`,
                                            onLoadingChange: setIsLoading,
                                            footerLogo: "https://stratum9-images-dev.s3.us-east-1.amazonaws.com/stratum9_logo.png"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                            lineNumber: 394,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                    lineNumber: 390,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                            lineNumber: 374,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                        lineNumber: 373,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                    lineNumber: 372,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "inner-section",
                    children: !editorContent && !isEditorLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: tGenerate("no_job_requirment_generated")
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                            lineNumber: 409,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                        lineNumber: 408,
                        columnNumber: 13
                    }, this) : isEditorLoading ? /* Skeleton loader for SunEditor */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "editor-skeleton",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "skeleton-toolbar",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 416,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 417,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 418,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 419,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 420,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                lineNumber: 415,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "skeleton-content",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 424,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 425,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 426,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 427,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 428,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                lineNumber: 423,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                        lineNumber: 413,
                        columnNumber: 13
                    }, this) : isEditorLoading ? /* Skeleton loader for SunEditor */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "editor-skeleton",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "skeleton-toolbar",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 436,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 437,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 438,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 439,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-button"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 440,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                lineNumber: 435,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "skeleton-content",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 444,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 445,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 446,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 447,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "skeleton-paragraph"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                        lineNumber: 448,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                lineNumber: 443,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                        lineNumber: 433,
                        columnNumber: 13
                    }, this) : /* SunEditor component */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SunEditor, {
                        setContents: editorContent,
                        onChange: handleEditorChange,
                        getSunEditorInstance: (sunEditor)=>{
                            // Store the SunEditor instance in our ref
                            editorRef.current = sunEditor;
                            // Check if content starts with our HTML marker
                            if (editorContent.startsWith("HTML:")) {
                                setTimeout(()=>{
                                    try {
                                        // Clear the editor first
                                        sunEditor.setContents("");
                                        // Extract the HTML content without the marker
                                        const htmlContent = editorContent.substring(5);
                                        // Insert as HTML rather than setting contents
                                        sunEditor.insertHTML(htmlContent);
                                        // Force editor to process the content properly
                                        sunEditor.core.focus();
                                    } catch (err) {
                                        console.error("Error setting HTML content:", err);
                                    }
                                }, 100);
                            } else if (editorContent) {
                                setTimeout(()=>{
                                    try {
                                        sunEditor.setContents(editorContent);
                                    } catch (error) {
                                        console.error("Error setting editor content:", error);
                                    }
                                }, 100);
                            }
                        },
                        setOptions: {
                            buttonList: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$jobRequirementConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUN_EDITOR_BUTTON_LIST"],
                            minHeight: "550px",
                            defaultStyle: "font-size: 16px; font-family: Arial, sans-serif;",
                            formats: [
                                "p",
                                "div",
                                "h1",
                                "h2",
                                "h3",
                                "h4",
                                "h5",
                                "h6"
                            ],
                            font: [
                                "Arial",
                                "Verdana",
                                "Georgia",
                                "Courier New",
                                "Times New Roman"
                            ],
                            fontSize: [
                                10,
                                12,
                                14,
                                16,
                                18,
                                20,
                                22,
                                24,
                                28,
                                36,
                                48
                            ],
                            toolbarContainer: "#editor-toolbar",
                            attributesWhitelist: {
                                all: "style",
                                span: "style"
                            }
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                        lineNumber: 453,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                    lineNumber: 405,
                    columnNumber: 9
                }, this),
                editorContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "button-align py-5",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            className: "primary-btn rounded-md",
                            onClick: handleSave,
                            disabled: !editorContent || isLoading,
                            children: [
                                isEditMode ? "Update Job Description" : "Save Job Requirement",
                                " ",
                                isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                                    lineNumber: 508,
                                    columnNumber: 94
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                            lineNumber: 507,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            className: "dark-outline-btn rounded-md",
                            onClick: ()=>isEditMode ? router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS) : router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.GENERATE_JOB}`),
                            children: isEditMode ? t("back_to_jobs") : t("back_to_start")
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                            lineNumber: 510,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
                    lineNumber: 506,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
            lineNumber: 371,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/views/jobRequirement/JobEditor.tsx",
        lineNumber: 370,
        columnNumber: 5
    }, this);
}
_s(JobEditor, "Lr1f5RNvSIBa7hMrBWKt2mdz7Mk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c1 = JobEditor;
const __TURBOPACK__default__export__ = JobEditor;
var _c, _c1;
__turbopack_context__.k.register(_c, "SunEditor");
__turbopack_context__.k.register(_c1, "JobEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/job-editor/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$jobRequirement$2f$JobEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/jobRequirement/JobEditor.tsx [app-client] (ecmascript)");
"use client";
;
;
const page = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$jobRequirement$2f$JobEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/job-editor/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/job-editor/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4fa726de._.js.map
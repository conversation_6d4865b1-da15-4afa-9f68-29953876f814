(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_ab8ab5df._.js", {

"[project]/src/utils/validationSchema.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CANDIDATE_NAME_REGEX": (()=>CANDIDATE_NAME_REGEX),
    "EMAIL_REGEX": (()=>EMAIL_REGEX),
    "NAME_REGEX": (()=>NAME_REGEX),
    "departmentValidationSchema": (()=>departmentValidationSchema),
    "employeeValidationSchema": (()=>employeeValidationSchema),
    "employeesValidationSchema": (()=>employeesValidationSchema),
    "roleValidationSchema": (()=>roleValidationSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yup/index.esm.js [app-client] (ecmascript)");
;
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const NAME_REGEX = /^[a-zA-Z0-9\s.'-]+$/;
const CANDIDATE_NAME_REGEX = /^[\p{L}\s]+$/u;
const employeeValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        firstName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("first_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(1, translation("min_name")).max(50, translation("max_name")),
        lastName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("last_name_req")).matches(NAME_REGEX, {
            message: translation("valid_name"),
            excludeEmptyString: true
        }).min(1, translation("min_name")).max(50, translation("max_name")),
        email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("email_req")).email(translation("email_val_msg")).matches(EMAIL_REGEX, translation("email_val_msg")),
        department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["number"])().transform((value)=>isNaN(value) ? undefined : value).required(translation("department_req")).min(1, "Department must be selected"),
        role: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["number"])().transform((value)=>isNaN(value) ? undefined : value).required(translation("role_req")).min(1, "Role must be selected")
    });
const employeesValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        employees: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["array"])().of(employeeValidationSchema(translation)).required("At least one employee is required")
    });
const departmentValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("department_name_req")).min(2, translation("min_name")).max(50, translation("max_name"))
    });
const roleValidationSchema = (translation)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(translation("role_name_req")).min(2, translation("min_name")).max(50, translation("max_name"))
    });
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/validations/screenResumeValidations.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// validations/screenResumeValidations.ts
__turbopack_context__.s({
    "formSchemaValidation": (()=>formSchemaValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yup/index.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validationSchema$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validationSchema.ts [app-client] (ecmascript)");
;
;
const candidateSchema = (t)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])().shape({
        name: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(t("name_required")).min(2, t("name_min")).max(50, t("name_max")).matches(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validationSchema$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CANDIDATE_NAME_REGEX"], {
            message: t("name_alpha_only"),
            excludeEmptyString: true
        }),
        email: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().required(t("email_required")).email(t("email_valid")).max(50, t("email_max")),
        gender: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().required(t("gender_required")),
        resume: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mixed"])().required(t("resume_required")),
        assessment: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mixed"])().optional().nullable(),
        additionalInfo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["string"])().trim().optional().max(200, t("additional_max"))
    });
const formSchemaValidation = (t)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["object"])({
        candidates: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yup$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["array"])().of(candidateSchema(t)).min(1, t("at_least_one_candidate"))
    });
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/constants/screenResumeConstant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "APPLICATION_UPDATE_STATUS": (()=>APPLICATION_UPDATE_STATUS),
    "GENDER_OPTIONS": (()=>GENDER_OPTIONS),
    "InterviewTabType": (()=>InterviewTabType)
});
const GENDER_OPTIONS = [
    {
        value: "Male",
        label: "Male"
    },
    {
        value: "Female",
        label: "Female"
    }
];
var InterviewTabType = /*#__PURE__*/ function(InterviewTabType) {
    InterviewTabType["UPCOMING"] = "UpcomingInterviews";
    InterviewTabType["PAST"] = "PastInterviews";
    return InterviewTabType;
}({});
const APPLICATION_UPDATE_STATUS = {
    PROMOTED: "Promoted",
    DEMOTED: "Demoted"
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function BackArrowIcon({ onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: "cursor-pointer me-3",
        width: "26",
        height: "26",
        viewBox: "0 0 32 32",
        fill: "none",
        onClick: onClick,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z",
            fill: "#333333"
        }, void 0, false, {
            fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/svgComponents/BackArrowIcon.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = BackArrowIcon;
const __TURBOPACK__default__export__ = BackArrowIcon;
var _c;
__turbopack_context__.k.register(_c, "BackArrowIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/HoldIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function HoldIcon({ className, PrimaryColor }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M20.0746 0H14.0859C13.827 0 13.6172 0.209859 13.6172 0.46875C13.6172 0.727641 13.827 0.9375 14.0859 0.9375H20.0746C20.932 0.9375 21.6296 1.63505 21.6296 2.49239V21.5076C21.6296 22.365 20.932 23.0625 20.0746 23.0625H3.92505C3.06766 23.0625 2.37012 22.3649 2.37012 21.5075V2.49244C2.37012 1.63505 3.06766 0.9375 3.92505 0.9375H9.96129C10.2202 0.9375 10.43 0.727641 10.43 0.46875C10.43 0.209859 10.2202 0 9.96129 0H3.92505C2.55073 0 1.43262 1.11811 1.43262 2.49244V21.5076C1.43262 22.8819 2.55073 24 3.92505 24H20.0746C21.4489 24 22.5671 22.8819 22.5671 21.5076V2.49239C22.5671 1.11811 21.4489 0 20.0746 0Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M8.53423 4.54633V7.32878C8.07003 6.94197 7.49131 6.73145 6.88053 6.73145C6.62164 6.73145 6.41178 6.94131 6.41178 7.2002V10.7753C6.33068 13.8414 8.90553 16.5269 11.9797 16.528C15.0649 16.528 17.6343 13.8204 17.5475 10.7477V5.84359C17.5475 5.03495 16.8911 4.37702 16.0841 4.37702H16.0752C15.8895 4.37702 15.7118 4.41184 15.5483 4.47531C15.5211 3.66695 14.9015 3.06419 14.0858 3.06419C13.8903 3.06419 13.7036 3.10281 13.5329 3.1728C13.4017 2.47075 12.7994 1.95508 12.0548 1.95508C11.3246 1.95508 10.7143 2.47919 10.5789 3.17167C10.4058 3.10356 10.2146 3.06648 10.0113 3.06648C9.19686 3.06658 8.53423 3.73037 8.53423 4.54633ZM10.5508 4.54633C10.5508 4.57023 10.5507 9.1585 10.5507 9.1585C10.5507 9.41739 10.7606 9.62725 11.0195 9.62725C11.2784 9.62725 11.4882 9.41739 11.4882 9.1585L11.4883 3.46216C11.4883 3.14809 11.7424 2.89262 12.0547 2.89262C12.3834 2.89262 12.6219 3.13216 12.6219 3.46216L12.6211 9.01787C12.6211 9.27677 12.8309 9.48662 13.0898 9.48662C13.3487 9.48662 13.5586 9.27677 13.5586 9.01787C13.5586 9.01787 13.5594 4.5363 13.5594 4.53077C13.5594 4.23902 13.7955 4.00169 14.0858 4.00169C14.3954 4.00169 14.6117 4.21923 14.6117 4.53077L14.6116 9.1585C14.6116 9.41739 14.8214 9.62725 15.0803 9.62725C15.3392 9.62725 15.5491 9.41739 15.5491 9.1585C15.5491 9.1585 15.5492 5.86773 15.5492 5.84359C15.5492 5.55184 15.7851 5.31452 16.0752 5.31452H16.0841C16.3741 5.31452 16.61 5.55184 16.61 5.84359V10.7402C16.5528 13.2973 14.6555 15.5905 11.9798 15.5905C9.36546 15.5896 7.34928 13.3261 7.34928 10.7817V7.73627C7.61103 7.81352 7.85107 7.95569 8.04954 8.15462C8.30107 8.40686 8.53015 9.11251 8.53404 9.64248V10.7503C8.53404 10.9835 8.70317 11.181 8.93412 11.214C8.98432 11.2217 10.1696 11.4158 10.7034 12.5455C10.7835 12.7149 10.9519 12.8141 11.1275 12.8141C11.1946 12.8141 11.2628 12.7996 11.3275 12.769C11.5616 12.6585 11.6616 12.379 11.5511 12.145C11.0166 11.0139 10.0268 10.5572 9.47154 10.3838L9.47173 4.54637C9.47173 4.24736 9.71379 4.00408 10.0113 4.00408C10.4095 4.00408 10.5508 4.29616 10.5508 4.54633Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 15,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M14.0444 17.6445C13.7855 17.6445 13.5757 17.8544 13.5757 18.1133V21.1865C13.5757 21.6615 14.042 21.6582 14.3669 21.6582C14.5327 21.6582 14.7728 21.6573 15.1241 21.6553C15.3829 21.6538 15.5917 21.4428 15.5902 21.1839C15.5888 20.9259 15.3792 20.7178 15.1215 20.7178C15.1206 20.7178 15.1197 20.7178 15.1189 20.7178C14.913 20.719 14.6961 20.7198 14.5132 20.7202V18.1133C14.5132 17.8544 14.3033 17.6445 14.0444 17.6445Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M7.48963 21.6579C7.74852 21.6579 7.95838 21.448 7.95838 21.1891V18.1133C7.95838 17.8544 7.74852 17.6445 7.48963 17.6445C7.23074 17.6445 7.02088 17.8544 7.02088 18.1133V19.1297H5.82983V18.1133C5.82983 17.8544 5.61997 17.6445 5.36108 17.6445C5.10219 17.6445 4.89233 17.8544 4.89233 18.1133V21.1891C4.89233 21.448 5.10219 21.6579 5.36108 21.6579C5.61997 21.6579 5.82983 21.448 5.82983 21.1891V20.0672H7.02088V21.1891C7.02088 21.448 7.23074 21.6579 7.48963 21.6579Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M16.2369 21.1799C16.2531 21.3965 16.4816 21.6138 16.7056 21.6138C16.7312 21.6137 17.3346 21.6113 17.5761 21.6071C18.5271 21.5905 19.2173 20.7685 19.2173 19.6526C19.2173 18.4795 18.5445 17.6914 17.5431 17.6914H16.6978C16.436 17.6914 16.229 17.9032 16.229 18.1632C16.2291 18.1632 16.232 21.1528 16.2369 21.1799ZM17.5431 18.6289C18.2267 18.6289 18.2798 19.4124 18.2798 19.6526C18.2798 20.1526 18.0571 20.6611 17.5597 20.6697C17.4683 20.6713 17.3194 20.6727 17.171 20.6737C17.1699 20.3296 17.168 18.9877 17.1673 18.6289H17.5431Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M8.76245 19.6512C8.76245 20.7577 9.66264 21.6579 10.7691 21.6579C11.8756 21.6579 12.7758 20.7577 12.7758 19.6512C12.7758 18.5447 11.8756 17.6445 10.7691 17.6445C9.66264 17.6445 8.76245 18.5447 8.76245 19.6512ZM11.8383 19.6512C11.8383 20.2407 11.3587 20.7204 10.7691 20.7204C10.1796 20.7204 9.69995 20.2407 9.69995 19.6512C9.69995 19.0617 10.1796 18.582 10.7691 18.582C11.3587 18.582 11.8383 19.0617 11.8383 19.6512Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M11.6406 0.647517C11.7161 0.831736 11.9093 0.951173 12.108 0.935705C12.3077 0.920142 12.4789 0.776611 12.528 0.582267C12.6287 0.183783 12.1825 -0.141202 11.8327 0.0667358C11.6361 0.183548 11.5527 0.436392 11.6406 0.647517Z",
                fill: PrimaryColor ? "#436EB6" : "#CB9932"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/HoldIcon.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_c = HoldIcon;
const __TURBOPACK__default__export__ = HoldIcon;
var _c;
__turbopack_context__.k.register(_c, "HoldIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
;
;
/**
 * Wrapper component for input fields
 * @param {string} className - Class name for the input field
 * @returns {JSX.Element} - Wrapper component
 */ const InputWrapper = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `form-group ${className ?? ""}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 10,
        columnNumber: 3
    }, this);
_c = InputWrapper;
/**
 * Label component for input fields
 * @param {string} children - Label text
 * @returns {JSX.Element} - Label component
 */ InputWrapper.Label = function({ children, htmlFor, required, className, onClick, style }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
        htmlFor: htmlFor,
        className: className,
        onClick: onClick,
        style: style,
        children: [
            children,
            required ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("sup", {
                children: "*"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/InputWrapper.tsx",
                lineNumber: 37,
                columnNumber: 19
            }, this) : null
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
/**
 * Error component for input fields to display error message
 * @param { string } message - Error message
 * @param { React.CSSProperties } style - Optional style object
 * @returns { JSX.Element } - Error component
 */ InputWrapper.Error = function({ message, style }) {
    return message ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
        className: "auth-msg error",
        style: style,
        children: message
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this) : null;
};
/**
 * Icon component for input fields
 * @param { string } src - Icon source
 * @param { function } onClick - Function to be called on click
 * @returns { JSX.Element } - Icon component
 */ InputWrapper.Icon = function({ children, // src,
onClick }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: "show-icon",
        type: "button",
        onClick: onClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/formElements/InputWrapper.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InputWrapper;
var _c;
__turbopack_context__.k.register(_c, "InputWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/DeleteIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function DeleteIcon({ className }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        className: className,
        width: "25",
        height: "28",
        viewBox: "0 0 25 28",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                fillRule: "evenodd",
                clipRule: "evenodd",
                d: "M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z",
                fill: "#D00000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgComponents/DeleteIcon.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = DeleteIcon;
const __TURBOPACK__default__export__ = DeleteIcon;
var _c;
__turbopack_context__.k.register(_c, "DeleteIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/svgComponents/InfoIcon.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$tooltip$2f$dist$2f$react$2d$tooltip$2e$min$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-tooltip/dist/react-tooltip.min.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
function InfoIcon({ tooltip, id, place = "bottom", className }) {
    _s();
    const generatedId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"])();
    const anchorId = id || `info-icon-${generatedId}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                id: anchorId,
                className: className,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "15",
                    height: "15",
                    viewBox: "0 0 23 23",
                    style: {
                        cursor: "pointer"
                    },
                    fill: "none",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                            clipPath: "url(#clip0_9605_3144)",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M11.5 2.15625C9.65198 2.15625 7.84547 2.70425 6.30889 3.73096C4.77232 4.75766 3.57471 6.21695 2.8675 7.9243C2.1603 9.63165 1.97526 11.5104 2.33579 13.3229C2.69632 15.1354 3.58623 16.8003 4.89298 18.107C6.19972 19.4138 7.86462 20.3037 9.67713 20.6642C11.4896 21.0247 13.3684 20.8397 15.0757 20.1325C16.783 19.4253 18.2423 18.2277 19.269 16.6911C20.2958 15.1545 20.8438 13.348 20.8438 11.5C20.8411 9.02269 19.8559 6.64759 18.1041 4.89586C16.3524 3.14413 13.9773 2.15887 11.5 2.15625ZM11.1406 6.46875C11.3539 6.46875 11.5623 6.53198 11.7396 6.65045C11.9169 6.76891 12.0551 6.93729 12.1367 7.13429C12.2183 7.3313 12.2396 7.54807 12.198 7.75721C12.1564 7.96634 12.0538 8.15845 11.903 8.30922C11.7522 8.46 11.5601 8.56268 11.351 8.60428C11.1418 8.64588 10.925 8.62453 10.728 8.54293C10.531 8.46133 10.3627 8.32315 10.2442 8.14585C10.1257 7.96855 10.0625 7.76011 10.0625 7.54688C10.0625 7.26094 10.1761 6.98671 10.3783 6.78453C10.5805 6.58234 10.8547 6.46875 11.1406 6.46875ZM12.2188 16.5312C11.8375 16.5312 11.4719 16.3798 11.2023 16.1102C10.9327 15.8406 10.7813 15.475 10.7813 15.0938V11.5C10.5906 11.5 10.4078 11.4243 10.273 11.2895C10.1382 11.1547 10.0625 10.9719 10.0625 10.7812C10.0625 10.5906 10.1382 10.4078 10.273 10.273C10.4078 10.1382 10.5906 10.0625 10.7813 10.0625C11.1625 10.0625 11.5281 10.214 11.7977 10.4835C12.0673 10.7531 12.2188 11.1188 12.2188 11.5V15.0938C12.4094 15.0938 12.5922 15.1695 12.727 15.3043C12.8618 15.4391 12.9375 15.6219 12.9375 15.8125C12.9375 16.0031 12.8618 16.1859 12.727 16.3207C12.5922 16.4555 12.4094 16.5312 12.2188 16.5312Z",
                                fill: "#436EB6"
                            }, void 0, false, {
                                fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                                lineNumber: 19,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                            lineNumber: 18,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                                id: "clip0_9605_3144",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                    width: "23",
                                    height: "23",
                                    fill: "white"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                                    lineNumber: 26,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                                lineNumber: 25,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                            lineNumber: 24,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                    lineNumber: 17,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$tooltip$2f$dist$2f$react$2d$tooltip$2e$min$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                anchorSelect: `#${anchorId}`,
                className: "responsive-tooltip",
                place: place,
                children: tooltip
            }, void 0, false, {
                fileName: "[project]/src/components/svgComponents/InfoIcon.tsx",
                lineNumber: 31,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(InfoIcon, "P3bvVUypbBAHy0F8g4TFKgtieUM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"]
    ];
});
_c = InfoIcon;
const __TURBOPACK__default__export__ = InfoIcon;
var _c;
__turbopack_context__.k.register(_c, "InfoIcon");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "commonPage-module-scss-module__em0r7a__active",
  "add_another_candidate_link": "commonPage-module-scss-module__em0r7a__add_another_candidate_link",
  "approved_status_indicator": "commonPage-module-scss-module__em0r7a__approved_status_indicator",
  "border_none": "commonPage-module-scss-module__em0r7a__border_none",
  "candidate_card": "commonPage-module-scss-module__em0r7a__candidate_card",
  "candidate_card_header": "commonPage-module-scss-module__em0r7a__candidate_card_header",
  "candidate_qualification_page": "commonPage-module-scss-module__em0r7a__candidate_qualification_page",
  "candidates_list_page": "commonPage-module-scss-module__em0r7a__candidates_list_page",
  "candidates_list_section": "commonPage-module-scss-module__em0r7a__candidates_list_section",
  "career-skill-card": "commonPage-module-scss-module__em0r7a__career-skill-card",
  "dashboard__stat": "commonPage-module-scss-module__em0r7a__dashboard__stat",
  "dashboard__stat_design": "commonPage-module-scss-module__em0r7a__dashboard__stat_design",
  "dashboard__stat_image": "commonPage-module-scss-module__em0r7a__dashboard__stat_image",
  "dashboard__stat_label": "commonPage-module-scss-module__em0r7a__dashboard__stat_label",
  "dashboard__stat_value": "commonPage-module-scss-module__em0r7a__dashboard__stat_value",
  "dashboard__stats": "commonPage-module-scss-module__em0r7a__dashboard__stats",
  "dashboard_inner_head": "commonPage-module-scss-module__em0r7a__dashboard_inner_head",
  "dashboard_page": "commonPage-module-scss-module__em0r7a__dashboard_page",
  "header_tab": "commonPage-module-scss-module__em0r7a__header_tab",
  "inner_heading": "commonPage-module-scss-module__em0r7a__inner_heading",
  "inner_page": "commonPage-module-scss-module__em0r7a__inner_page",
  "input_type_file": "commonPage-module-scss-module__em0r7a__input_type_file",
  "interview_form_icon": "commonPage-module-scss-module__em0r7a__interview_form_icon",
  "job_info": "commonPage-module-scss-module__em0r7a__job_info",
  "job_page": "commonPage-module-scss-module__em0r7a__job_page",
  "manual_upload_resume": "commonPage-module-scss-module__em0r7a__manual_upload_resume",
  "operation_admins_img": "commonPage-module-scss-module__em0r7a__operation_admins_img",
  "resume_page": "commonPage-module-scss-module__em0r7a__resume_page",
  "search_box": "commonPage-module-scss-module__em0r7a__search_box",
  "section_heading": "commonPage-module-scss-module__em0r7a__section_heading",
  "section_name": "commonPage-module-scss-module__em0r7a__section_name",
  "selected": "commonPage-module-scss-module__em0r7a__selected",
  "selecting": "commonPage-module-scss-module__em0r7a__selecting",
  "selection": "commonPage-module-scss-module__em0r7a__selection",
  "skills_info_box": "commonPage-module-scss-module__em0r7a__skills_info_box",
  "skills_tab": "commonPage-module-scss-module__em0r7a__skills_tab",
  "text_xs": "commonPage-module-scss-module__em0r7a__text_xs",
  "upload_resume_page": "commonPage-module-scss-module__em0r7a__upload_resume_page",
});
}}),
"[project]/src/services/screenResumeServices.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "changeApplicationStatus": (()=>changeApplicationStatus),
    "getAllPendingJobApplications": (()=>getAllPendingJobApplications),
    "getPresignedUrl": (()=>getPresignedUrl),
    "processFileUpload": (()=>processFileUpload),
    "uploadManualCandidate": (()=>uploadManualCandidate),
    "uploadToS3": (()=>uploadToS3)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/http.ts [app-client] (ecmascript)");
;
;
const getPresignedUrl = async (file)=>{
    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileType", file.type);
    formData.append("fileName", file.name);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_PRESIGNED_URL, formData, {
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
};
const uploadToS3 = async (presignedUrl, file)=>{
    return fetch(presignedUrl, {
        method: "PUT",
        body: file,
        headers: {
            "Content-Type": file.type
        }
    });
};
const processFileUpload = async (file)=>{
    try {
        // Get presigned URL
        const presignedUrlResponse = await getPresignedUrl(file);
        if (!presignedUrlResponse.data) {
            throw new Error("Failed to get presigned URL");
        }
        const responseData = presignedUrlResponse.data;
        // The response might have data nested inside another data property
        const urlData = responseData.data;
        if (!urlData.presignedUrl || !urlData.fileUrl) {
            console.error("Missing URL information in response:", urlData);
            throw new Error("Missing URL information in response");
        }
        const { presignedUrl, fileUrl, fileText } = urlData;
        // Upload file to S3
        const uploadResponse = await uploadToS3(presignedUrl, file);
        if (!uploadResponse.ok) {
            throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);
        }
        // Return the file URL and flag for backend extraction
        return {
            fileUrl,
            fileText: fileText,
            presignedUrl
        };
    } catch (error) {
        console.error("Error processing file upload:", error);
        // Include error details in the console for debugging
        if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
        }
        throw error;
    }
};
const getAllPendingJobApplications = async (params)=>{
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        if (params.limit) queryParams.append("limit", params.limit.toString());
        // Always include offset parameter, even when it's 0
        queryParams.append("offset", params.offset !== undefined ? params.offset.toString() : "0");
        if (params.job_id) queryParams.append("job_id", params.job_id.toString());
        if (params.status) queryParams.append("status", params.status);
        if (params.hiring_manager_id) queryParams.append("hiring_manager_id", params.hiring_manager_id.toString());
        if (params.organization_id) queryParams.append("organization_id", params.organization_id.toString());
        // Make API request
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].get(url);
    } catch (error) {
        console.error("Error fetching job applications:", error);
        throw error;
    }
};
const uploadManualCandidate = async (uploadManualCandidateData)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.MANUAL_CANDIDATE_UPLOAD, uploadManualCandidateData);
};
const changeApplicationStatus = async (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$http$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["http"].post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeScreen.CHANGE_APPLICATION_STATUS, data);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/views/resume/ManualUploadResume.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/yup/dist/yup.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$validations$2f$screenResumeValidations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/validations/screenResumeValidations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/screenResumeConstant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/loader/Loader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/BackArrowIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$HoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/HoldIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/InputWrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/DeleteIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgComponents/InfoIcon.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helper.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/styles/commonPage.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/screenResumeServices.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * ManualUploadResume Component
 *
 * Allows hiring managers to manually upload candidate resumes and assessments.
 * Supports adding multiple candidates (up to 5) with validation for required fields.
 *
 * @returns {JSX.Element} The rendered ManualUploadResume component
 */ function ManualUploadResume({ params, searchParams }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const scrollContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const paramsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(params);
    const searchParamsPromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(searchParams);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    // Initialize form with validation schema
    const { control, handleSubmit, formState: { errors }, reset } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$yup$2f$dist$2f$yup$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["yupResolver"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$validations$2f$screenResumeValidations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formSchemaValidation"])(t)),
        defaultValues: {
            candidates: [
                {
                    name: "",
                    email: "",
                    gender: "",
                    resume: null,
                    assessment: null,
                    additionalInfo: ""
                }
            ]
        }
    });
    // Initialize field array for dynamic candidates
    const { fields, append, remove } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFieldArray"])({
        control,
        name: "candidates"
    });
    // State for loading status during form submission
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [candidateErrors, setCandidateErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [candidateSuccess, setCandidateSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [candidatesToRemove, setCandidatesToRemove] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const resumeFileRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const assessmentFileRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Effect to handle candidate removal after successful processing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ManualUploadResume.useEffect": ()=>{
            if (candidatesToRemove.length > 0) {
                console.log("Removing candidates at indices:", candidatesToRemove);
                // Remove candidates from highest index to lowest to avoid index shifting
                const sortedIndices = [
                    ...candidatesToRemove
                ].sort({
                    "ManualUploadResume.useEffect.sortedIndices": (a, b)=>b - a
                }["ManualUploadResume.useEffect.sortedIndices"]);
                sortedIndices.forEach({
                    "ManualUploadResume.useEffect": (index)=>{
                        console.log(`Removing candidate at index ${index}`);
                        remove(index);
                    }
                }["ManualUploadResume.useEffect"]);
                // Clear the removal list
                setCandidatesToRemove([]);
                // If all candidates were removed, add a new empty one
                if (candidatesToRemove.length === fields.length) {
                    console.log("All candidates were removed, adding new empty candidate");
                    append({
                        name: "",
                        email: "",
                        gender: "",
                        resume: null,
                        assessment: null,
                        additionalInfo: ""
                    });
                }
            }
        }
    }["ManualUploadResume.useEffect"], [
        candidatesToRemove,
        remove,
        append,
        fields.length
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ManualUploadResume.useEffect": ()=>{
            if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {
                router.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS);
            }
        }
    }["ManualUploadResume.useEffect"], [
        paramsPromise.jobId,
        searchParamsPromise?.title
    ]);
    /**
   * Handles form submission for candidate uploads
   *
   * Processes the submitted form data, uploads files to S3, transforms File objects to URLs,
   * calls the API to upload candidates, shows success/error messages, and redirects on success.
   *
   * @param {IFormValues} data - The validated form data containing candidate information
   * @returns {Promise<void>}
   */ const onSubmit = async (data)=>{
        console.log("onSubmit=========>", data);
        console.log("Number of candidates being submitted:", data.candidates.length);
        console.log("Current fields length:", fields.length);
        console.log("Candidates data:", data.candidates.map((c, i)=>({
                index: i,
                name: c.name,
                email: c.email
            })));
        try {
            setIsSubmitting(true);
            setCandidateErrors({}); // Clear previous errors
            setCandidateSuccess({}); // Clear previous success states
            // Transform candidates by uploading files to S3 and converting File objects to URLs
            const transformedCandidates = [];
            const uploadErrors = {};
            // Process candidates sequentially to handle errors properly
            for(let index = 0; index < data.candidates.length; index++){
                const candidate = data.candidates[index];
                let hasUploadError = false;
                let errorMessage = "";
                try {
                    // Generate unique file paths for each candidate's files
                    const timestamp = Date.now() + index; // Add index to ensure uniqueness
                    // Process resume file (required)
                    let resumeUrl = "";
                    if (candidate.resume) {
                        const resumeFilePath = `manual-uploads/${candidate.resume.name}-resume-${timestamp}.pdf`;
                        const uploadResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadFileOnS3"])(candidate.resume, resumeFilePath);
                        console.log("resumeUrl=========>", uploadResult);
                        if (!uploadResult) {
                            hasUploadError = true;
                            errorMessage = "Resume file upload failed - please try again";
                        } else {
                            resumeUrl = uploadResult;
                        }
                    } else {
                        hasUploadError = true;
                        errorMessage = "Resume file is required";
                    }
                    // Process assessment file (optional) - only if resume upload succeeded
                    let assessmentUrl = "";
                    if (!hasUploadError && candidate.assessment) {
                        const assessmentFilePath = `manual-uploads/${candidate.assessment.name}-assessment-${timestamp}.pdf`;
                        const uploadResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadFileOnS3"])(candidate.assessment, assessmentFilePath);
                        console.log("assessmentUrl=========>", uploadResult);
                        if (!uploadResult) {
                            hasUploadError = true;
                            errorMessage = "Assessment file upload failed - please try again";
                        } else {
                            assessmentUrl = uploadResult;
                        }
                    }
                    // Only add to transformedCandidates if no upload errors occurred
                    if (!hasUploadError) {
                        transformedCandidates.push({
                            name: candidate.name,
                            email: candidate.email,
                            gender: candidate.gender,
                            resume_file: resumeUrl,
                            assessment_file: assessmentUrl,
                            additional_details: candidate.additionalInfo
                        });
                    } else {
                        // Store the upload error for this candidate
                        uploadErrors[index] = errorMessage;
                    }
                } catch (fileUploadError) {
                    console.error(`Error uploading files for candidate ${index + 1}:`, fileUploadError);
                    uploadErrors[index] = fileUploadError?.message || `Failed to upload files`;
                }
            }
            // Set upload errors immediately on the UI
            if (Object.keys(uploadErrors).length > 0) {
                setCandidateErrors(uploadErrors);
            }
            const jobId = Number(paramsPromise.jobId);
            console.log("transformedCandidates=========>", transformedCandidates);
            // Only proceed with backend submission if we have candidates with successful uploads
            if (transformedCandidates.length > 0) {
                // Upload candidates with transformed data (URLs instead of File objects)
                const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$screenResumeServices$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadManualCandidate"])({
                    candidates: transformedCandidates,
                    job_id: jobId
                });
                console.log("Upload response:", response);
                if (response.data && response.data.results && response.data.results.length > 0) {
                    // Process results to show per-candidate status
                    const errors = {
                        ...uploadErrors
                    }; // Start with upload errors
                    const success = {};
                    let successCount = 0;
                    let errorCount = Object.keys(uploadErrors).length; // Count upload errors
                    // Create mapping from transformed candidates back to original indices
                    let transformedIndex = 0;
                    for(let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++){
                        // Skip candidates that had upload errors
                        if (uploadErrors[originalIndex]) {
                            continue;
                        }
                        // Process backend result for this candidate
                        if (transformedIndex < response.data.results.length) {
                            const result = response.data.results[transformedIndex];
                            if (!result.success && result.error) {
                                errors[originalIndex] = result.error;
                                success[originalIndex] = false;
                                errorCount++;
                            } else if (result.success) {
                                success[originalIndex] = true;
                                successCount++;
                            }
                            transformedIndex++;
                        }
                    }
                    // Identify successful candidates to remove
                    const successfulIndices = Object.keys(success).filter((index)=>success[parseInt(index)]).map((index)=>parseInt(index));
                    console.log("Successful candidates to remove:", successfulIndices);
                    // Update error indices for remaining candidates (before removal)
                    const updatedErrors = {};
                    let newIndex = 0;
                    for(let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++){
                        // Skip successful candidates (they will be removed)
                        if (success[originalIndex]) {
                            continue;
                        }
                        // If this candidate had an error, map it to the new index
                        if (errors[originalIndex]) {
                            updatedErrors[newIndex] = errors[originalIndex];
                        }
                        newIndex++;
                    }
                    console.log("Updated errors after index remapping:", updatedErrors);
                    // Set the updated states
                    setCandidateErrors(updatedErrors);
                    setCandidateSuccess({}); // Clear success state since successful candidates will be removed
                    // Trigger candidate removal via useEffect
                    if (successfulIndices.length > 0) {
                        setCandidatesToRemove(successfulIndices);
                    }
                    // Show summary toast message only
                    if (successCount > 0 && errorCount > 0) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(`${successCount} candidates processed successfully`);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(`${errorCount} candidates failed to process`);
                    } else if (successCount > 0) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])(`All ${successCount} candidates processed successfully`);
                    } else if (errorCount > 0) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(`All ${errorCount} candidates failed to process`);
                    }
                // No redirect - user stays on the same page
                } else {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(response.data?.message || t("something_went_wrong"));
                }
            } else {
                // No candidates had successful uploads - all had upload errors
                const totalErrors = Object.keys(uploadErrors).length;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(`All ${totalErrors} candidates failed to upload files`);
            }
        } catch (error) {
            console.error("Error uploading candidates:", error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("something_went_wrong"));
        } finally{
            setIsSubmitting(false);
        }
    };
    /**
   * Validates if a file meets PDF requirements
   * @param file - The file to validate
   * @returns boolean - True if validation passes, False otherwise
   */ const handleFileChange = (file)=>{
        if (!file) {
            return false;
        }
        if (file.size > __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PDF_FILE_SIZE_LIMIT"]) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("pdf_size_five_mb"));
            return false;
        }
        // Validate file name contains PDF_FILE_NAME
        if (!file.name.includes(".pdf")) {
            console.log("file name not contains pdf");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("unsupported_file_type"));
            return false;
        }
        // Validate file type (PDF only)
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PDF_FILE_TYPE"].includes(file.type)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("pdf_only"));
            return false;
        }
        // Validate file size (max 5MB)
        if (file.name.length > 50) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageError"])(t("pdf_name"));
            return false;
        }
        return true;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: scrollContainerRef,
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].resume_page} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].manual_upload_resume}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].inner_page,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "common-page-header",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "common-page-head-section",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "main-heading",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$BackArrowIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].JOBS.ACTIVE_JOBS}`)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                lineNumber: 357,
                                                columnNumber: 19
                                            }, this),
                                            t("manual_upload_resume"),
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: searchParamsPromise?.title
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                lineNumber: 358,
                                                columnNumber: 47
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                        lineNumber: 356,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "clear-btn p-0 color-primary",
                                        onClick: ()=>router.push(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${paramsPromise.jobId}` + `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$HoldIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                className: "me-2",
                                                PrimaryColor: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                lineNumber: 369,
                                                columnNumber: 19
                                            }, this),
                                            " ",
                                            t("view_panding_action")
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                        lineNumber: 360,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                lineNumber: 355,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                            lineNumber: 354,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                        lineNumber: 353,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                        onSubmit: handleSubmit((data)=>{
                            setTimeout(()=>{
                                window.scrollTo({
                                    top: 0,
                                    behavior: "smooth"
                                });
                            }, 50);
                            onSubmit(data);
                        }),
                        children: [
                            fields.map((field, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidate_card,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `d-flex align-items-center justify-content-between ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidate_card_header}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    children: [
                                                        t("candidate"),
                                                        " 0",
                                                        index + 1
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                    lineNumber: 386,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "d-flex align-items-center gap-2",
                                                    children: [
                                                        candidateSuccess[index] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-success",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "mb-0",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "✅ Successfully processed"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 392,
                                                                    columnNumber: 45
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                lineNumber: 392,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 391,
                                                            columnNumber: 23
                                                        }, this),
                                                        candidateErrors[index] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-danger",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "mb-0",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: [
                                                                        "❌ ",
                                                                        candidateErrors[index]
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 397,
                                                                    columnNumber: 45
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                lineNumber: 397,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 396,
                                                            columnNumber: 23
                                                        }, this),
                                                        index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            type: "button",
                                                            onClick: ()=>{
                                                                remove(index);
                                                                // Clear status for this candidate when removed
                                                                const newErrors = {
                                                                    ...candidateErrors
                                                                };
                                                                const newSuccess = {
                                                                    ...candidateSuccess
                                                                };
                                                                delete newErrors[index];
                                                                delete newSuccess[index];
                                                                setCandidateErrors(newErrors);
                                                                setCandidateSuccess(newSuccess);
                                                                setCandidatesToRemove([]); // Clear any pending removals
                                                            },
                                                            className: "clear-btn p-0",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$DeleteIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                className: "p-1"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                lineNumber: 416,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 401,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                    lineNumber: 389,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                            lineNumber: 385,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].candidate_card_body,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "row",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-6",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.name`,
                                                                    required: true,
                                                                    children: [
                                                                        t("name"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Enter the candidate’s full name.",
                                                                            id: `name-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 428,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 426,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                    control: control,
                                                                    name: `candidates.${index}.name`,
                                                                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                            ...field,
                                                                            id: `candidates.${index}.name`,
                                                                            type: "text",
                                                                            placeholder: "Please enter full name of the candidate",
                                                                            className: "form-control"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 434,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 430,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.name?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 444,
                                                                    columnNumber: 62
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 425,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 424,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-6",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.email`,
                                                                    required: true,
                                                                    children: [
                                                                        t("email"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Provide a valid email address to contact the candidate regarding job-related updates.",
                                                                            id: `email-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 453,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 451,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                    control: control,
                                                                    name: `candidates.${index}.email`,
                                                                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                            ...field,
                                                                            id: `candidates.${index}.email`,
                                                                            type: "email",
                                                                            placeholder: "Please enter candidate’s email address",
                                                                            className: "form-control"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 463,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 459,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.email?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 472,
                                                                    columnNumber: 63
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 450,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 449,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-6",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.gender`,
                                                                    required: true,
                                                                    children: [
                                                                        t("gender"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Select the candidate’s gender to support inclusive hiring analytics and reporting.",
                                                                            id: `gender-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 481,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 479,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                    control: control,
                                                                    name: `candidates.${index}.gender`,
                                                                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                                            ...field,
                                                                            id: `candidates.${index}.gender`,
                                                                            className: "form-control",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                                    value: "",
                                                                                    disabled: true,
                                                                                    children: t("select_gender")
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                                    lineNumber: 492,
                                                                                    columnNumber: 31
                                                                                }, void 0),
                                                                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$screenResumeConstant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GENDER_OPTIONS"].map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                                        value: option.value,
                                                                                        children: option.label
                                                                                    }, option.value, false, {
                                                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                                        lineNumber: 496,
                                                                                        columnNumber: 33
                                                                                    }, void 0))
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 491,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 487,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.gender && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.gender?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 503,
                                                                    columnNumber: 64
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 478,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 477,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-6",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.resume`,
                                                                    required: true,
                                                                    children: [
                                                                        t("upload_resume"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Upload the candidate’s latest resume in PDF format for evaluation",
                                                                            id: `resume-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 512,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 510,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].input_type_file,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                        name: `candidates.${index}.resume`,
                                                                        control: control,
                                                                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                id: `candidates.${index}.resume`,
                                                                                type: "file",
                                                                                ref: resumeFileRef,
                                                                                accept: ".pdf",
                                                                                onChange: (e)=>{
                                                                                    const file = e.target.files?.[0] || null;
                                                                                    console.log("file==============>", file);
                                                                                    if (file) {
                                                                                        if (handleFileChange(file)) {
                                                                                            field.onChange(file);
                                                                                        } else {
                                                                                            e.target.value = "";
                                                                                            field.onChange(null);
                                                                                        }
                                                                                    } else {
                                                                                        field.onChange(null);
                                                                                    }
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                                lineNumber: 523,
                                                                                columnNumber: 31
                                                                            }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                        lineNumber: 519,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 518,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.resume && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.resume?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 546,
                                                                    columnNumber: 64
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 509,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 508,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-6",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.assessment`,
                                                                    children: [
                                                                        t("upload_assesment"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Attach any completed assessments or test results relevant to the job role.",
                                                                            id: `assessment-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 555,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 553,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].input_type_file,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                        name: `candidates.${index}.assessment`,
                                                                        control: control,
                                                                        render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                id: `candidates.${index}.assessment`,
                                                                                type: "file",
                                                                                accept: ".pdf",
                                                                                ref: assessmentFileRef,
                                                                                onChange: (e)=>{
                                                                                    const file = e.target.files?.[0] || null;
                                                                                    if (file) {
                                                                                        if (handleFileChange(file)) {
                                                                                            field.onChange(file);
                                                                                        } else {
                                                                                            e.target.value = "";
                                                                                            field.onChange(null);
                                                                                        }
                                                                                    } else {
                                                                                        field.onChange(null);
                                                                                    }
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                                lineNumber: 566,
                                                                                columnNumber: 31
                                                                            }, void 0)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                        lineNumber: 562,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 561,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.assessment && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.assessment?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 589,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 552,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 551,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "col-md-12",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$InputWrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Label, {
                                                                    htmlFor: `candidates.${index}.additionalInfo`,
                                                                    children: [
                                                                        t("additional_info"),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgComponents$2f$InfoIcon$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                            tooltip: "Add any extra details about the candidate like experience, availability, or preferences.",
                                                                            id: `additional-info-${index}`,
                                                                            place: "right"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 599,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 597,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                    control: control,
                                                                    name: `candidates.${index}.additionalInfo`,
                                                                    render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                                            ...field,
                                                                            id: `candidates.${index}.additionalInfo`,
                                                                            rows: 6,
                                                                            placeholder: t("enter_additional_info_about_candidate"),
                                                                            className: "form-control"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                            lineNumber: 609,
                                                                            columnNumber: 29
                                                                        }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 605,
                                                                    columnNumber: 25
                                                                }, this),
                                                                errors.candidates?.[index]?.additionalInfo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "auth-msg error",
                                                                    children: errors.candidates[index]?.additionalInfo?.message
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                                    lineNumber: 619,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                            lineNumber: 596,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                        lineNumber: 595,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                lineNumber: 422,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                            lineNumber: 421,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, field.id, true, {
                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                    lineNumber: 384,
                                    columnNumber: 15
                                }, this)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$styles$2f$commonPage$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].add_another_candidate_link,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    type: "button",
                                    onClick: ()=>{
                                        /**
                   * Handles adding another candidate to the form
                   *
                   * Checks if maximum limit (5) is reached before adding a new candidate entry.
                   * Shows a message if the limit is reached.
                   */ if (fields.length < 5) {
                                            append({
                                                name: "",
                                                email: "",
                                                gender: "",
                                                resume: null,
                                                assessment: null,
                                                additionalInfo: ""
                                            });
                                        } else {
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helper$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toastMessageSuccess"])("Maximum 5 candidates allowed");
                                        }
                                    },
                                    className: "clear-btn p-0 color-primary",
                                    disabled: fields.length >= 5,
                                    children: [
                                        t("add_candidates_resume"),
                                        " ",
                                        fields.length >= 5 ? "(Maximum 5 candidates allowed)" : ""
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                    lineNumber: 629,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                lineNumber: 628,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "button-align py-5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        type: "submit",
                                        className: "primary-btn rounded-md minWidth",
                                        disabled: isSubmitting,
                                        children: [
                                            t("analyze"),
                                            " ",
                                            isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$loader$2f$Loader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                                lineNumber: 653,
                                                columnNumber: 49
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                        lineNumber: 652,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        type: "button",
                                        onClick: ()=>{
                                            /**
                   * Resets the form to its default state
                   *
                   * Clears all form fields, errors, and success states, returning the form to its initial state
                   * with a single empty candidate entry.
                   */ reset();
                                            setCandidateErrors({});
                                            setCandidateSuccess({});
                                            setCandidatesToRemove([]);
                                        },
                                        className: "dark-outline-btn rounded-md minWidth",
                                        disabled: isSubmitting,
                                        children: t("reset")
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                        lineNumber: 655,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                                lineNumber: 651,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                        lineNumber: 375,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
                lineNumber: 352,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
            lineNumber: 351,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/views/resume/ManualUploadResume.tsx",
        lineNumber: 350,
        columnNumber: 5
    }, this);
}
_s(ManualUploadResume, "Tki/nFqsChXWVl2f9qdh3zNBTNg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFieldArray"]
    ];
});
_c = ManualUploadResume;
const __TURBOPACK__default__export__ = ManualUploadResume;
var _c;
__turbopack_context__.k.register(_c, "ManualUploadResume");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/manual-upload-resume/[jobId]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$ManualUploadResume$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/views/resume/ManualUploadResume.tsx [app-client] (ecmascript)");
"use client";
;
;
const page = ({ params, searchParams })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$views$2f$resume$2f$ManualUploadResume$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            params: params,
            searchParams: searchParams
        }, void 0, false, {
            fileName: "[project]/src/app/manual-upload-resume/[jobId]/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/manual-upload-resume/[jobId]/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = page;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_ab8ab5df._.js.map
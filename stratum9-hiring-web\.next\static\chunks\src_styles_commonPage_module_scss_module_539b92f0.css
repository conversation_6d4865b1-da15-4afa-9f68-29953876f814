/* [project]/src/styles/commonPage.module.scss.module.css [app-client] (css) */
.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul, .commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box .commonPage-module-scss-module__em0r7a__skills_tab, .commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head .commonPage-module-scss-module__em0r7a__header_tab {
  list-style: none;
  padding: 0;
  margin: 0;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats {
  background: #fff;
  border-radius: 25px;
  padding: 2.5rem;
  margin-bottom: 28px;
  box-shadow: 0 2px 9px #00000017;
  display: flex;
  overflow: hidden;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 20%;
  padding-left: 1.2rem;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat:after {
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  background-color: #e5e7eb;
  z-index: 1;
  margin: auto 0;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat:first-child {
  padding-left: 0;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat.commonPage-module-scss-module__em0r7a__border_none {
  padding-right: 0;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat.commonPage-module-scss-module__em0r7a__border_none:after {
  display: none;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat .commonPage-module-scss-module__em0r7a__dashboard__stat_label {
  color: #333;
  font-size: 1.4rem;
  font-weight: 700;
  padding-bottom: 1.2rem;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat .commonPage-module-scss-module__em0r7a__dashboard__stat_value {
  font-size: 2rem;
  color: #436eb6;
  font-weight: 700;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat_design {
  position: relative;
  min-width: 135px;
}

.commonPage-module-scss-module__em0r7a__dashboard__stats .commonPage-module-scss-module__em0r7a__dashboard__stat_design .commonPage-module-scss-module__em0r7a__dashboard__stat_image {
  width: 230px;
  min-width: 230px;
  height: 140px;
  object-fit: contain;
  position: absolute;
  z-index: 2;
  top: -30px;
  right: -30px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__job_info {
  border-radius: 8px;
  background: #436eb61a;
  padding: 5px 15px;
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__job_info.commonPage-module-scss-module__em0r7a__text_xs {
  font-size: 1.3rem;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__job_info a {
  color: #436eb6;
  font-size: 1.4rem;
  text-decoration: underline !important;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__section_heading {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__section_heading span {
  color: #436eb6;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__interview_form_icon {
  width: 100%;
  height: 260px;
  margin-top: -20px;
  padding: 0 20px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__inner_heading {
  font-size: 2.4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__inner_heading span {
  color: #cb9932;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul {
  display: flex;
  gap: 4rem;
  align-items: center;
  margin-bottom: 0;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul li {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul li span {
  width: 20px;
  height: 20px;
  border-radius: 100%;
  display: block;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul li span.commonPage-module-scss-module__em0r7a__selecting {
  background: #cb9932;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul li span.commonPage-module-scss-module__em0r7a__selected {
  background: #436eb6;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box ul li span.commonPage-module-scss-module__em0r7a__selection {
  background: #436eb61a;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box .commonPage-module-scss-module__em0r7a__skills_tab {
  display: flex;
  align-items: center;
  border: 1px solid #333;
  border-radius: 14px;
  display: inline-flex;
  overflow: hidden;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box .commonPage-module-scss-module__em0r7a__skills_tab li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 8px 30px;
  position: relative;
  margin: 0;
  text-align: center;
  min-width: 155px;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__skills_info_box .commonPage-module-scss-module__em0r7a__skills_tab li.commonPage-module-scss-module__em0r7a__active {
  position: relative;
  color: #fff;
  background: #436eb6;
}

.commonPage-module-scss-module__em0r7a__job_page .commonPage-module-scss-module__em0r7a__career-skill-card {
  min-height: 280px;
}

.commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
  margin-bottom: 20px;
}

.commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head .commonPage-module-scss-module__em0r7a__header_tab {
  display: flex;
  align-items: center;
  border: 1px solid #333;
  border-radius: 14px;
  display: inline-flex;
  overflow: hidden;
}

.commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head .commonPage-module-scss-module__em0r7a__header_tab li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 8px 30px;
  position: relative;
  margin: 0;
  text-align: center;
  min-width: 155px;
}

.commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head .commonPage-module-scss-module__em0r7a__header_tab li.commonPage-module-scss-module__em0r7a__active {
  position: relative;
  color: #fff;
  background: #436eb6;
}

.commonPage-module-scss-module__em0r7a__dashboard_page .commonPage-module-scss-module__em0r7a__dashboard_inner_head .commonPage-module-scss-module__em0r7a__search_box {
  width: 35%;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__upload_resume_page .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__operation_admins_img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__manual_upload_resume .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__input_type_file input {
  border: 1px solid #fff9;
  padding: 11px 15px;
  font-size: 1.4rem;
  border-radius: 12px;
  background: #3333330d;
  color: #333;
  width: 100%;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__manual_upload_resume .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__candidate_card {
  border-radius: 30px;
  border: 2px solid #0003;
  background: #ffffff0d;
  padding: 20px;
  margin-bottom: 20px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__manual_upload_resume .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__candidate_card .commonPage-module-scss-module__em0r7a__candidate_card_header {
  margin-bottom: 15px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__manual_upload_resume .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__candidate_card .commonPage-module-scss-module__em0r7a__candidate_card_header h3 {
  color: #333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin: 0;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__manual_upload_resume .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__add_another_candidate_link {
  text-align: left;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidate_qualification_page .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__approved_status_indicator {
  display: flex;
  gap: 30px;
  margin-top: 10px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidate_qualification_page .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__approved_status_indicator p {
  font-size: 1.4rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidate_qualification_page .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__approved_status_indicator p span {
  background: #e5be531a;
  border: 1px solid #e5be53;
  width: 24px;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidate_qualification_page .commonPage-module-scss-module__em0r7a__inner_page .commonPage-module-scss-module__em0r7a__approved_status_indicator p:nth-child(2) span {
  background: #d000001a;
  border: 1px solid #d00000;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidates_list_page .commonPage-module-scss-module__em0r7a__candidates_list_section {
  margin-bottom: 20px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidates_list_page .commonPage-module-scss-module__em0r7a__candidates_list_section .commonPage-module-scss-module__em0r7a__section_name {
  margin-bottom: 20px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidates_list_page .commonPage-module-scss-module__em0r7a__candidates_list_section .commonPage-module-scss-module__em0r7a__section_name h3 {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
}

.commonPage-module-scss-module__em0r7a__resume_page.commonPage-module-scss-module__em0r7a__candidates_list_page .commonPage-module-scss-module__em0r7a__candidates_list_section .commonPage-module-scss-module__em0r7a__section_name p {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}


/*# sourceMappingURL=src_styles_commonPage_module_scss_module_539b92f0.css.map*/

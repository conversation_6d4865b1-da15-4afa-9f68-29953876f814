/* [project]/src/styles/style.scss.css [app-client] (css) */
.custom-dropdown, .permissions-card .checbox-group, .library-folder, .table-responsive .table .multi-user-list, .common-search-group .search-view-box ul, .common-tab, .assignments-card .assignments-list, .interview-topic-list, .number-task {
  list-style: none;
  padding: 0;
  margin: 0;
}

.theme-btn {
  padding: 12px 30px;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all .4s;
  font-weight: 600;
  white-space: nowrap;
}

.theme-btn:active {
  opacity: .7;
}

@media screen and (width <= 767px) {
  .theme-btn {
    padding: 1rem 1.5rem;
  }
}

.theme-btn.rounded-sm {
  border-radius: 8px;
}

.theme-btn.rounded-md {
  border-radius: 12px;
}

.theme-btn.rounded-lg {
  border-radius: 20px;
}

.theme-btn.button-lg {
  padding: 16px;
}

.theme-btn.button-sm {
  padding: 8px 15px;
  font-size: 1.4rem;
}

.theme-btn.button-full {
  padding: 1rem 1.5rem;
  font-size: 1.6rem;
  width: 100%;
  justify-content: center;
}

.theme-btn.button-full svg {
  fill: #fff;
}

.theme-btn.primary-outline-btn {
  border: 1px solid #436eb6;
  background-color: #fff;
  color: #436eb6;
}

.theme-btn.primary-outline-btn:hover {
  background: #436eb6;
  color: #fff;
  border-color: #fff;
}

.theme-btn.dark-outline-btn {
  border: 1px solid #333;
  background-color: #0000;
  color: #333;
}

.theme-btn.dark-outline-btn:hover {
  background: none;
  color: #436eb6;
  border-color: #436eb6;
}

.theme-btn.danger-outline-btn {
  border: 1px solid #d00000;
  background-color: #0000;
  color: #d00000;
}

.theme-btn.danger-outline-btn svg {
  fill: #d00000;
  stroke: #d00000;
}

.theme-btn.danger-outline-btn:hover {
  background: none;
  color: #333;
  border-color: #333;
}

.theme-btn.danger-outline-btn:hover svg {
  fill: #333;
  stroke: #333;
}

.theme-btn.danger-btn {
  background: #d00000;
  border: 1px solid #d00000;
  color: #fff;
}

.theme-btn.danger-btn:hover {
  background: #333;
  border-color: #333;
}

.theme-btn.primary-btn {
  background: #436eb6;
  border: 1px solid #436eb6;
  color: #fff;
}

.theme-btn.primary-btn:hover {
  background: #333;
}

.theme-btn.primary-btn.truly-disabled {
  background: #436eb6;
  opacity: .6;
  cursor: not-allowed;
}

.theme-btn.primary-btn.truly-disabled:hover {
  background: #436eb6;
  border: 1px solid #436eb6;
  color: #fff;
}

.theme-btn.secondary-btn {
  background: #cb9932;
  border: 2px solid #cb9932;
  color: #fff;
}

.theme-btn.secondary-btn:hover {
  background: #cb9932;
  color: #fff;
}

.theme-btn.disable-btn {
  background: #3333;
  color: #33333380;
  border: 1px solid #0000;
  cursor: default;
}

.theme-btn.white-btn {
  color: #436eb6;
  background-color: #fff;
  padding-inline: 10px;
}

.theme-btn.white-btn:hover {
  background: #436eb6;
  color: #fff;
  border-color: #fff;
}

.theme-btn.white-btn:hover svg path {
  stroke: #fff;
}

.theme-btn.dark-btn {
  background: #333;
  border: 1px solid #333;
  color: #fff;
}

.theme-btn.dark-btn:hover {
  background: #cb9932;
  border-color: #cb9932;
  color: #fff;
}

.theme-btn.clear-btn {
  background-color: #0000;
  color: #fff;
  border: none;
}

.theme-btn.clear-btn.primary {
  color: #436eb6;
}

.theme-btn.clear-btn.primary svg {
  fill: #436eb6;
}

.theme-btn.padding-lg {
  padding: 10px 40px;
}

.theme-btn.minWidth {
  min-width: 110px;
}

.theme-btn.secondary-outline-btn {
  border: 1px solid #cb9932;
  background-color: #0000;
  color: #cb9932;
}

.theme-btn.secondary-outline-btn:hover {
  background-color: #cb9932;
  color: #fff;
  border-color: #cb9932;
}

.theme-btn.text-btn.secondary {
  color: #cb9932;
}

.theme-btn.text-btn.secondary:hover {
  color: #333;
}

.theme-btn.text-btn.secondary:hover svg {
  fill: #333;
}

.theme-btn.text-btn.primary {
  color: #436eb6;
}

.theme-btn.text-btn.primary svg {
  fill: #436eb6;
}

.theme-btn.text-btn.primary:hover {
  color: #333;
}

.theme-btn.text-btn.primary:hover svg {
  fill: #333;
}

.theme-btn .spinner-border {
  width: 14px;
  height: 14px;
  margin-left: 5px;
  border-width: 2px;
}

.text-loader {
  --bs-spinner-width: 14px !important;
  --bs-spinner-height: 14px !important;
}

.rounded360 {
  transform: rotate(360deg);
  transition: transform 1s ease-in-out;
}

.common-tab {
  display: flex;
  align-items: center;
  display: inline-flex;
}

.common-tab li {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  padding: 8px 30px;
  position: relative;
  margin: 0;
  text-align: center;
  min-width: 220px;
  border: 1px solid #333;
}

.common-tab li:first-child {
  border-radius: 10px 0 0 10px;
}

.common-tab li:last-child {
  border-radius: 0 10px 10px 0;
}

.common-tab li.active {
  position: relative;
  color: #fff;
  background: #436eb6;
  border-color: #436eb6;
}

.announcement-card {
  border-radius: 24px;
  background: #436eb61a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  cursor: pointer;
}

.announcement-card .announcement-content {
  padding: 20px;
}

.announcement-card .announcement-content h3 {
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.announcement-card .announcement-image {
  padding: 10px 20px 0;
}

.announcement-card .announcement-image .announce-img {
  width: 149px;
  height: 110px;
  object-fit: contain;
  object-position: bottom;
}

.hiring-card {
  background: #fff;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 25px 30px 0;
  border-radius: 32px;
  box-shadow: 0 4px 12px #00000029;
  overflow: hidden;
}

.hiring-card .hiring-content {
  width: 50%;
  padding-bottom: 25px;
  height: 150px;
  min-height: 150px;
}

.hiring-card .hiring-content h3 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.hiring-card .hiring-content p {
  font-size: 1.4rem;
  color: #333;
  font-style: normal;
  font-weight: 500;
}

.hiring-card .hiring-content p.space-bottom30 {
  margin-bottom: 30px;
}

.hiring-card .hiring-content a {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #436eb6;
  font-size: 1.4rem;
  font-weight: 600;
  margin-top: 10px;
  min-height: 20px;
  line-height: 20px;
}

.hiring-card .hiring-content a svg {
  width: 20px;
  height: 20px;
  fill: #436eb6;
}

.hiring-card .hiring-image {
  width: 50%;
}

.hiring-card .hiring-image .hiring-img {
  width: 100%;
  height: 150px;
  min-height: 150px;
  object-fit: contain;
  object-position: bottom;
}

.hiring-card.active, .hiring-card:hover {
  background: #436eb61a;
}

@media (width <= 767px) {
  .hiring-card {
    flex-direction: column;
    padding: 30px 20px 0;
    border-radius: 24px;
  }

  .hiring-card .hiring-content {
    width: 100%;
  }

  .hiring-card .hiring-image {
    width: 100%;
  }
}

.career-skill-card {
  border-radius: 24px;
  background: #436eb61a;
  padding: 20px;
}

.career-skill-card .head {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.career-skill-card .head h3 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.career-skill-card .head .right-img {
  width: 20px;
  min-width: 20px;
  height: 20px;
  fill: #333;
  cursor: pointer;
}

.career-skill-card .head .right-img path {
  fill: #333;
}

.career-skill-card .head .right-img:active {
  opacity: .7;
}

.career-skill-card .head .skill-content p {
  font-size: 1.6rem;
  color: #333;
  font-weight: 500;
}

.career-skill-card:hover, .career-skill-card.active {
  background: #cb9932;
  transition: all .4s;
}

.career-skill-card:hover .head h3, .career-skill-card.active .head h3 {
  color: #fff;
}

.career-skill-card:hover .head .right-img path, .career-skill-card.active .head .right-img path {
  fill: #fff;
}

.career-skill-card:hover .skill-content p, .career-skill-card.active .skill-content p {
  color: #fff;
}

.assignments-card .assignments-name {
  border-radius: 24px;
  background: #cb99321a;
  padding: 20px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  flex-direction: column;
  gap: 15px;
}

.assignments-card .assignments-name h4 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 500;
}

.assignments-card .assignments-list {
  margin-top: 20px;
}

.assignments-card .assignments-list li {
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  background: #436eb61a;
  cursor: pointer;
  transition: all .4s;
  margin-bottom: 20px;
  font-size: 1.6rem;
  font-weight: 500;
}

.assignments-card .assignments-list li:active {
  opacity: .7;
}

.assignments-card .assignments-list li.selecting {
  background: #cb9932;
  color: #333;
}

.assignments-card .assignments-list li.selected {
  background: #436eb6;
  color: #fff;
}

.candidate-card {
  border-radius: 24px;
  background: #fff;
  box-shadow: 0 4px 12px #00000017;
  padding: 25px;
}

.candidate-card h2 {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
}

.candidate-card .title {
  font-size: 16px;
  color: #3339;
  font-weight: 500;
  margin-bottom: 5px;
}

.candidate-card .title:last-child {
  margin-bottom: 0;
}

.candidate-card .actions {
  margin-top: 15px;
}

.candidate-card .actions a {
  display: flex;
  align-items: center;
  text-decoration: none;
  font-size: 14px;
  color: #436eb6;
  margin-bottom: 10px;
  gap: 5px;
}

.candidate-card .actions a:last-child {
  margin-bottom: 0;
}

.common-card {
  border-radius: 40px;
  background: #fff;
  box-shadow: 0 4px 12px #33333329;
  padding: 25px;
}

.common-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #333;
  padding-bottom: 20px;
}

.common-card .card-header h3 {
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.qualification-card {
  border-radius: 32px;
  background-color: #cb99321a;
  padding: 30px;
  margin-bottom: 20px;
}

.qualification-card.skeleton-card {
  background-color: #cb99320d;
  position: relative;
  overflow: hidden;
}

.qualification-card.skeleton-card:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(90deg, #fff0 0, #fff3 20%, #ffffff80 60%, #fff0);
  animation: 2s infinite shimmer;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.qualification-card.skeleton-card .skeleton-text {
  height: 14px;
  background-color: #3333331a;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 100%;
}

.qualification-card.skeleton-card .skeleton-text.skeleton-title {
  height: 24px;
  width: 70%;
  margin-bottom: 6px;
}

.qualification-card.skeleton-card .skeleton-text.skeleton-subtitle {
  height: 16px;
  width: 50%;
}

.qualification-card.skeleton-card .skeleton-text.skeleton-label {
  height: 18px;
  width: 40%;
  margin-bottom: 12px;
}

.qualification-card.skeleton-card .skeleton-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3333331a;
}

.qualification-card.skeleton-card .skeleton-circle.skeleton-icon {
  width: 50px;
  height: 50px;
  margin: 0 auto 15px;
}

.qualification-card.skeleton-card .skeleton-button {
  height: 40px;
  border-radius: 8px;
  background-color: #3333331a;
  width: 48%;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill {
  height: 40px;
  border-radius: 8px;
  background-color: #33333314;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill .skeleton-text {
  width: 70%;
  height: 16px;
  margin-bottom: 0;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill.skeleton-selected:nth-child(odd) {
  background-color: #436eb633;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill.skeleton-selected:nth-child(2n) {
  background-color: #cb993233;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill.skeleton-selected:nth-child(3n) {
  background-color: #3b82f633;
}

.qualification-card.skeleton-card .assignments-list .skeleton-skill.skeleton-selected:nth-child(4n) {
  background-color: #6366f133;
}

.qualification-card.rejected-card {
  background-color: #d000001a;
}

.qualification-card .qualification-card-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.qualification-card .qualification-card-top .name h3 {
  color: #333;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
}

.qualification-card .qualification-card-top .name p {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  margin: 0;
}

.qualification-card .qualification-card-top .top-right .hold-icon {
  width: 30px;
  height: 30px;
  min-width: 30px;
  fill: #cb9932;
}

.qualification-card .qualification-card-top .top-right .approved-status {
  cursor: pointer;
}

.qualification-card .qualification-card-top .top-right .approved-status p {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.qualification-card .qualification-card-top .top-right .approved-status p svg {
  width: 20px;
  height: 20px;
  min-width: 20px;
}

.qualification-card .qualification-card-top .top-right .approved-status p svg.down-arrow-icon {
  width: 15px;
  height: 15px;
  min-width: 15px;
}

.qualification-card .qualification-card-mid p {
  font-size: 14px;
  color: #333;
  font-weight: 400;
  margin-bottom: 8px;
}

.qualification-card .button-align {
  padding-top: 10px;
}

.qualification-card .button-align .theme-btn {
  padding: 12px 20px;
}

.jobs-card {
  border-radius: 24px;
  background-color: #fff;
  padding: 20px;
  box-shadow: 0 4px 12px #00000017;
  min-height: 115px;
}

.jobs-card .name {
  display: flex;
  justify-content: space-between;
}

.jobs-card .name h4 {
  color: #333;
  font-size: 20px;
  font-weight: 700;
}

.jobs-card .name svg {
  width: 18px;
  height: 18px;
}

.jobs-card .description {
  font-size: 1.4rem;
  color: #333c;
  font-weight: 500;
  margin-top: 8px;
}

.jobs-card.active, .jobs-card:hover {
  background-color: #436eb6;
  transition: all .4s;
}

.jobs-card.active h4, .jobs-card.active .description, .jobs-card:hover h4, .jobs-card:hover .description {
  color: #fff;
}

.jobs-card.active svg path, .jobs-card:hover svg path {
  fill: #fff;
}

.jobs-card:active {
  opacity: .7;
  transition: all .4s;
}

.role-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.role-list .role-item {
  background: #436eb61a;
  border-radius: 12px;
  padding: 12px 30px;
  font-weight: 600;
  color: #333;
  font-size: 1.6rem;
  cursor: pointer;
  transition: background-color .3s, color .3s;
  user-select: none;
}

.role-list .role-item:hover, .role-list .role-item:focus, .role-list .role-item.active {
  background: #436eb6;
  outline: none;
  color: #fff;
}

.role-list.secondary .role-item {
  background: #fff;
  color: #333;
  border-radius: 16px;
  border: 1px solid #33333326;
  box-shadow: 0 7px 7px #00000008;
  padding: 22px;
}

.role-list.secondary .role-item:hover, .role-list.secondary .role-item:focus, .role-list.secondary .role-item.active {
  background: #cb9932;
  outline: none;
  color: #fff;
}

@media (width <= 767px) {
  .role-list {
    justify-content: center;
  }

  .role-item {
    font-size: 1.2rem;
    padding: 8px 14px;
  }
}

@media (width >= 768px) and (width <= 1023px) {
  .role-list {
    justify-content: flex-start;
  }

  .role-list__item {
    font-size: .95rem;
  }
}

@media (width >= 1024px) {
  .role-list {
    justify-content: flex-start;
  }

  .role-item {
    font-size: 1.4rem;
  }
}

.overview-skill-card {
  position: relative;
  background-color: #cb99321a;
  border-radius: 24px;
  padding: 30px;
  z-index: 0;
  overflow: hidden;
}

.overview-skill-card .overview-txt {
  font-size: 1.6rem;
  color: #333;
  font-weight: 600;
  margin-bottom: 15px;
}

.overview-skill-card .clear-btn {
  gap: 6px;
  color: #cb9932;
}

.overview-skill-card .clear-btn svg {
  width: 15px;
  height: 15px;
  fill: #cb9932;
}

.overview-skill-card:after {
  content: attr(data-text);
  position: absolute;
  z-index: -1;
  top: 7px;
  right: 11px;
  font-size: 15px;
  font-weight: 700;
  color: #fff;
}

.overview-skill-card:before {
  content: "";
  position: absolute;
  z-index: -1;
  top: -20px;
  right: -15px;
  background: #cb9932;
  height: 60px;
  width: 60px;
  border-radius: 100%;
  transform: scale(1);
  transform-origin: 50%;
  transition: transform .3s ease-out;
}

.overview-skill-card:hover:before {
  transform: scale(100);
  transition: all .3s ease-out;
}

.overview-skill-card:hover {
  transition: all .2s ease-out !important;
}

.overview-skill-card:hover .clear-btn {
  color: #fff;
}

.overview-skill-card:hover .clear-btn svg {
  fill: #fff;
}

.overview-skill-card:hover .overview-txt {
  color: #fff;
}

.interview-info {
  border-radius: 24px;
  background-color: #cb99321a;
  padding: 30px;
  margin-bottom: 20px;
}

.interview-info .info-item {
  margin-bottom: 15px;
}

.interview-info .info-item .info-title {
  font-size: 1.6rem;
  color: #333;
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.interview-info .info-item .info-title .dot {
  width: 8px;
  height: 8px;
  background-color: #333;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10px;
}

.interview-info .info-item p {
  font-size: 1.6rem;
  color: #333;
  font-weight: 400;
}

.interview-question-card {
  border-radius: 24px;
  background-color: #cb99320d;
  padding: 20px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
}

.interview-question-card.with-border {
  border: 2px solid #cb993299;
  background: #cb99320d;
}

.interview-question-card .tittle {
  font-size: 1.8rem;
  color: #cb9932;
  font-weight: 700;
  margin-bottom: 10px;
}

.interview-question-card .tittle svg {
  margin-left: 6px;
  stroke: #cb9932 !important;
}

.interview-question-card .tittle svg.rotate {
  transform: rotate(180deg);
}

.interview-question-card h5 {
  font-size: 1.8rem;
  color: #333;
  font-weight: 600;
}

.interview-question-card .question-body {
  margin-top: 15px;
}

.interview-question-card .question-body .form-group {
  margin-bottom: 0;
}

.interview-question-card .question-body .form-group textarea {
  background-color: #fff;
  border-radius: 16px;
  border: 1px solid #3336;
  padding: 10px;
}

.interview-question-card .question-body .follow-up-container {
  display: flex;
  align-items: center;
  margin-top: 15px;
  gap: 30px;
}

.interview-question-card .question-body .follow-up-container .follow-up-btn {
  display: flex;
  gap: 5px;
  align-items: center;
}

.interview-question-card .question-body .follow-up-container .follow-up-text {
  font-size: 1.6rem;
  color: #333;
  font-weight: 500;
}

.interview-question-card .question-body .follow-up-container .follow-up-text span {
  font-weight: 700;
}

.interview-question-card .question-body .answer-strap {
  padding: 14px 12px;
  border-radius: 8px;
  border: 1px solid #0000;
  width: 100%;
  margin: 5px 0;
}

.interview-question-card .question-body .answer-strap .radio-wrapper {
  gap: 10px;
  margin-bottom: 0;
  line-height: 16px;
  align-items: center;
}

.interview-question-card .question-body .answer-strap .radio-wrapper .radio-input {
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  --bs-form-check-bg-image: url("../media/check-bg-image-white.2d0cc2ad.svg");
}

.interview-question-card .question-body .answer-strap .radio-wrapper .radio-label {
  font-size: 1.8rem;
  color: #333;
  font-weight: 500;
}

.interview-question-card .question-body .answer-strap.right-answer {
  color: #333;
  border: 1px solid #0739;
  background-color: #00773314;
}

.interview-question-card .question-body .answer-strap.right-answer .radio-wrapper .radio-label {
  color: #333;
}

.interview-question-card .question-body .answer-strap.right-answer .radio-wrapper .radio-input {
  border-color: #00773380;
  --bs-form-check-bg-image: url("../media/check-bg-image-green.0472dbca.svg");
}

.interview-question-card .question-body .answer-strap.candidate-answer {
  color: #333;
}

.interview-question-card .question-body .answer-strap.candidate-answer .radio-wrapper .radio-label {
  color: #333;
}

.interview-question-card .question-body .answer-strap.candidate-answer .radio-wrapper .radio-input {
  border-color: #cb9932;
  --bs-form-check-bg-image: url("../media/check-bg-image-primary.d14ebe94.svg");
}

.interview-question-card .question-body .note-text {
  background: #3333;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 1.5rem;
  color: #000;
  font-weight: 500;
  margin-top: 15px;
}

.summary-text-card {
  padding: 20px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 3px solid var(--Stratum-GPT-Gradient, #74a8ff);
  background: linear-gradient(54deg, #74a8ff1a 20.92%, #aacaff1a 52.91%, #5d86cc1a 88.37%);
}

.summary-text-card .tittle {
  font-size: 2rem;
  color: #436eb6;
  font-weight: 700;
  margin-bottom: 20px;
}

.summary-text-card .sub-tittle {
  font-size: 2.2rem;
  color: #333;
  font-weight: 700;
  margin: 20px 0;
}

.summary-text-card p {
  font-size: 1.6rem;
  color: #333;
  font-weight: 400;
}

.summary-text-card ul {
  margin: 15px 0;
  padding-left: 25px;
}

.summary-text-card ul li {
  margin-bottom: 5px;
}

.summary-text-card ul li:last-child {
  margin-bottom: 0;
}

.summary-text-card ul li::marker {
  color: #333;
  font-weight: 700;
  font-size: 14px;
}

.summary-text-card ul.check-list {
  list-style: none;
  padding-left: 0;
}

.summary-text-card ul.check-list li {
  margin-bottom: 5px;
  font-size: 1.6rem;
  color: #333;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.summary-text-card ul.check-list li:last-child {
  margin-bottom: 0;
}

.summary-text-card .big-text {
  font-size: 10rem;
  font-weight: 700;
  color: #436eb6;
  line-height: 12rem;
  background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: #0000;
}

.summary-text-card .candidate-profile {
  border: none;
  background: none;
  margin: 0;
}

.summary-text-card .candidate-profile .info-container {
  justify-content: flex-start !important;
}

.summary-text-card .candidate-profile .info-container .info-item {
  width: 22%;
}

@media (width <= 991px) {
  .summary-text-card .candidate-profile .info-container .info-item {
    width: 100% !important;
  }
}

@media (width <= 767px) {
  .summary-text-card .big-text {
    font-size: 5rem;
    line-height: normal;
  }

  .summary-text-card .sub-tittle {
    font-size: 1.6rem;
    margin: 10px 0;
  }

  .summary-text-card p {
    font-size: 1.4rem;
  }

  .summary-text-card ul {
    margin: 5px 0;
  }
}

.progress-container .time {
  font-weight: 700;
  white-space: nowrap;
  font-size: 2rem;
}

.progress-container .progress-tracker {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  margin-bottom: 60px;
}

.progress-container .progress-tracker .bar-container {
  flex: 1;
  position: relative;
}

.progress-container .progress-tracker .bar-container .bar {
  position: relative;
  height: 6px;
  background: #e0e0e0;
  border-radius: 2px;
}

.progress-container .progress-tracker .bar-container .bar .progress {
  position: absolute;
  height: 100%;
  background: #cb9932;
  border-radius: 2px;
  transition: width .3s;
  overflow: visible;
}

.progress-container .progress-tracker .bar-container .bar .progress:after {
  content: "";
  position: absolute;
  top: -2px;
  right: -7px;
  width: 10px;
  height: 10px;
  background: #cb9932;
  box-shadow: 0 0 0 3px #cb99324d;
  border-radius: 50%;
  z-index: 999;
}

.progress-container .progress-tracker .bar-container .bar .marker {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 12px;
  background: #e0e0e0;
  border: 1.5px solid #cb9932;
}

.progress-container .progress-tracker .bar-container .bar .marker.active {
  background: #cb9932;
}

.progress-container .progress-tracker .bar-container .labels {
  position: relative;
  display: flex;
  justify-content: space-between;
  font-size: 1.4rem;
  color: #333;
}

.progress-container .progress-tracker .bar-container .labels .label {
  position: absolute;
  white-space: nowrap;
  top: 100%;
  font-size: 1.4rem;
  margin-top: 20px;
  font-weight: 600;
}

.progress-container .status {
  background-color: #ffeaea;
  color: #d00000;
  font-size: 1.2rem;
  padding: 5px 10px;
  border-radius: 20px;
  font-weight: 500;
  white-space: nowrap;
}

.behavioral-letter-card {
  padding: 20px;
  border-radius: 0;
  background-color: #436eb61a;
  min-height: 100%;
  position: relative;
  padding-bottom: 76px;
}

.behavioral-letter-card:after {
  content: "";
  position: absolute;
  background: url("../media/letter-pin.9e3ab7b2.png");
  background-size: contain;
  background-repeat: no-repeat;
  top: -10px;
  right: 5px;
  z-index: 2;
  width: 50px;
  height: 100%;
}

.behavioral-letter-card h5 {
  font-size: 2rem;
  color: #333;
  font-weight: 700;
  margin-bottom: 10px;
}

.behavioral-letter-card h5 span {
  color: #436eb6;
}

.behavioral-letter-card p {
  font-size: 1.6rem;
  color: #33333380;
  font-weight: 500;
}

.behavioral-letter-card p.font-dark {
  color: #333;
}

.behavioral-letter-card .fold-svg {
  position: absolute;
  bottom: 0;
  left: 0;
  background: #fff;
  height: 66px;
}

.interview-topic-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 30px;
}

.interview-topic-list .topic-item {
  background: #fff;
  border-radius: 16px;
  padding: 18px 30px;
  font-weight: 600;
  color: #333;
  font-size: 1.6rem;
  cursor: pointer;
  border: 1px solid #33333326;
  box-shadow: 0 7px 7px #00000008;
  position: relative;
}

.interview-topic-list .topic-item.current {
  background: #cb9932;
  color: #fff;
}

.interview-topic-list .topic-item.current .interviewer-name {
  background: #cb9932;
  color: #fff;
}

.interview-topic-list .topic-item.completed {
  background: #436eb6;
  color: #fff;
}

.interview-topic-list .topic-item.completed .interviewer-name {
  background: #436eb6;
  color: #fff;
}

.interview-topic-list .topic-item.additional {
  background: #073;
  color: #fff;
}

.interview-topic-list .topic-item.additional .interviewer-name {
  background: #073;
  color: #fff;
}

.interview-topic-list .topic-item .interviewer-name {
  background: #cb9932;
  color: #fff;
  height: 35px;
  width: 35px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  text-transform: uppercase;
  border: 2px solid #fff;
  position: absolute;
  top: -10px;
  right: -10px;
}

.number-task {
  display: flex;
  gap: 15px;
  margin: 20px 0;
}

.number-task li {
  font-size: 1.6rem;
  color: #333;
  font-weight: 700;
  padding: 7px 15px;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  border: 1px solid #333;
  transition: all .3s;
}

.number-task li:hover, .number-task li.active {
  background-color: #cb9932;
  color: #333;
  border-color: #cb9932;
}

.number-task li.extreme:hover, .number-task li.extreme.active {
  background-color: #d00000;
  color: #fff;
  border-color: #d00000;
}

.user-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #cb9932;
  padding: 20px;
  border-radius: 14px;
  margin-bottom: 20px;
}

.user-summary .info {
  display: flex;
  flex-direction: column;
  color: #fff;
}

.user-summary .info .user-name {
  font-size: 1.6rem;
  font-weight: 700;
}

.user-summary .info .user-role {
  font-size: 1.4rem;
  font-weight: 400;
  margin-top: 8px;
}

.user-summary .actions {
  display: flex;
  gap: 20px;
  align-items: center;
  position: relative;
}

.user-summary .actions:after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #fff3;
  position: absolute;
  right: 10px;
  left: 0;
  top: 0;
  margin: auto;
}

.user-summary .actions button {
  gap: 5px;
  text-decoration: underline;
}

.user-summary .actions button svg {
  fill: #fff;
}

.interview-summary {
  background-color: #cb99321a;
  border-radius: 14px;
  padding: 20px;
  overflow: hidden;
  margin-bottom: 20px;
}

.interview-summary .summary-header {
  background-color: #cb9932;
  padding: 12px 15px;
  margin: -20px -20px 20px;
}

.interview-summary .summary-header .summary-heading {
  font-size: 1.6rem;
  font-weight: 400;
  color: #fff;
  margin: 0;
}

.interview-summary .interviewer {
  margin-bottom: 25px;
}

.interview-summary .interviewer .interviewer-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.interview-summary .interviewer .interviewer-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.interview-summary .interviewer .interviewer-name {
  font-size: 1.6rem;
  font-weight: 500;
  color: #333;
}

.interview-summary .interviewer .large .interviewer-avatar {
  width: 45px;
  height: 45px;
}

.interview-summary .interviewer .large .interviewer-name {
  font-size: 2rem;
  font-weight: 700;
}

.interview-summary .summary-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.interview-summary .summary-scores .score-btns {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.interview-summary .summary-highlights {
  margin-top: 20px;
}

.interview-summary .summary-highlights .highlight-list {
  margin-bottom: 0;
}

.interview-summary .summary-highlights .highlight-list .highlight-item {
  position: relative;
  font-size: 1.6rem;
  color: #333;
  margin-bottom: 10px;
}

.interview-summary .summary-highlights .highlight-list .highlight-item:last-child {
  margin-bottom: 0;
}

.profile-section .candidate-profile {
  display: flex;
  align-items: center;
  gap: 25px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  border-bottom: 1px dashed #3333;
}

.profile-section .candidate-profile.user-profile {
  padding: 20px;
  position: relative;
  z-index: 1;
  gap: 20px;
  margin-top: 25px;
  padding-bottom: 35px;
}

.profile-section .candidate-profile.user-profile:after {
  content: "";
  position: absolute;
  top: -25px;
  left: 0;
  width: 100%;
  height: 100px;
  border-radius: 12px;
  background: linear-gradient(0deg, #6b90ca 6.6%, #436eb6 91.1%);
  z-index: -1;
}

.profile-section .candidate-profile.user-profile .candidate-name {
  color: #fff;
  font-size: 2rem;
  text-transform: capitalize;
}

.profile-section .candidate-profile.user-profile .candidate-role {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-top: 20px;
}

.profile-section .candidate-name {
  font-size: 2.4rem;
  font-weight: 700;
  color: #333;
}

.profile-section .candidate-info {
  width: 100%;
}

.profile-section .candidate-info .info-container {
  display: flex;
  gap: 15px;
  margin-top: 15px;
  align-items: flex-start;
  justify-content: space-between;
}

.profile-section .candidate-info .info-container .info-item .info-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.profile-section .candidate-info .info-container .info-item .info-value {
  font-size: 1.4rem;
  color: #333;
  font-weight: 600;
}

.profile-section .candidate-info .info-container .info-item .info-value.with-img {
  display: flex;
  align-items: center;
  gap: 5px;
}

.profile-section .candidate-info .info-container .info-item .info-value.with-img img {
  width: 18px;
  min-width: 18px;
  height: 18px;
  min-height: 18px;
  border-radius: 100%;
  object-fit: cover;
}

.profile-section .candidate-info.user-info-md .info-container .info-item .info-title {
  font-size: 1.6rem;
}

.profile-section .candidate-info.user-info-md .info-container .info-item .info-value {
  font-size: 1.6rem;
}

.profile-section .candidate-image {
  width: 100px;
  min-width: 100px;
  height: 100px;
  min-height: 100px;
  overflow: hidden;
  object-fit: cover;
  border-radius: 12px;
}

@media (width <= 991px) {
  .profile-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .profile-section .candidate-info .candidate-name {
    font-size: 2rem;
  }

  .profile-section .candidate-info .info-container {
    flex-wrap: wrap;
    gap: 10px;
  }

  .profile-section .candidate-info .info-container .info-item {
    width: calc(50% - 5px);
  }

  .profile-section .candidate-info .info-container .info-item p {
    white-space: nowrap;
  }

  .profile-section .candidate-info .info-container .theme-btn {
    padding: 1rem 1.5rem;
    margin-top: 10px;
  }

  .profile-section .candidate-image {
    width: 80px;
    min-width: 80px;
    height: 80px;
    min-height: 80px;
  }
}

.plan-info-card {
  background-color: #ebf2ff;
  border-radius: 16px;
  padding: 20px;
}

.plan-info-card .plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.plan-info-card .plan-header .plan-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.plan-info-card .plan-header .plan-status {
  font-size: 1.6rem;
  font-weight: 500;
  color: #073;
}

.plan-info-card .plan-header .plan-status.inactive {
  color: #d00000;
}

.plan-info-card .plan-details {
  margin-bottom: 24px;
}

.plan-info-card .plan-details .plan-price {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.plan-info-card .plan-details .plan-access {
  font-size: 1.4rem;
  color: #333;
}

.plan-info-card .plan-actions {
  display: flex;
  gap: 12px;
}

.notifications {
  max-width: 440px;
  position: absolute;
  top: 100px;
  right: 15%;
  z-index: 1000;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px #00000029;
}

.notifications .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notifications .header-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.notifications .header-content .clear-btn {
  font-size: 1.6rem;
  font-weight: 500;
  color: #436eb6;
}

.notifications .read-btns {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 15px;
}

.notifications .read-btns button {
  font-size: 1.6rem;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 6px;
  min-width: 60px;
  border: none;
}

.notifications .read-btns button.grey-btn {
  color: #33333380;
  background: #3333331a;
}

.notifications .notification-wrapper {
  height: 400px;
  overflow-y: auto;
}

.notifications .notification-wrapper .notification-item {
  padding: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #3333;
  position: relative;
  cursor: pointer;
  padding-right: 20px;
}

.notifications .notification-wrapper .notification-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.notifications .notification-wrapper .notification-item h4 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  padding-bottom: 10px;
}

.notifications .notification-wrapper .notification-item h4 span {
  color: #436eb6;
}

.notifications .notification-wrapper .notification-item p {
  font-size: 1.4rem;
  color: #333;
  padding-bottom: 10px;
}

.notifications .notification-wrapper .notification-item p.time {
  margin-bottom: 0;
}

.notifications .notification-wrapper .notification-item.unread:after {
  content: "";
  position: absolute;
  bottom: 0;
  top: 0;
  right: 15px;
  margin: auto 0;
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background-color: #d00000;
}

.skills-score-card {
  padding: 20px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid #3333;
  background: #fff;
}

.skills-score-card .skills-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.skills-score-card .skills-list .skills-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #3333;
  margin-bottom: 0;
}

.skills-score-card .skills-list .skills-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.skills-score-card .skills-list .skills-item:first-child {
  padding-top: 0;
}

.skills-score-card .skills-list .skills-item .skill-name {
  font-size: 1.6rem;
  color: #333;
  font-weight: 600;
}

.skills-score-card .skills-list .skills-item .skill-rating {
  font-size: 1.6rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: .5rem;
  font-weight: 500;
}

.skills-score-card .skills-list .skills-item .skill-badge {
  font-size: 1.6rem;
  color: #d00000;
  font-weight: 500;
}

.skills-summary-card {
  padding: 20px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid #3333;
  background: #fff;
  min-height: 390px;
}

.skills-summary-card .skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 15px;
}

.skills-summary-card .skills-tags .skill-tag {
  font-size: 1.4rem;
  padding: 10px 15px;
  border-radius: 12px;
  background: #3333331a;
  color: #3339;
  font-weight: 500;
}

.skills-summary-card .skills-tags .skill-tag.active {
  background: #436eb6;
  color: #fff;
}

.skills-summary-card .skill-sub-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.skills-summary-card .strengths .strength-item {
  font-size: 1.6rem;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.2;
  position: relative;
}

.skills-summary-card .probability-card {
  border-left: 1px solid #3333;
  padding-left: 20px;
}

.skills-summary-card .probability-card .progress-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}

.skills-summary-card .probability-card .progress-container h3 {
  font-size: 3rem;
  font-weight: 700;
  color: #cb9932;
  margin: 0;
}

.skills-summary-card .probability-card .progress-container .probability-bar {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 5px;
}

.skills-summary-card .probability-card .progress-container .probability-bar .bar {
  width: 10%;
  height: 35px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
  display: block;
}

.skills-summary-card .probability-card .progress-container .probability-bar .bar.filled {
  background-color: #cb9932;
}

.skills-summary-card .insight-card {
  background-color: #cb99321a;
  border-radius: 24px;
  padding: 30px;
  margin-top: 34px;
}

.skills-summary-card .insight-card .insight-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  margin: 0;
}

.skills-summary-card .insight-card .insight-text {
  font-size: 1.4rem;
  color: #333c;
  font-weight: 500;
  margin-top: 15px;
}

.improvement-areas-card {
  padding: 20px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid #3333;
  background: #fff;
}

.improvement-areas-card .improvement-card {
  padding: 25px;
  border-radius: 24px;
  background: linear-gradient(54deg, #74a8ff4d 20.92%, #aacaff4d 52.91%, #5d86cc4d 88.37%);
}

.improvement-areas-card .improvement-card .title {
  font-size: 1.9rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 15px;
}

.improvement-areas-card .improvement-card .description {
  font-size: 1.6rem;
  color: #333c;
  line-height: 1.4;
}

.job-generate-head {
  border-bottom: 2px solid #3333;
  padding-bottom: 15px;
  position: relative;
}

.job-generate-head:after {
  content: "Or Enter Details Manually";
  position: absolute;
  bottom: -10px;
  left: 25px;
  width: max-content;
  background: #fff;
  font-size: 14px;
  display: inline-block;
  padding: 0 10px;
  font-weight: 500;
  color: #333c;
  background: #fff;
}

.job-generate-head .job-generate-doc-card {
  padding: 15px;
  cursor: pointer;
  transition: all .3s;
  margin-bottom: 20px;
  border-radius: 20px;
  border: 2px solid var(--Stratum-GPT-Gradient, #74a8ff);
  background: linear-gradient(53.6deg, #74a8ff40 20.92%, #aacaff40 52.91%, #5d86cc40 88.37%);
}

.job-generate-head .job-generate-doc-card .upload-card {
  background: #ffffffb3;
  min-height: 115px;
}

.job-generate-head .job-generate-doc-card .tittle {
  font-size: 1.6rem;
  color: #436eb6;
  font-weight: 700;
  margin-bottom: 20px;
}

.job-generate-head .job-generate-doc-card .sub-tittle {
  font-size: 1.8rem;
  color: #333;
  font-weight: 700;
  margin: 20px 0;
}

.job-generate-head .job-generate-doc-card p {
  font-size: 1.4rem;
  color: #333;
  font-weight: 400;
}

.job-generate-head .job-generate-doc-card .upload-box-inner svg {
  margin-bottom: 10px;
  width: 32px !important;
  min-width: 32px !important;
  height: 32px !important;
}

.job-generate-head .job-generate-doc-card .upload-box-inner p {
  font-size: 1.2rem !important;
  line-height: 16px !important;
}

.job-generate-head .job-generate-doc-card .uploading-message {
  font-size: 1.2rem !important;
}

.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-group label {
  font-size: 1.4rem;
  margin-bottom: 5px;
  color: #333;
  font-weight: 500;
}

.form-group label sup {
  color: #d00000;
  font-size: 1.4rem;
  top: -2px;
}

.form-group .form-control {
  border: 1px solid #fff9;
  padding: 11px 15px;
  font-size: 1.4rem;
  border-radius: 12px;
  background: #3333330d;
  color: #333;
  resize: none;
  min-height: 46px;
}

.form-group .form-control::placeholder {
  color: #3336;
  font-weight: 500;
}

.form-group .form-control:focus {
  outline: none;
  box-shadow: none;
  color: #333;
}

.form-group.border-danger input, .form-group.border-danger textarea, .form-group.border-danger select {
  border-color: #d00000;
}

.form-group .auth-msg {
  font-size: 1.2rem;
  margin: 5px 0 0;
  font-weight: 400;
}

.form-group .auth-msg.error {
  color: #d00000;
}

.form-group .icon-align {
  position: relative;
}

.form-group .icon-align input {
  padding-right: 46px;
}

.form-group .icon-align.left input {
  padding-left: 46px;
}

.form-group .icon-align.left img, .form-group .icon-align.left button, .form-group .icon-align.left svg {
  left: 25px;
}

.form-group .icon-align.right img {
  right: 5px;
}

.form-group .icon-align .show-icon {
  background-color: #0000;
  border: none;
  padding: 0;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(-50%, -50%);
}

.form-group .icon-align .show-icon img, .form-group .icon-align .show-icon svg {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.form-group .css-13cymwt-control, .form-group .css-t3ipsp-control {
  background: none;
  border: none !important;
  outline: none !important;
  border-color: #0000 !important;
  padding-left: 5px !important;
  padding-right: 5px !important;
}

.form-group .select__control {
  border: none;
  background: none;
  box-shadow: none !important;
}

.form-group .select__control:focus {
  box-shadow: none !important;
}

.css-3iigni-container {
  pointer-events: none;
  position: relative;
  box-sizing: border-box;
  min-height: 45px;
  padding: 3px 5px !important;
}

.disabled, .select--is-disabled, input:disabled {
  background-color: #e0e0e0 !important;
  color: #555 !important;
}

select:disabled {
  background-color: #e0e0e0 !important;
  color: #555 !important;
}

.common-search-group .search-view-box {
  position: absolute;
  top: 42px;
  left: 0;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 4px 12px #00000014;
  width: 100%;
  border-radius: 0 0 12px 12px;
  display: none;
}

.common-search-group .search-view-box ul li {
  padding: 10px;
  cursor: pointer;
  font-size: 1.4rem;
}

.common-search-group .search-view-box ul li:hover {
  background-color: #cb9932;
  color: #fff;
}

.common-search-group:focus .form-control + .search-view-box, .common-search-group:focus-visible .form-control + .search-view-box, .common-search-group:focus-within .form-control + .search-view-box {
  display: block !important;
}

select {
  border: 1px solid #fff9;
  padding: 11px 15px;
  font-size: 1.4rem;
  border-radius: 12px;
  color: #333;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: #3333330d;
  background-image: url("../media/down-arrow.88f69ff5.svg");
  background-repeat: no-repeat;
  background-position-x: calc(100% - 12px);
  background-position-y: center;
  background-size: 14px 14px;
  padding-right: 32px;
  border-radius: 12px;
  color: #3336;
}

select:focus {
  outline: none;
  box-shadow: none;
  color: #333;
}

select option {
  color: #333;
  font-style: normal;
}

select option:first-child {
  color: #3336;
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out;
  -webkit-text-fill-color: #333 !important;
}

input:-moz-autofill, input:-moz-autofill:hover, input:-moz-autofill:focus, input:-moz-autofill:active {
  transition: background-color 5000s ease-in-out;
  color: #333 !important;
}

input:-webkit-autofill {
  transition: background-color 5000s ease-in-out;
  -webkit-text-fill-color: #333 !important;
}

.upload-box {
  border: 1px solid #fff9;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
}

.upload-box .upload-para {
  font-weight: 500;
  margin: 10px 0;
}

.upload-box .browse {
  text-align: center;
  color: #fff9;
  margin: 0;
}

.upload-box .browse span {
  color: #cb9932;
  font-weight: 600;
  text-decoration: underline;
}

.upload-box input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

.checkbox-wrapper {
  margin-bottom: 15px;
}

.checkbox-wrapper .checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
}

.checkbox-wrapper .checkbox-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
}

.checkbox-wrapper .checkbox-text {
  line-height: 22px;
  margin-top: -5px;
}

.radio-wrapper {
  display: flex;
  line-height: 22px;
  gap: 15px;
  margin-bottom: 15px;
}

.radio-wrapper input[type="radio"] {
  width: 25px;
  height: 25px;
  min-width: 25px;
  min-height: 25px;
  margin: 0;
  border-color: #436eb699 !important;
}

.radio-wrapper input[type="radio"]:active {
  box-shadow: none;
}

.radio-wrapper .radio-label {
  cursor: pointer;
  font-size: 2rem;
  font-weight: 700;
}

.radio-wrapper .radio-text {
  line-height: 22px;
  margin-top: -5px;
}

.form-check-input[type="radio"] {
  --bs-form-check-bg: transparent;
}

.form-check-input:checked[type="radio"] {
  --bs-form-check-bg-image: url("../media/check-bg-image.360baf3d.svg");
  background-color: #fff;
  border-color: #436eb6;
  box-shadow: none;
}

.custom-datepicker {
  align-items: center;
  justify-content: space-between;
  display: flex !important;
  font-weight: 300 !important;
}

.custom-datepicker svg path {
  fill: #fff;
}

.calendar {
  position: absolute;
  top: 110%;
  left: 0;
  z-index: 100;
  background-color: #2b2b2b;
  color: #fff;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 14px #0009;
  width: 100%;
  max-width: 300px;
}

.calendar .rdp-root {
  background-color: inherit;
  color: inherit;
}

.calendar .rdp-nav {
  display: flex;
  justify-content: space-between;
  margin-bottom: .5rem;
}

.calendar .rdp-nav .rdp-button_previous, .calendar .rdp-nav .rdp-button_next {
  background: none;
  border: none;
  color: #ddd;
  cursor: pointer;
  padding: .25rem;
}

.calendar .rdp-nav .rdp-button_previous:hover, .calendar .rdp-nav .rdp-button_next:hover {
  background-color: #3a3a3a;
  border-radius: 4px;
}

.calendar .rdp-nav .rdp-button_previous .rdp-chevron polygon, .calendar .rdp-nav .rdp-button_next .rdp-chevron polygon {
  fill: #ddd;
}

.calendar .rdp-caption_label {
  font-weight: 600;
  font-size: 1rem;
  color: #f0f0f0;
}

.calendar .rdp-weekday {
  color: #828282;
  font-weight: 700;
  font-size: 1.4rem;
  text-transform: uppercase;
  padding-bottom: .5rem;
  text-align: center;
}

.calendar .rdp-day button {
  background: none;
  border: none;
  color: #eee;
  padding: 2px;
  border-radius: 6px;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 1.4rem;
}

.calendar .rdp-day button:hover {
  background-color: #444;
}

.calendar .rdp-day.rdp-today button {
  border: 1px solid #cb9932;
}

.calendar .rdp-day.rdp-selected button {
  background-color: #cb9932;
  color: #333;
}

.calendar .rdp-day.rdp-outside button {
  color: #666;
  opacity: .5;
}

.calendar .rdp-month_grid {
  width: 100%;
  border-collapse: collapse;
}

.calendar .rdp-week {
  display: table-row;
}

.calendar .rdp-day {
  display: table-cell;
  text-align: center;
}

.calendar .rdp-months {
  position: relative;
}

.calendar .rdp-months .rdp-month_caption {
  position: absolute;
  top: 4px;
  left: 0;
  right: 0;
  text-align: center;
  width: 80%;
  margin: 0 auto;
}

.calendar .rdp-months .rdp-month_caption span {
  font-size: 1.4rem;
}

.react-time-picker {
  padding: 10.5px 15px !important;
}

.react-time-picker .react-time-picker__wrapper {
  border: 0 !important;
}

.react-time-picker .react-time-picker__wrapper select {
  padding: 0;
  border: 0;
}

.react-time-picker .react-time-picker__wrapper input, .react-time-picker .react-time-picker__wrapper select {
  color: #fff;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button {
  padding-block: 0;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg line, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg line {
  stroke: #fff;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg circle, .react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg path, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg circle, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg path {
  stroke: #fff;
}

.custom-checkbox {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 12px;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #333;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 22px;
  width: 22px;
  background-color: #eee;
  border-radius: 4px;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #436eb6;
}

.custom-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.forgot-password {
  color: #436eb6;
  font-weight: 600;
  font-size: 1.4rem;
}

.otp-main {
  margin-bottom: 10px;
}

.otp-main div {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5%;
}

.otp-main input {
  text-align: center;
  background-color: #fff;
  border: 1px solid #707070;
  border-radius: 10px;
  color: #000;
  font-size: 14px;
  height: 47px;
  width: 100% !important;
}

.react-multi-select.show-active {
  border: 1px solid #436eb6;
}

.react-multi-select .select__control {
  background: none;
  border: none;
  min-height: 45px;
}

.react-multi-select .select__control.select__control--menu-is-open, .react-multi-select .select__control.select__control--is-focused {
  box-shadow: none;
}

.react-multi-select .select__control .select__multi-value {
  color: #333;
  align-items: center;
  padding: 2px 6px;
  border-radius: 8px;
  gap: 8px;
  background: #cb9932;
}

.react-multi-select .select__control .select__multi-value .select__multi-value__label {
  color: #333;
  font-size: 1.2rem;
  font-weight: 500;
}

.react-multi-select .select__control .select__multi-value .select__multi-value__remove {
  color: #333;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
  color: #fff;
}

.react-multi-select .select__control .select__multi-value .select__multi-value__remove:hover {
  background-color: #d00000;
  color: #fff;
}

.common-datepicker-wrapper .react-datepicker-wrapper .react-datepicker__input-container {
  display: flex;
  gap: 15px;
  align-items: center;
  line-height: 1;
}

.common-datepicker-wrapper .react-datepicker-wrapper .react-datepicker__input-container .react-datepicker__calendar-icon {
  position: static;
  width: 18px;
  height: 18px;
  min-width: 18px;
  padding: 0;
  line-height: 1;
}

.common-datepicker-wrapper .react-datepicker-wrapper .react-datepicker__input-container input {
  padding: 0;
  background: none;
  border: none;
  outline: none;
}

.common-datepicker-wrapper .react-datepicker {
  background: #fff;
  width: 100%;
  display: block;
  overflow: hidden;
  font-size: 16px;
  min-height: 46px !important;
  padding: 0 !important;
  border: 1px solid #436eb6 !important;
  border-radius: 18px !important;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day {
  font-size: 16px;
  width: 35px;
  line-height: 2.5rem;
  padding: 5px !important;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day:disabled {
  opacity: .5 !important;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day--selected {
  background: #436eb6 !important;
  color: #fff !important;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day--today {
  color: #cb9932;
  border-radius: 10px;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day-names div, .common-datepicker-wrapper .react-datepicker .react-datepicker__day div {
  font-size: 16px;
  width: 35px;
  line-height: 2.5rem;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__day {
  font-size: 16px;
}

.common-datepicker-wrapper .react-datepicker .react-datepicker__current-month {
  font-size: 16px;
  margin-bottom: 5px;
}

.common-datepicker-wrapper .react-datepicker-popper {
  top: 5px !important;
  left: 0 !important;
  transform: translate(0, 69px) !important;
}

.react-time-picker {
  padding: 10.5px 15px !important;
}

.react-time-picker .react-time-picker__wrapper {
  border: 0 !important;
}

.react-time-picker .react-time-picker__wrapper select {
  padding: 0;
  border: 0;
}

.react-time-picker .react-time-picker__wrapper input, .react-time-picker .react-time-picker__wrapper select {
  color: #333;
}

.react-time-picker .react-time-picker__wrapper input:focus, .react-time-picker .react-time-picker__wrapper select:focus {
  outline: none;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button {
  padding-block: 0;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg line, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg line {
  stroke: #333;
}

.react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg circle, .react-time-picker .react-time-picker__wrapper .react-time-picker__clock-button svg path, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg circle, .react-time-picker .react-time-picker__wrapper .react-time-picker__clear-button svg path {
  stroke: #333;
}

.table-header {
  margin-bottom: 15px;
}

.table-header h4 {
  color: #fff;
  font-size: 1.8rem;
  font-weight: 600;
}

.table-header h4 span {
  color: #cb9932;
  font-weight: 600;
}

.table-responsive {
  font-size: 1.4rem;
  border-radius: 12px;
}

.table-responsive.min-data {
  min-height: 300px;
}

.table-responsive .table {
  border-radius: 12px;
  overflow: hidden;
}

.table-responsive .table th, .table-responsive .table td {
  color: #333;
  padding: 10px 15px;
  white-space: nowrap;
}

.table-responsive .table thead {
  position: sticky;
  top: 0;
  width: 100%;
  backdrop-filter: blur(10px);
  z-index: 100;
}

.table-responsive .table thead tr th {
  font-size: 1.6rem;
  background-color: #436eb6;
  color: #fff;
}

.table-responsive .table tbody tr td {
  border-color: #fff6;
  vertical-align: middle;
}

.table-responsive .table tbody tr td .clear-btn svg {
  width: 20px;
  height: 20px;
}

.table-responsive .table tbody tr:last-child td {
  border: none;
}

.table-responsive .table tbody tr:nth-child(odd) td {
  background-color: #436eb60f;
}

.table-responsive .table a {
  font-weight: 500;
}

.table-responsive .table a.dark {
  color: #333;
}

.table-responsive .table a.primary {
  color: #436eb6;
}

.table-responsive .table a.underline {
  text-decoration: underline !important;
}

.table-responsive .table .multi-user-list {
  display: flex;
  align-items: center;
}

.table-responsive .table .multi-user-list li img {
  width: 25px;
  height: 25px;
  border-radius: 100%;
  object-fit: cover;
  border: 1px solid #fff;
  overflow: hidden;
}

.table-responsive .table .multi-user-list li:not(:first-child) {
  margin-left: -8px;
}

.table-responsive .table .multi-action-links {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: flex-start;
}

.upload-card {
  border-radius: 16px;
  border: 1px dashed #333333b3;
  background: #3333330d;
  text-align: center;
  width: 100%;
  padding: 40px 20px;
  position: relative;
  min-height: 138px;
}

.upload-card input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-card .upload-box-inner svg {
  width: 42px;
  min-width: 42px;
  height: 42px;
  margin-bottom: 15px;
}

.upload-card .upload-box-inner p {
  color: #333;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  opacity: .7;
}

.upload-card .upload-box-inner p.uploading-message {
  opacity: 1;
  color: #436eb6;
  font-weight: 600;
  transition: opacity .3s ease-in-out;
  animation: .5s ease-in-out fadeInOut;
}

.upload-card.upload-card-sm {
  padding: 20px;
}

.uploded-item {
  border-radius: 16px;
  border: 1px solid #bdbdbd;
  background-color: #fff;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  width: fit-content;
}

.uploded-item .item-name {
  display: flex;
  align-items: center;
  gap: 15px;
}

.uploded-item .item-name svg {
  width: 32px;
  min-width: 32px;
  height: 32px;
}

.uploded-item .item-name p {
  color: #333;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
}

.uploded-item .delete-item {
  cursor: pointer;
  width: 32px;
  min-width: 32px;
  height: 32px;
}

.uploded-item.upload-card-sm {
  padding: 10px;
  border-radius: 8px;
  margin-top: 10px;
}

.uploded-item.upload-card-sm .item-name svg {
  width: 22px;
  min-width: 22px;
  height: 22px;
}

.uploded-item.upload-card-sm .item-name p {
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.uploded-item.upload-card-sm .delete-item {
  cursor: pointer;
  width: 22px;
  min-width: 22px;
  height: 22px;
}

@keyframes fadeInOut {
  0% {
    opacity: .7;
    transform: translateY(2px);
  }

  50% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-modal {
  backdrop-filter: blur(3px);
  background: #436eb680;
}

.theme-modal.show-modal {
  display: block;
}

.theme-modal .modal-dialog.modal-xl {
  max-width: 1000px;
  width: 90%;
}

.theme-modal .modal-dialog .modal-content {
  border-radius: 16px;
  background-color: #fff;
  border: 0;
  box-shadow: 0 7px 2px #0000, 0 5px 2px #00000003, 0 3px 2px #00000005, 0 1px 1px #00000008, 0 0 1px #0000000a;
}

.theme-modal .modal-dialog .modal-content .modal-header {
  border: 0;
  padding: 20px;
  position: relative;
  display: block;
  text-align: center;
}

.theme-modal .modal-dialog .modal-content .modal-header.secondary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #3333331a;
}

.theme-modal .modal-dialog .modal-content .modal-header h2 {
  color: #212121;
  font-size: 32px;
  line-height: normal;
  margin: 15px 0;
  padding: 0;
  font-weight: 600;
}

.theme-modal .modal-dialog .modal-content .modal-header h4 {
  color: #212121;
  font-size: 26px;
  line-height: normal;
  margin: 10px 0;
  padding: 0;
  font-weight: 600;
}

.theme-modal .modal-dialog .modal-content .modal-header h4 span {
  color: #436eb6;
}

.theme-modal .modal-dialog .modal-content .modal-header p {
  max-width: 70%;
  margin: 0 auto;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.theme-modal .modal-dialog .modal-content .modal-header p.textMd {
  font-size: 1.6rem;
}

.theme-modal .modal-dialog .modal-content .modal-header p.w100 {
  width: 100%;
  max-width: 100%;
}

.theme-modal .modal-dialog .modal-content .modal-header .modal-close-btn {
  padding: 0;
  background-color: #0000;
  margin: 0;
  position: absolute;
  top: -13px;
  right: -9px;
  border: 0;
  z-index: 100;
}

.theme-modal .modal-dialog .modal-content .modal-header .modal-close-btn svg {
  width: 30px;
  height: 30px;
  min-width: 30px;
}

.theme-modal .modal-dialog .modal-content .modal-body {
  padding: 20px;
}

.theme-modal .modal-dialog .modal-content .modal-body .information-preview .preview-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 18px;
}

.theme-modal .modal-dialog .modal-content .modal-body .information-preview .preview-section {
  margin-bottom: 16px;
}

.theme-modal .modal-dialog .modal-content .modal-body .information-preview .preview-section .preview-section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #436eb6;
}

.theme-modal .modal-dialog .modal-content .modal-body .information-preview .preview-section .preview-list {
  padding-left: 20px;
}

.theme-modal .modal-dialog .modal-content .modal-body .information-preview .preview-section .preview-list .preview-item {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 500;
}

.theme-modal .modal-dialog .modal-content .modal-body .alert-warning {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
}

.theme-modal .modal-dialog .modal-content .modal-body .action-btn {
  display: flex;
  align-items: center;
  gap: 15px;
}

.theme-modal .modal-dialog .modal-content .modal-body .action-btn .theme-button {
  min-width: 120px;
  font-size: 16px;
}

.theme-modal.applications-sources-modal .modal-body .applications-list {
  margin-bottom: 10px;
}

.theme-modal.applications-sources-modal .modal-body .applications-list .item {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #0003;
}

.theme-modal.applications-sources-modal .modal-body .applications-list .item:last-child {
  border-bottom: none;
}

.theme-modal.applications-sources-modal .modal-body .applications-list .item .left-item {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.theme-modal.applications-sources-modal .modal-body .applications-list .item .left-item img {
  height: 30px;
  object-fit: contain;
}

.theme-modal.applications-sources-modal .modal-body .applications-list .item .item-right {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.theme-modal .interview-info-img {
  width: 110px;
  height: 180px;
  object-fit: contain;
  position: absolute;
  top: -60px;
  right: 60px;
  z-index: 0;
}

.theme-modal .copy-link-icon {
  width: 20px;
  height: 15px;
  fill: #333;
  margin-right: 10px;
}

.theme-modal .model-heading-lottie {
  position: relative;
}

.theme-modal .model-heading-lottie .lottie-icon {
  width: 100px;
  height: 80px;
  object-fit: contain;
  position: absolute;
  top: -20px;
  right: 80px;
}

.interview-details-modal p {
  font-size: 1.6rem;
  font-weight: 400;
  color: #333;
  margin: 0;
  margin-bottom: 10px;
  line-height: 1;
}

.interview-details-modal h4 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  margin-bottom: 10px;
}

.interview-details-modal h5 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1;
}

.interview-details-modal .high-light-text {
  background: #cb99324d;
  color: #333;
  padding: 7px 12px;
  border-radius: 8px;
  font-size: 1.4rem;
  display: inline-block;
  line-height: 1;
}

.interview-details-modal a {
  font-size: 1.6rem;
  color: #436eb6;
  line-height: 1;
}

.interview-details-modal sup {
  color: #d00000;
}

.editor-skeleton {
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  width: 100%;
  min-height: 400px;
  box-shadow: 0 1px 3px #0000001a;
  overflow: hidden;
  position: relative;
}

.editor-skeleton .skeleton-toolbar {
  padding: 10px;
  display: flex;
  gap: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  position: absolute;
  bottom: 0;
  width: 100%;
}

.editor-skeleton .skeleton-toolbar .skeleton-button {
  height: 24px;
  width: 30px;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: 1.5s infinite loading;
  border-radius: 3px;
}

.editor-skeleton .skeleton-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: calc(100% - 45px);
}

.editor-skeleton .skeleton-content .skeleton-paragraph {
  height: 18px;
  width: 100%;
  background: linear-gradient(90deg, #eee 0%, #f5f5f5 50%, #eee 100%);
  background-size: 200% 100%;
  animation: 1.5s infinite loading;
  border-radius: 3px;
}

.editor-skeleton .skeleton-content .skeleton-paragraph:nth-child(2) {
  width: 90%;
}

.editor-skeleton .skeleton-content .skeleton-paragraph:nth-child(3) {
  width: 85%;
}

.editor-skeleton .skeleton-content .skeleton-paragraph:nth-child(4) {
  width: 95%;
}

.editor-skeleton .skeleton-content .skeleton-paragraph:nth-child(5) {
  width: 80%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

* {
  font-family: Plus Jakarta Sans, sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

html {
  font-size: 62.5%;
}

.sun-editor-editable strong {
  font-weight: bold;
}

.sun-editor-editable em {
  font-style: italic;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
}

p {
  font-size: 1.6rem;
  margin: 0;
  padding: 0;
  font-weight: 300;
}

@media only screen and (width <= 767px) {
  p {
    font-size: 1.6rem;
  }
}

a {
  font-size: 1.6rem;
  cursor: pointer;
  text-decoration: none !important;
}

@media only screen and (width <= 767px) {
  a {
    font-size: 1.6rem;
  }
}

.font12 {
  font-size: 1.2rem !important;
}

.font14 {
  font-size: 1.4rem !important;
}

.font16 {
  font-size: 1.6rem !important;
}

.font18 {
  font-size: 1.8rem !important;
}

.color-primary {
  color: #436eb6 !important;
}

.color-success {
  color: #073 !important;
}

.color-dark {
  color: #333 !important;
}

.color-white {
  color: #fff !important;
}

.color-secondary {
  color: #cb9932 !important;
}

.color-danger {
  color: #d00000 !important;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

input[type="file"], input[type="file"]::-webkit-file-upload-button {
  cursor: pointer;
}

input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #e8e0ff;
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #436eb6;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #436eb6;
}

.button-align {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.common-section-spacing {
  padding: 55px 0;
}

.skeleton-shimmer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: none;
  background-color: #ebebeb !important;
}

.skeleton-shimmer * {
  opacity: 0;
}

.skeleton-shimmer:after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #0000, #f5f5f5e6, #0000);
  animation: 1.5s infinite shimmer;
  z-index: 200;
}

.skeleton-shimmer:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #ebebebe6;
  z-index: 100;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

.responsive-tooltip {
  z-index: 9999;
  font-size: 1.4rem;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #436eb6 !important;
  color: #fff !important;
}

.react-tooltip {
  font-size: 10px !important;
}

.dotted-border {
  border-bottom: 1px dotted #00000080;
}

.common-heading h2 {
  color: #fff;
}

.common-heading h2 span {
  color: #cb9932;
  font-size: 4rem;
}

.common-heading p {
  font-size: 1.4rem;
  color: #fff;
}

.common-heading p span {
  color: #cb9932;
}

.section-heading h2 {
  font-size: 2.4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.section-heading h2 span {
  color: #cb9932;
}

.section-heading h2 span.primary {
  color: #436eb6;
}

.section-heading p {
  font-size: 1.6rem;
  color: #333;
  font-weight: 500;
}

.avatar-desc {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.4rem;
}

.avatar-desc img, .avatar-desc svg {
  width: 35px;
  height: 35px;
  border-radius: 100px;
  border: 1px solid #cdcdcd;
}

.avatar-desc svg {
  border: none;
  border-radius: 0;
}

.avatar-desc .avatar-para p {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 500;
}

.avatar-desc .avatar-para p:first-child {
  font-size: 1rem;
  font-weight: 400;
}

.avatar-desc .button-align .user {
  color: #020202;
  font-weight: 400;
}

.avatar-desc .button-align p {
  font-size: 1.6rem;
  margin: 0;
}

.avatar-desc .button-align .date-share {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.avatar-desc .button-align .date-share .dot {
  width: 4px;
  height: 4px;
  opacity: .5;
}

.avatar-desc .button-align .date-share .date {
  opacity: .6;
}

.avatar-desc .button-align .date-share button svg {
  width: 20px;
  height: 20px;
}

.information-box {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1rem;
  padding: 10px;
  background: linear-gradient(0deg, #6b90ca 4.2%, #436eb6 90.9%);
}

.information-box p {
  font-size: 1.4rem;
  color: #fff;
  font-weight: 500;
  margin: 0;
}

.common-box {
  display: flex;
  gap: 20px;
}

.common-box .main-content {
  flex: 1;
  width: calc(100% - 70px);
}

.common-page-header {
  padding: 40px 0;
}

.common-page-header .breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.common-page-header .breadcrumb a {
  color: #333;
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0;
}

.common-page-header .breadcrumb a:not(:last-child) {
  color: #333333b3;
  font-weight: 500;
}

.common-page-header .breadcrumb a:not(:last-child):after {
  content: "/";
  margin: 0 2px;
}

.common-page-header .breadcrumb a:hover {
  text-decoration: underline;
  color: #436eb6;
}

.common-page-head-section .main-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.common-page-head-section .main-heading h2 {
  font-size: 2.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.common-page-head-section .main-heading h2 span {
  color: #436eb6;
}

@media (width <= 767px) {
  .common-page-head-section .main-heading h2 {
    font-size: 2.4rem;
  }
}

.common-page-head-section .main-heading .right-action {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-end;
}

.common-page-head-section .main-heading .right-action .search-input {
  min-width: 400px;
}

.common-page-head-section .description {
  font-size: 1.6rem;
  color: #333;
  padding-top: 15px;
  font-weight: 500;
}

.custom-dropdown {
  position: absolute;
  top: 26px;
  right: 5px;
  background: #fff;
  box-shadow: 0 4px 12px #0000001f;
  border-radius: 12px;
  padding: 8px;
  min-width: 180px;
  z-index: 10;
}

.custom-dropdown li {
  cursor: pointer;
  padding: 8px;
  border-bottom: 1px dashed #0b0b0b4d;
  text-align: left;
}

.custom-dropdown li:hover {
  color: #436eb6;
}

.custom-dropdown li:last-child {
  border-bottom: none;
}

.permissions-card {
  background: #cb99321a;
  border-radius: 20px;
}

.permissions-card h3 {
  margin-bottom: 25px;
  font-weight: 700;
}

.permissions-card .checbox-group {
  padding: 15px 15px 15px 20px;
  margin: 10px 0;
  border-radius: 10px;
}

.permissions-card .checbox-group:last-child {
  margin-bottom: 0;
}

.permissions-card .checbox-group li {
  margin-bottom: 15px;
}

.permissions-card .checbox-group li:last-child {
  margin-bottom: 0;
}

.permissions-card .checbox-group label {
  font-size: 1.4rem;
  margin-bottom: 10px;
}

.permissions-card .checbox-group label:last-child {
  margin-bottom: 0;
}

.permissions-card .checbox-group label span {
  width: 16px;
  height: 16px;
}

.permissions-card .checbox-group label span:after {
  left: 4px;
  top: 1px;
}

.permissions-card .checbox-group label .permission-item {
  display: flex;
  flex-direction: column;
  margin-right: 20px;
}

.permissions-card .checbox-group label .permission-item .permission-name {
  font-weight: 700;
  margin-bottom: 2px;
}

.permissions-card .checbox-group label .permission-item .permission-description {
  font-size: .85rem;
  color: #666;
  font-weight: normal;
}

.container-checkbox {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 1.4rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #333;
}

.container-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #333;
}

.container-checkbox:hover input ~ .checkmark {
  background-color: #fff;
}

.container-checkbox input:checked ~ .checkmark {
  background-color: #436eb6;
  border-color: #0000;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.container-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid #fff;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.library-folder {
  margin: 50px 0 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 1rem;
  height: fit-content;
}

.library-folder .folder-card {
  border-radius: 10px;
  border: 2px solid #0000000f;
  display: inline-block;
  padding: 10px 15px;
  width: 15.9%;
  cursor: pointer;
  text-align: center;
}

.library-folder .folder-card > div {
  display: flex;
  justify-content: flex-end;
}

.library-folder .folder-card button svg {
  transform: rotate(90deg);
  width: 20px;
  opacity: .5;
}

.library-folder .folder-card p {
  font-size: 1.6rem;
  text-align: center;
  font-weight: 600;
  margin-top: 5px;
  margin-bottom: 15px;
}

.library-folder .folder-card .folder-icon {
  width: 145px;
  height: auto;
  object-fit: contain;
}

.green-dot {
  position: relative;
}

.green-dot:after {
  content: "";
  position: absolute;
  top: 7px;
  right: -20px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #073;
}

.red-dot {
  position: relative;
}

.red-dot:after {
  content: "";
  position: absolute;
  top: 7px;
  right: -20px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: #d00000;
}

@keyframes skeletonPulse {
  0% {
    opacity: .6;
  }

  50% {
    opacity: .3;
  }

  100% {
    opacity: .6;
  }
}

.skeleton-pulse {
  background-color: #e0e0e0;
  animation: 1.5s ease-in-out infinite skeletonPulse;
  border-radius: 4px;
}

.fas-swiper-container {
  position: relative;
  margin-bottom: 20px;
}

.fas-swiper-container .summary-text-card {
  margin-bottom: 10px;
}

.fas-swiper-container .swiper-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fas-swiper-container .swiper-footer .swiper-pagination-text {
  width: auto;
  font-size: 1.6rem;
  font-weight: 700;
}

.fas-swiper-container .swiper-footer .swiper-pagination-text .swiper-pagination-current {
  color: #436eb6;
}

.fas-swiper-container .swiper-footer .swiper-pagination-text .swiper-pagination-total {
  color: #333;
}

.fas-swiper-container .swiper-footer .custom-nav-buttons {
  gap: 15px;
  display: flex;
  align-items: center;
}

.fas-swiper-container .swiper-footer .custom-nav-buttons button {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
}

.fas-swiper-container .swiper-footer .custom-nav-buttons button.swiper-button-disabled {
  background: #3333331a;
  cursor: not-allowed;
}

.fas-swiper-container .swiper-footer .custom-nav-buttons button.swiper-button-disabled svg {
  stroke: #33333380;
}

.fas-swiper-container .swiper-footer .custom-nav-buttons button svg {
  stroke: #fff;
  width: 16px;
  height: 16px;
}

.fas-swiper-container .swiper-footer .custom-nav-buttons button.rotate {
  transform: rotate(180deg);
}

.video_call_section {
  border: 1px solid #3333331a;
  border-radius: 12px;
}

.video_call_section .interview-content {
  padding: 20px;
}

.video_call_section .interview-content .common-page-head-section .main-heading {
  margin-bottom: 20px;
}

.video_call_section .interview-content .common-page-head-section .main-heading h2 {
  font-size: 1.8rem;
}

.video_call_section .interview-content .interview-question-cards-height {
  height: 60vh;
  overflow-y: auto;
}

.sun-editor {
  border-radius: 9px !important;
}

.sun-editor .se-toolbar {
  background-color: #436eb6 !important;
  border-bottom: 1px solid #436eb6 !important;
  border-radius: 8px !important;
}

.sun-editor .se-btn-tray button {
  color: #fff !important;
  background-color: #436eb6 !important;
}

.sun-editor .se-btn-tray button:hover {
  background-color: #436eb6 !important;
}

.sun-editor .se-toolbar-separator-vertical {
  background-color: #fff !important;
}

.career-based-skills .career-skill-card {
  min-height: 150px;
}

@media (width <= 1400px) and (width >= 1200px) {
  .career-based-skills .career-skill-card {
    min-height: 170px;
  }
}

.role-based-skills .career-skill-card {
  min-height: 125px;
}

.culture-based-skills .career-skill-card {
  min-height: 125px;
}

.calendar-container .fc-header-toolbar .fc-button-group button {
  border: none;
  border-radius: 16px;
  font-size: 14px;
  border: 1px solid #0000;
  transition: background-color .2s;
  font-weight: 600;
  text-transform: capitalize;
  box-shadow: 0 2px 5px #32326926, 0 1px 1px #0000000d;
  background-color: #fff !important;
  color: #3339 !important;
  border-color: #33333317 !important;
}

.calendar-container .fc-header-toolbar .fc-button-group button:hover {
  background-color: #333 !important;
  color: #fff !important;
  border-color: #333 !important;
}

.calendar-container .fc-header-toolbar .fc-button-group button:focus {
  box-shadow: none !important;
}

.calendar-container .fc-header-toolbar .fc-toolbar-title {
  padding: 6px 20px;
  border-radius: 8px;
  border: 1px solid #0000;
  text-transform: capitalize;
  box-shadow: 0 2px 5px #32326926, 0 1px 1px #0000000d;
  color: #333c !important;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
}

.calendar-container table thead tr th .fc-col-header-cell-cushion {
  color: #333c;
  font-weight: 500;
  font-size: 1.4rem;
  padding: 15px 0;
}

.calendar-container table tbody tr td .fc-timegrid-slot-label-frame {
  padding: 15px 0;
  font-size: 1.2rem;
}

.calendar-container table .fc-day-today {
  background-color: #34a8531a !important;
}

.calendar-container table .fc-day-today .fc-daygrid-day-number {
  font-weight: 700;
  color: #34a853 !important;
}

.calendar-container .fc-dayGridMonth-view, .calendar-container .fc-view-harness {
  border: 1.5px solid #3333;
  border-radius: 12px;
  box-shadow: 0 60px 120px #26334d0d;
  overflow: hidden;
}

.calendar-container .fc-dayGridMonth-view * a, .calendar-container .fc-view-harness * a {
  color: #333;
}

.calendar-container .fc-dayGridMonth-view table thead tr th .fc-col-header-cell-cushion, .calendar-container .fc-view-harness table thead tr th .fc-col-header-cell-cushion {
  color: #333c;
  font-weight: 500;
  font-size: 1.4rem;
  padding: 15px 0;
}

.calendar-container .fc-dayGridMonth-view table .fc-day-today, .calendar-container .fc-view-harness table .fc-day-today {
  background-color: #34a8531a !important;
}

.calendar-container .fc-dayGridMonth-view table .fc-day-today .fc-daygrid-day-number, .calendar-container .fc-view-harness table .fc-day-today .fc-daygrid-day-number {
  font-weight: 700;
  color: #34a853 !important;
}

.calendar-container .fc-dayGridMonth-view .fc-col-header .fc-col-header-cell-cushion, .calendar-container .fc-view-harness .fc-col-header .fc-col-header-cell-cushion {
  color: #333c;
  font-weight: 500;
  font-size: 1.4rem;
  padding: 15px 0;
}

.calendar-container .fc-dayGridMonth-view .fc-event, .calendar-container .fc-view-harness .fc-event {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  background: #34a8531a;
  padding: 5px;
  border: 1px solid #34a853;
  border-radius: 8px;
}

.calendar-container .fc-dayGridMonth-view .fc-event .fc-event-content, .calendar-container .fc-view-harness .fc-event .fc-event-content {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 5px;
}

.calendar-container .fc-dayGridMonth-view .fc-event .fc-event-content .fc-event-time, .calendar-container .fc-view-harness .fc-event .fc-event-content .fc-event-time {
  padding: 5px;
  background: green;
  display: inline;
  color: #fff;
  border-radius: 5px;
  line-height: 1;
  font-size: 10px;
}

.calendar-container .fc-dayGridMonth-view .fc-event .fc-event-content .fc-event-title, .calendar-container .fc-view-harness .fc-event .fc-event-content .fc-event-title {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.calendar-container .fc-dayGridMonth-view .fc-event div, .calendar-container .fc-view-harness .fc-event div {
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
  text-wrap: auto;
}

.calendar-container .fc-dayGridMonth-view .fc-event .fc-daygrid-event-dot, .calendar-container .fc-view-harness .fc-event .fc-daygrid-event-dot {
  border-color: #34a853;
}

.calendar-container .fc-daygrid-more-link {
  font-size: 1.2rem;
  background: none;
  padding: 5px;
  border-radius: 5px;
  line-height: 1;
  width: 100%;
  text-align: center;
  font-weight: 700;
  border: 1px solid #436eb6;
  border-radius: 5px;
  text-transform: capitalize;
  color: #436eb6 !important;
}

.calendar-container .fc-daygrid-more-link:hover {
  background-color: #333 !important;
  color: #fff !important;
  border-color: #333 !important;
}

.calendar-container .fc-more-popover .fc-popover-title {
  font-size: 1.2rem;
}

.calendar-container .fc-more-popover .fc-popover-body .fc-daygrid-event-harness {
  margin-bottom: 10px;
}

.calendar-container .fc-more-popover .fc-popover-body .fc-daygrid-event-harness:last-child {
  margin-bottom: 0;
}

.calendar-container .fc-more-popover .fc-popover-body .fc-daygrid-event-harness .fc-event {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  background: #34a8531a;
  padding: 5px;
  border: 1px solid #34a853;
  border-radius: 8px;
}

.calendar-container .fc-more-popover .fc-popover-body .fc-daygrid-event-harness .fc-event .fc-daygrid-event-dot {
  border-color: #333;
}

/*# sourceMappingURL=src_styles_style_scss_b52d8e88.css.map*/
import React, { useEffect, useState, useRef, useCallback } from "react";
import styles from "../../../styles/commonPage.module.scss";
import style from "@/styles/commonPage.module.scss";
import Button from "@/components/formElements/Button";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import Sidebar from "@/components/sidebar/Sidebar";
import { updateJobStatus } from "@/services/jobRequirements/updateJobServices";
import { fetchJobsMeta, Job } from "@/services/jobRequirements/jobServices";
import { useTranslations } from "next-intl";
import { fetchCandidatesApplications } from "@/services/CandidatesServices/candidatesApplicationServices";
import { AuthState } from "@/redux/slices/authSlice";
import { useSelector } from "react-redux";
import { PERMISSION } from "@/constants/commonConstants";
import { archiveActiveApplication } from "@/services/CandidatesServices/candidatesApplicationStatusUpdateService";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";
import { CandidateApplication } from "@/interfaces/candidatesInterface";
import InfiniteScroll from "react-infinite-scroll-component";
import RestoreIcon from "@/components/svgComponents/RestoreIcon";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import TableSkeleton from "../skeletons/TableSkeleton";

const ARCHIVE_TABS = {
  CANDIDATES: "Candidates",
  JOBS: "Jobs",
} as const;

type ArchiveTab = (typeof ARCHIVE_TABS)[keyof typeof ARCHIVE_TABS];

function Archive() {
  // Get user permissions from Redux store - cast to string[] to match middleware implementation
  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];

  // Check if user has permissions for candidates and jobs
  const hasArchiveRestoreJobPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_JOB_POSTS);
  const hasArchiveRestoreCandidatesPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);
  // Set default tab based on permissions
  const hasAnyPermission = hasArchiveRestoreCandidatesPermission || hasArchiveRestoreJobPermission;
  const defaultTab = hasArchiveRestoreCandidatesPermission
    ? ARCHIVE_TABS.CANDIDATES
    : hasArchiveRestoreJobPermission
      ? ARCHIVE_TABS.JOBS
      : ARCHIVE_TABS.CANDIDATES;
  const [selectedTab, setSelectedTab] = useState<ArchiveTab>(defaultTab);
  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);
  const [candidateOffset, setCandidateOffset] = useState(0);
  const [hasMoreCandidates, setHasMoreCandidates] = useState(true);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [jobOffset, setJobOffset] = useState(0);
  const [hasMoreJobs, setHasMoreJobs] = useState(true);
  const [loadingCandidates, setLoadingCandidates] = useState(false);
  const [loadingJobs, setLoadingJobs] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const [jobId, setJobId] = useState<number | null>(null);
  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations();
  const isFetchingCandidates = useRef(false);
  const isFetchingJobs = useRef(false);

  const fetchMoreCandidatesApplications = useCallback(
    async (currentOffset = 0, reset = false) => {
      if (!userData?.orgId || isFetchingCandidates.current) return;
      isFetchingCandidates.current = true;
      setLoadingCandidates(true);
      try {
        const response = await fetchCandidatesApplications({
          page: currentOffset,
          limit: DEFAULT_LIMIT,
          isActive: false,
        });

        const newCandidates: CandidateApplication[] = response?.data?.data || [];
        if (Array.isArray(newCandidates) && newCandidates.length > 0) {
          setCandidates((prev) => (reset ? newCandidates : [...prev, ...newCandidates]));
          setHasMoreCandidates(newCandidates.length >= DEFAULT_LIMIT);
          setCandidateOffset(currentOffset + newCandidates.length);
        } else {
          setHasMoreCandidates(false);
        }
      } catch (error) {
        console.error("Error fetching candidates:", error);
        setHasMoreCandidates(false);
      } finally {
        setLoadingCandidates(false);
        isFetchingCandidates.current = false;
      }
    },
    [userData?.orgId]
  );

  const fetchMoreJobs = async (currentOffset = 0, reset = false) => {
    if (isFetchingJobs.current) return;
    isFetchingJobs.current = true;
    setLoadingJobs(true);
    try {
      const result = await fetchJobsMeta({
        isActive: false,
        page: currentOffset,
        limit: DEFAULT_LIMIT,
      });

      const newJobs: Job[] = result?.data?.data || [];
      if (Array.isArray(newJobs) && newJobs.length > 0) {
        setJobs((prev) => (reset ? newJobs : [...prev, ...newJobs]));
        setHasMoreJobs(newJobs.length >= DEFAULT_LIMIT);
        setJobOffset(currentOffset + newJobs.length);
      } else {
        setHasMoreJobs(false);
      }
    } catch (err) {
      console.error("Failed to load jobs:", err);
      setError("Failed to load jobs.");
      setHasMoreJobs(false);
    } finally {
      setLoadingJobs(false);
      isFetchingJobs.current = false;
    }
  };

  useEffect(() => {
    if (selectedTab === ARCHIVE_TABS.CANDIDATES && userData?.orgId && !candidates.length) {
      fetchMoreCandidatesApplications(0, true);
    } else if (selectedTab === ARCHIVE_TABS.JOBS && userData?.orgId && !jobs.length) {
      fetchMoreJobs(0, true);
    }
  }, [fetchMoreCandidatesApplications, selectedTab, userData?.orgId]);

  useEffect(() => {
    if (selectedTab === ARCHIVE_TABS.JOBS && userData?.orgId && !jobs.length) {
      fetchMoreJobs(0, true);
    }
  }, [fetchMoreJobs, selectedTab, userData?.orgId]);

  const handleArchiveJob = async (jobId: number) => {
    setActiveDropdown(null);
    try {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      await updateJobStatus(jobId, true);
    } catch {
      console.error(t("archive_job_failed"));
    }
  };

  const handleArchiveCandidate = async (applicationId: number) => {
    try {
      await archiveActiveApplication(applicationId, true);
      setCandidates((prev) => prev.filter((c) => c.applicationId !== applicationId));
    } catch (error) {
      console.error("Failed to restore candidate:", error);
    }
  };

  const toggleDropdown = (id: number) => {
    setActiveDropdown((prev) => (prev === id ? null : id));
  };
  useEffect(() => {
    // Reset dropdown when switching tabs
    setActiveDropdown(null);
  }, [selectedTab]);

  return (
    <div className="container">
      <div className="common-page-header">
        <div className="common-page-head-section">
          <div className="main-heading">
            <h2>
              {t("hiring_manager_dashboard")} - <span>{t("archive_job")}</span>
            </h2>
            {/* <div className="d-flex">
              <NotificationIcon hasNotification={true} />
            </div> */}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="common-box">
        <Sidebar />
        <main className="main-content">
          <div className={style.dashboard_page}>
            {hasAnyPermission ? (
              <div className={`${styles.dashboard_inner_head} mt-0`}>
                <ul className={styles.header_tab}>
                  {/* Only show Candidates tab if user has permission */}
                  {hasArchiveRestoreCandidatesPermission && (
                    <li
                      className={selectedTab === ARCHIVE_TABS.CANDIDATES ? styles.active : ""}
                      onClick={() => {
                        if (candidates.length === 0) {
                          setLoadingCandidates(true);
                        }
                        setSelectedTab(ARCHIVE_TABS.CANDIDATES);
                      }}
                      style={{ cursor: "pointer" }}
                    >
                      {t("candidates")}
                    </li>
                  )}

                  {/* Only show Jobs tab if user has permission */}
                  {hasArchiveRestoreJobPermission && (
                    <li
                      className={selectedTab === ARCHIVE_TABS.JOBS ? styles.active : ""}
                      onClick={() => setSelectedTab(ARCHIVE_TABS.JOBS)}
                      style={{ cursor: "pointer" }}
                    >
                      {t("jobs")}
                    </li>
                  )}
                </ul>
              </div>
            ) : (
              <div className="text-center mt-5">
                <NoDataFoundIcon width={400} height={400} />
                <h3 className="mt-3">{t("you_dont_have_permission_to_view_archive_content")}</h3>
              </div>
            )}
            {/* Only show content if user has any permission */}
            {hasAnyPermission && (
              <>
                {/* CANDIDATES - Only show if user has permission */}
                {selectedTab === ARCHIVE_TABS.CANDIDATES && hasArchiveRestoreCandidatesPermission && (
                  <div className="table-responsive mt-5" style={{ maxHeight: "60vh", overflowY: "auto" }} id="scrollableCandidateDiv">
                    <InfiniteScroll
                      dataLength={candidates.length}
                      next={() => fetchMoreCandidatesApplications(candidateOffset)}
                      hasMore={selectedTab === ARCHIVE_TABS.CANDIDATES ? hasMoreCandidates : hasMoreJobs}
                      scrollableTarget="scrollableCandidateDiv"
                      loader={
                        loadingCandidates ? (
                          <table className="table w-100">
                            <TableSkeleton rows={5} cols={4} colWidths="120,80,100,24,24" />
                          </table>
                        ) : null
                      }
                      endMessage={
                        !loadingCandidates && candidates.length ? (
                          <table className="table w-100">
                            <tbody>
                              <tr>
                                <td colSpan={5} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                                  {t("no_more_jobs_to_fetch")}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        ) : null
                      }
                    >
                      <table className="table">
                        <thead>
                          <tr>
                            <th>{t("candidate_name")}</th>
                            <th>{t("archived_on")}</th>
                            <th>{t("reason_for_archiving")}</th>
                            <th className="text-center">{t("actions")}</th>
                          </tr>
                        </thead>
                        {candidates.length ? (
                          <tbody>
                            {candidates.map((candidate) => {
                              const date = candidate.applicationCreatedTs ? new Date(candidate.applicationCreatedTs) : null;
                              const formattedDate =
                                date && !isNaN(date.getTime())
                                  ? date.toLocaleDateString("en-US", {
                                      year: "numeric",
                                      month: "short",
                                      day: "numeric",
                                    })
                                  : "—";

                              return (
                                <tr key={candidate.applicationId}>
                                  <td>{candidate.candidateName}</td>
                                  <td>{formattedDate}</td>
                                  <td>{candidate.hiringManagerReason || "—"}</td>
                                  <td className="text-center">
                                    <div className="position-relative">
                                      <Button className="clear-btn p-0" onClick={() => toggleDropdown(candidate.applicationId)}>
                                        <ThreeDotsIcon />
                                      </Button>
                                      {activeDropdown === candidate.applicationId && (
                                        <ul className="custom-dropdown show">
                                          <li onClick={() => handleArchiveCandidate(candidate.applicationId)}>{t("restore")}</li>
                                          <li>{t("analyze_candidate_resume")}</li>
                                        </ul>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        ) : (
                          !loadingCandidates && (
                            <tbody>
                              <tr>
                                <td colSpan={5} style={{ textAlign: "center" }}>
                                  <NoDataFoundIcon width={400} height={400} />
                                </td>
                              </tr>
                            </tbody>
                          )
                        )}
                      </table>
                    </InfiniteScroll>
                  </div>
                )}

                {/* JOBS - Only show if user has permission */}
                {selectedTab === ARCHIVE_TABS.JOBS && hasArchiveRestoreJobPermission && (
                  <>
                    {error && <p className="text-danger">{error}</p>}
                    {!loadingJobs && jobs.length === 0 && (
                      <div className="text-center mt-3">
                        <NoDataFoundIcon width={400} height={400} />
                      </div>
                    )}
                    <InfiniteScroll
                      dataLength={jobs.length}
                      next={() => fetchMoreJobs(jobOffset)}
                      hasMore={selectedTab === ARCHIVE_TABS.JOBS ? hasMoreJobs : hasMoreCandidates}
                      loader={
                        loadingJobs ? (
                          <div className="row g-4 mt-3 pb-4 w-100">
                            {Array(6)
                              .fill(0)
                              .map((_, index) => (
                                <div key={`skeleton-job-${index}`} className="col-md-4">
                                  <div className="jobs-card position-relative">
                                    <div className="name d-flex justify-content-between align-items-center">
                                      <h4 className="w-100">
                                        <Skeleton width="50%" height={18} />
                                      </h4>
                                      <Skeleton circle width={24} height={24} />
                                    </div>
                                    <p className="description">
                                      <Skeleton width="100%" height={16} />
                                    </p>
                                  </div>
                                </div>
                              ))}
                          </div>
                        ) : null
                      }
                      endMessage={!loadingJobs && jobs.length && jobs.length > DEFAULT_LIMIT ? <p>{t("no_more_jobs_to_fetch")}</p> : null}
                    >
                      {jobs.length && !loadingJobs ? (
                        <div className="row g-4 mt-3 w-100 pb-4">
                          {jobs.map((job) => (
                            <div key={job.id} className="col-md-4">
                              <div className="jobs-card position-relative">
                                <div className="name d-flex justify-content-between align-items-center">
                                  <h4>{job.title}</h4>
                                  <div className="position-relative">
                                    <Button
                                      className="clear-btn p-0"
                                      onClick={() => {
                                        setJobId(job.id);
                                        handleArchiveJob(job.id);
                                      }}
                                    >
                                      <RestoreIcon className={jobId === job.id ? "rounded360" : ""} />
                                    </Button>
                                  </div>
                                </div>
                                <p className="description">
                                  {job.jobId} |{" "}
                                  {(() => {
                                    const date = job.updatedDate ? new Date(job.updatedDate) : null;
                                    return date && !isNaN(date.getTime())
                                      ? date.toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })
                                      : "—";
                                  })()}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        !loadingJobs && (
                          <div className="text-center mt-3">
                            <NoDataFoundIcon width={400} height={400} />
                          </div>
                        )
                      )}
                    </InfiniteScroll>
                  </>
                )}
              </>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default Archive;

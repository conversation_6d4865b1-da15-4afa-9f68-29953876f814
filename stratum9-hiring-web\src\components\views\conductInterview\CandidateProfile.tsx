import { use, useEffect, useState } from "react";
import Button from "@/components/formElements/Button";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import Image from "next/image";
import user from "../../../../public/assets/images/user.png";
import style from "../../../styles/conductInterview.module.scss";
import { fetchCandidateProfile, updateJobApplicationStatus } from "@/services/CandidatesServices/candidatesApplicationServices";
// import candidateProfile from "../../../../public/assets/images/doctor-strange.png";

//  Import the interface to type the API response
import type { CandidateProfileResponse } from "@/interfaces/candidatesInterface";
import AiMarkIcon from "@/components/svgComponents/AiMarkIcon";
import AIVerifiedIcon from "@/components/svgComponents/AIVerifiedIcon";
import CheckSecondaryIcon from "@/components/svgComponents/CheckSecondaryIcon";
import StarIcon from "@/components/svgComponents/StarIcon";
import Success80 from "@/components/svgComponents/Success80";
import { toastMessageError, toTitleCase } from "@/utils/helper";
import { useTranslations } from "use-intl";
import { useRouter } from "next/navigation";
import { toastMessageSuccess } from "@/utils/helper";
import Loader from "@/components/loader/Loader";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";

const CandidateProfile = ({ params }: { params: Promise<{ candidateId: string }> }) => {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState(true);
  const [activeSkillTab, setActiveSkillTab] = useState("Strengths");
  const [isProcessing, setIsProcessing] = useState(false);

  const t = useTranslations();

  //  Use the imported interface in useState to strongly type the candidate profile
  const [candidateProfileData, setCandidateProfileData] = useState<CandidateProfileResponse | null>(null);

  const paramsPromise = use(params);
  const candidateId = paramsPromise.candidateId;

  const handleTabClick = () => {
    setSelectedTab(!selectedTab);
  };

  // Handle candidate rejection
  const handleHireRejectCandidate = async (status: string) => {
    if (!candidateProfileData) return;
    setIsProcessing(true);

    try {
      const response = await updateJobApplicationStatus(
        Number(candidateProfileData.jobApplicationId), // Using candidate ID as job application ID
        status
      );

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
        // Refresh candidate data
        getCandidateProfile();
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch (error) {
      console.error("Error updating job application status:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsProcessing(false);
    }
  };

  const getCandidateProfile = async () => {
    try {
      const response = await fetchCandidateProfile(candidateId);
      if (response?.data?.success) {
        setCandidateProfileData(response.data.data); //  Save typed data
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    }
  };

  useEffect(() => {
    getCandidateProfile();
  }, []);

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                  <BackArrowIcon
                    onClick={() => {
                      router.back();
                    }}
                  />
                Candidate <span>Profile </span>
              </h2>

              {/* Conditionally render resume preview button if resumeLink exists */}
              {candidateProfileData?.resumeLink && (
                <a
                  href={candidateProfileData.resumeLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="theme-btn clear-btn text-btn primary p-0 m-0"
                >
                  <PreviewResumeIcon className="me-2" />
                  Preview Candidate Resume
                </a>
              )}
            </div>
          </div>
        </div>
        <div className="inner-section profile-section">
          <div className="candidate-profile">
            <Image
              src={candidateProfileData?.imageUrl || ""}
              alt="candidate image"
              className="candidate-image"
              width={100}
              height={100}
              priority={true}
            />
            <div className="candidate-info">
              {/*  Use candidateName from API */}
              <h3 className="candidate-name">{toTitleCase(candidateProfileData?.candidateName || "-")}</h3>

              <div className="info-container">
                <div className="info-item">
                  <p className="info-title">Post Applied For</p>
                  {/* Use jobTitle from API */}
                  <p className="info-value">{candidateProfileData?.jobTitle || "-"}</p>
                </div>
                <div className="info-item">
                  <p className="info-title">Departement</p>
                  <p className="info-value">{candidateProfileData?.department || "-"}</p>
                </div>
                <div className="info-item">
                  <p className="info-title">Rounds Cleared</p>
                  <p className="info-value">{candidateProfileData?.roundNumber || 0}</p>
                </div>
                <div className="info-item">
                  <p className="info-title">Resume Approved By</p>
                  <p className="info-value with-img">
                    <Image src={user} alt="Interviewer avatar" />
                    {/*  Use interviewerName from API */}
                    {candidateProfileData?.interviewerName || "-"}
                  </p>
                </div>
                <div className="button-align">
                  <Button
                    className="primary-btn rounded-md minWidth"
                    onClick={() => handleHireRejectCandidate(APPLICATION_STATUS.HIRED)}
                    disabled={isProcessing || !candidateProfileData}
                  >
                    {/* {isProcessing ? Hire : </Loader>} */}
                    {isProcessing ? <Loader /> : "Hire"}
                  </Button>
                  <Button
                    className="dark-outline-btn rounded-md minWidth"
                    onClick={() => handleHireRejectCandidate(APPLICATION_STATUS.FINAL_REJECT)}
                    disabled={isProcessing || !candidateProfileData}
                  >
                    {isProcessing ? <Loader /> : "Reject"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="common-tab mb-5">
            <li className={selectedTab ? "active" : ""} onClick={handleTabClick}>
              Skill-Specific Assessment
            </li>
            <li className={!selectedTab ? "active" : ""} onClick={handleTabClick}>
              Interview History
            </li>
          </div>
          {selectedTab && (
            <div className="assessment-content">
              <div className="row g-4">
                <div className="col-md-4">
                  <Success80 />
                </div>
                <div className="col-md-8">
                  <div className="summary-text-card row">
                    <div className="col-md-9">
                      <h3 className="sub-tittle mt-0">
                        <AiMarkIcon className="me-2" /> AI Summary
                      </h3>
                      <ul className="check-list">
                        <li>
                          <CheckSecondaryIcon className="me-2" /> Strong foundational skills, emotional intelligence.
                        </li>
                        <li>
                          <CheckSecondaryIcon className="me-2" /> Strong foundational skills, emotional intelligence.
                        </li>
                        <li>
                          <CheckSecondaryIcon className="me-2" /> Strong foundational skills, emotional intelligence.
                        </li>
                      </ul>
                    </div>
                    <div className="col-md-3">
                      <AIVerifiedIcon />
                    </div>
                  </div>
                </div>
              </div>
              {/* next design */}
              <div className="row g-4">
                <div className="col-lg-4">
                  <div className="summary-text-card skills-score-card">
                    <h3 className="sub-tittle mt-0">Skills Score</h3>
                    <ul className="skills-list">
                      <li className="skills-item">
                        <span className="skill-name">Hard Skills</span>
                        <span className="skill-rating">
                          <StarIcon /> 7/10
                        </span>
                      </li>
                      <li className="skills-item">
                        <span className="skill-name">Work Ethic</span>
                        <span className="skill-rating">
                          <StarIcon /> 8/10
                        </span>
                      </li>
                      <li className="skills-item">
                        <span className="skill-name">Humility</span>
                        <span className="skill-rating">
                          <StarIcon /> 6/10
                        </span>
                      </li>
                      <li className="skills-item">
                        <span className="skill-name">Drive</span>
                        <span className="skill-rating">
                          <StarIcon /> 9/10
                        </span>
                      </li>
                      <li className="skills-item">
                        <span className="skill-name">Confidence</span>
                        <span className="skill-badge">Extreme</span>
                      </li>
                      <li className="skills-item">
                        <span className="skill-name">Resourcefulness</span>
                        <span className="skill-rating">
                          <StarIcon /> 9/10
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="col-lg-8">
                  <div className="summary-text-card skills-summary-card">
                    <h3 className="sub-tittle mt-0">Skills Summary</h3>
                    <div className="skills-tags">
                      <span className={`skill-tag ${activeSkillTab === "Strengths" ? "active" : ""}`} onClick={() => setActiveSkillTab("Strengths")}>
                        Strengths
                      </span>
                      <span className={`skill-tag ${activeSkillTab === "WorkEthic" ? "active" : ""}`} onClick={() => setActiveSkillTab("WorkEthic")}>
                        Work Ethic
                      </span>
                      <span
                        className={`skill-tag ${activeSkillTab === "Communication" ? "active" : ""}`}
                        onClick={() => setActiveSkillTab("Communication")}
                      >
                        Communication
                      </span>
                      <span className={`skill-tag ${activeSkillTab === "Curiosity" ? "active" : ""}`} onClick={() => setActiveSkillTab("Curiosity")}>
                        Curiosity
                      </span>
                      <span
                        className={`skill-tag ${activeSkillTab === "Decisiveness" ? "active" : ""}`}
                        onClick={() => setActiveSkillTab("Decisiveness")}
                      >
                        Decisiveness
                      </span>
                    </div>

                    <div className="strengths-gaps">
                      {activeSkillTab === "Strengths" && (
                        <div className="row">
                          <div className="col-md-7">
                            <h4 className="skill-sub-title">Strengths</h4>
                            <ul className="strengths">
                              <li className="strength-item">Strong note-taking skills (uses OneNote and a notebook).</li>
                              <li className="strength-item">Strong note-taking skills (uses OneNote and a notebook).</li>
                              <li className="strength-item">Strong note-taking skills (uses OneNote and a notebook).</li>
                            </ul>
                            <h4 className="skill-sub-title">Potential Gaps</h4>
                            <ul className="strengths">
                              <li className="strength-item">
                                Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.
                              </li>
                            </ul>
                          </div>
                          <div className="col-md-5">
                            <div className="probability-card">
                              <h4 className="skill-sub-title">Skill Success Probability</h4>
                              <div className="progress-container">
                                <h3 className="ms-2 fw-bold">80%</h3>
                                <div className="probability-bar">
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar filled" />
                                  <div className="bar" />
                                  <div className="bar" />
                                </div>
                              </div>
                              <div className="insight-card">
                                <h4 className="insight-title">Insight</h4>
                                <p className="insight-text">The candidate has a strong foundation in [specific area].</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      {activeSkillTab === "WorkEthic" && (
                        <div>
                          <h4 className="skill-sub-title">Work Ethic</h4>
                          <ul className="strengths">
                            <li className="strength-item">Lacks experience in [specific area].</li>
                            <li className="strength-item">Could improve on [specific skill].</li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-12">
                  <div className="summary-text-card improvement-areas-card">
                    <h3 className="sub-tittle mt-0">Improvement Areas</h3>
                    <div className="row g-3">
                      <div className="col-md-4">
                        <div className="improvement-card">
                          <h4 className="title">Time Management</h4>
                          <p className="description">
                            Could benefit from using more advanced time management techniques to handle multiple projects simultaneously.
                          </p>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="improvement-card">
                          <h4 className="title">Software Proficiency</h4>
                          <p className="description">
                            While proficient in basics tools, advanced training in [specific software] would be beneficial.
                          </p>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="improvement-card">
                          <h4 className="title">Communication</h4>
                          <p className="description">
                            Could benefit from using more advanced time management techniques to handle multiple projects simultaneously.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* end next design */}
            </div>
          )}
          {!selectedTab && (
            <div className="history-content">
              <div className="interview-summary">
                <div className="summary-header">
                  <h1 className="summary-heading">Round 1 Summary</h1>
                </div>
                <div className="interviewer">
                  <h2 className="summary-title">Interview By</h2>
                  <div className="interviewer-info">
                    <Image src={user} alt="Interviewer avatar" className="interviewer-avatar" />
                    <span className="interviewer-name">Aaron Salko</span>
                  </div>
                </div>
                <div className="summary-scores">
                  <h2 className="summary-title">Scores</h2>
                  <div className="score-btns">
                    <Button className="secondary-btn rounded-md px-3 py-3">Hard Skills : 07</Button>
                    <Button className="secondary-btn rounded-md px-3 py-3">Work Ethic : 08</Button>
                    <Button className="secondary-btn rounded-md px-3 py-3">Humility : 06</Button>
                    <Button className="secondary-btn rounded-md px-3 py-3">Drive : 09</Button>
                    <Button className="secondary-btn rounded-md px-3 py-3">Confidence : Extreme</Button>
                  </div>
                </div>
                <div className="summary-highlights">
                  <h2 className="summary-title">Highlights</h2>
                  <ul className="highlight-list">
                    <li className="highlight-item">Proficient in managing schedules and using tools like [specific tools].</li>
                    <li className="highlight-item">Adaptable under high workload and challenging scenarios.</li>
                    <li className="highlight-item">Ensures compliance with safety and organizational standards.</li>
                    <li className="highlight-item">Proactive in taking ownership beyond assigned tasks.</li>
                  </ul>
                </div>
              </div>
              <div className="interview-summary">
                <div className="summary-header">
                  <h1 className="summary-heading">Your Performance Feedback</h1>
                </div>
                <div className="interviewer">
                  <div className="interviewer-info large">
                    <Image src={user} alt="Interviewer avatar" className="interviewer-avatar" />
                    <span className="interviewer-name">Aaron Salko</span>
                  </div>
                </div>
                <div className="summary-highlights">
                  <h2 className="summary-title">Highlights</h2>
                  <ul className="highlight-list">
                    <li className="highlight-item">Proficient in managing schedules and using tools like [specific tools].</li>
                    <li className="highlight-item">Adaptable under high workload and challenging scenarios.</li>
                    <li className="highlight-item">Ensures compliance with safety and organizational standards.</li>
                    <li className="highlight-item">Proactive in taking ownership beyond assigned tasks.</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CandidateProfile;

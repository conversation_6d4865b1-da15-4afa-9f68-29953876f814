/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { useForm, useFieldArray, Controller, Resolver } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

import { formSchemaValidation } from "@/validations/screenResumeValidations";

import ROUTES from "@/constants/routes";
import { GENDER_OPTIONS } from "@/constants/screenResumeConstant";

import { IFormValues } from "@/interfaces/screenResumeInterfaces";

// Interface for transformed candidate data with URLs instead of File objects
interface ITransformedCandidate {
  name: string;
  email: string;
  gender: string;
  resume: string;
  assessment: string | null;
  additionalInfo: string;
}

// Interface for transformed form values
interface ITransformedFormValues {
  candidates: ITransformedCandidate[];
}

import Loader from "@/components/loader/Loader";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import HoldIcon from "@/components/svgComponents/HoldIcon";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import InfoIcon from "@/components/svgComponents/InfoIcon";

import { toastMessageSuccess, toastMessageError, uploadFileOnS3 } from "@/utils/helper";

import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import { PDF_FILE_SIZE_LIMIT, PDF_FILE_TYPE } from "@/constants/commonConstants";
import { uploadManualCandidate } from "@/services/screenResumeServices";

/**
 * ManualUploadResume Component
 *
 * Allows hiring managers to manually upload candidate resumes and assessments.
 * Supports adding multiple candidates (up to 5) with validation for required fields.
 *
 * @returns {JSX.Element} The rendered ManualUploadResume component
 */

function ManualUploadResume({
  params,
  searchParams,
}: {
  params: Promise<{ jobId: string }>;
  searchParams: Promise<{ title: string; jobUniqueId: string }>;
}) {
  const router = useRouter();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const paramsPromise = React.use(params);
  const searchParamsPromise = React.use(searchParams);
  const t = useTranslations();
  // Initialize form with validation schema
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<IFormValues>({
    resolver: yupResolver(formSchemaValidation(t)) as unknown as Resolver<IFormValues>,
    defaultValues: {
      candidates: [{ name: "", email: "", gender: "", resume: null, assessment: null, additionalInfo: "" }],
    },
  });

  // Initialize field array for dynamic candidates
  const { fields, append, remove } = useFieldArray({
    control,
    name: "candidates",
  });

  // State for loading status during form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [candidateErrors, setCandidateErrors] = useState<Record<number, string>>({});
  const [candidateSuccess, setCandidateSuccess] = useState<Record<number, boolean>>({});
  const [candidatesToRemove, setCandidatesToRemove] = useState<number[]>([]);
  const resumeFileRef = useRef<HTMLInputElement>(null);
  const assessmentFileRef = useRef<HTMLInputElement>(null);

  // Effect to handle candidate removal after successful processing
  useEffect(() => {
    if (candidatesToRemove.length > 0) {
      console.log("Removing candidates at indices:", candidatesToRemove);

      // Remove candidates from highest index to lowest to avoid index shifting
      const sortedIndices = [...candidatesToRemove].sort((a, b) => b - a);
      sortedIndices.forEach(index => {
        console.log(`Removing candidate at index ${index}`);
        remove(index);
      });

      // Clear the removal list
      setCandidatesToRemove([]);

      // If all candidates were removed, add a new empty one
      if (candidatesToRemove.length === fields.length) {
        console.log("All candidates were removed, adding new empty candidate");
        append({ name: "", email: "", gender: "", resume: null, assessment: null, additionalInfo: "" });
      }
    }
  }, [candidatesToRemove, remove, append, fields.length]);

  useEffect(() => {
    if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {
      router.push(ROUTES.JOBS.ACTIVE_JOBS);
    }
  }, [paramsPromise.jobId, searchParamsPromise?.title]);

  /**
   * Handles form submission for candidate uploads
   *
   * Processes the submitted form data, uploads files to S3, transforms File objects to URLs,
   * calls the API to upload candidates, shows success/error messages, and redirects on success.
   *
   * @param {IFormValues} data - The validated form data containing candidate information
   * @returns {Promise<void>}
   */
  const onSubmit = async (data: IFormValues) => {
    console.log("onSubmit=========>", data);
    console.log("Number of candidates being submitted:", data.candidates.length);
    console.log("Current fields length:", fields.length);
    console.log("Candidates data:", data.candidates.map((c, i) => ({ index: i, name: c.name, email: c.email })));

    try {
      setIsSubmitting(true);
      setCandidateErrors({}); // Clear previous errors
      setCandidateSuccess({}); // Clear previous success states

      // Transform candidates by uploading files to S3 and converting File objects to URLs
      const transformedCandidates = [];
      const uploadErrors: Record<number, string> = {};

      // Process candidates sequentially to handle errors properly
      for (let index = 0; index < data.candidates.length; index++) {
        const candidate = data.candidates[index];
        let hasUploadError = false;
        let errorMessage = "";

        try {
          // Generate unique file paths for each candidate's files
          const timestamp = Date.now() + index; // Add index to ensure uniqueness

          // Process resume file (required)
          let resumeUrl: string = "";
          if (candidate.resume) {
            const resumeFilePath = `manual-uploads/${candidate.resume.name}-resume-${timestamp}.pdf`;
            const uploadResult = await uploadFileOnS3(candidate.resume, resumeFilePath);
            console.log("resumeUrl=========>", uploadResult);
            if(!uploadResult) {
              hasUploadError = true;
              errorMessage = "Resume file upload failed - please try again";
            } else {
              resumeUrl = uploadResult;
            }
          } else {
            hasUploadError = true;
            errorMessage = "Resume file is required";
          }

          // Process assessment file (optional) - only if resume upload succeeded
          let assessmentUrl: string = "";
          if (!hasUploadError && candidate.assessment) {
            const assessmentFilePath = `manual-uploads/${candidate.assessment.name}-assessment-${timestamp}.pdf`;
            const uploadResult = await uploadFileOnS3(candidate.assessment, assessmentFilePath);
            console.log("assessmentUrl=========>", uploadResult);
            if(!uploadResult) {
              hasUploadError = true;
              errorMessage = "Assessment file upload failed - please try again";
            } else {
              assessmentUrl = uploadResult;
            }
          }

          // Only add to transformedCandidates if no upload errors occurred
          if (!hasUploadError) {
            transformedCandidates.push({
              name: candidate.name,
              email: candidate.email,
              gender: candidate.gender,
              resume_file: resumeUrl,
              assessment_file: assessmentUrl,
              additional_details: candidate.additionalInfo,
            });
          } else {
            // Store the upload error for this candidate
            uploadErrors[index] = errorMessage;
          }

        } catch (fileUploadError) {
          console.error(`Error uploading files for candidate ${index + 1}:`, fileUploadError);
          uploadErrors[index] = (fileUploadError as Error)?.message || `Failed to upload files`;
        }
      }

      // Set upload errors immediately on the UI
      if (Object.keys(uploadErrors).length > 0) {
        setCandidateErrors(uploadErrors);
      }

      const jobId = Number(paramsPromise.jobId);

      console.log("transformedCandidates=========>", transformedCandidates);

      // Only proceed with backend submission if we have candidates with successful uploads
      if (transformedCandidates.length > 0) {
        // Upload candidates with transformed data (URLs instead of File objects)
        const response = await uploadManualCandidate({ candidates: transformedCandidates, job_id:jobId});
        console.log("Upload response:", response);

        if (response.data && response.data.results && response.data.results.length > 0) {
          // Process results to show per-candidate status
          const errors: Record<number, string> = { ...uploadErrors }; // Start with upload errors
          const success: Record<number, boolean> = {};
          let successCount = 0;
          let errorCount = Object.keys(uploadErrors).length; // Count upload errors

          // Create mapping from transformed candidates back to original indices
          let transformedIndex = 0;
          for (let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++) {
            // Skip candidates that had upload errors
            if (uploadErrors[originalIndex]) {
              continue;
            }

            // Process backend result for this candidate
            if (transformedIndex < response.data.results.length) {
              const result = response.data.results[transformedIndex];
              if (!result.success && result.error) {
                errors[originalIndex] = result.error;
                success[originalIndex] = false;
                errorCount++;
              } else if (result.success) {
                success[originalIndex] = true;
                successCount++;
              }
              transformedIndex++;
            }
          }

          // Identify successful candidates to remove
          const successfulIndices = Object.keys(success)
            .filter(index => success[parseInt(index)])
            .map(index => parseInt(index));

          console.log("Successful candidates to remove:", successfulIndices);

          // Update error indices for remaining candidates (before removal)
          const updatedErrors: Record<number, string> = {};
          let newIndex = 0;

          for (let originalIndex = 0; originalIndex < data.candidates.length; originalIndex++) {
            // Skip successful candidates (they will be removed)
            if (success[originalIndex]) {
              continue;
            }
            // If this candidate had an error, map it to the new index
            if (errors[originalIndex]) {
              updatedErrors[newIndex] = errors[originalIndex];
            }
            newIndex++;
          }

          console.log("Updated errors after index remapping:", updatedErrors);

          // Set the updated states
          setCandidateErrors(updatedErrors);
          setCandidateSuccess({}); // Clear success state since successful candidates will be removed

          // Trigger candidate removal via useEffect
          if (successfulIndices.length > 0) {
            setCandidatesToRemove(successfulIndices);
          }

          // Show summary toast message only
          if (successCount > 0 && errorCount > 0) {
            toastMessageSuccess(`${successCount} candidates processed successfully`);
            toastMessageError(`${errorCount} candidates failed to process`);
          } else if (successCount > 0) {
            toastMessageSuccess(`All ${successCount} candidates processed successfully`);
          } else if (errorCount > 0) {
            toastMessageError(`All ${errorCount} candidates failed to process`);
          }

          // No redirect - user stays on the same page
        } else {
          toastMessageError(response.data?.message || t("something_went_wrong"));
        }
      } else {
        // No candidates had successful uploads - all had upload errors
        const totalErrors = Object.keys(uploadErrors).length;
        toastMessageError(`All ${totalErrors} candidates failed to upload files`);
      }
    } catch (error) {
      console.error("Error uploading candidates:", error);
      toastMessageError(t("something_went_wrong"));
    } finally {
      setIsSubmitting(false);
    }
  };
  /**
   * Validates if a file meets PDF requirements
   * @param file - The file to validate
   * @returns boolean - True if validation passes, False otherwise
   */
  const handleFileChange = (file: File | null): boolean => {
    if (!file) {
      return false;
    }
    if (file.size > PDF_FILE_SIZE_LIMIT) {
      toastMessageError(t("pdf_size_five_mb"));
      return false;
    }
    // Validate file name contains PDF_FILE_NAME
    if (!file.name.includes(".pdf")) {
      console.log("file name not contains pdf");
      toastMessageError(t("unsupported_file_type"));
      return false;
    }

    // Validate file type (PDF only)
    if (!PDF_FILE_TYPE.includes(file.type)) {
      toastMessageError(t("pdf_only"));
      return false;
    }

    // Validate file size (max 5MB)
    if (file.name.length > 50) {
      toastMessageError(t("pdf_name"));
      return false;
    }

    return true;
  };

  return (
    <div ref={scrollContainerRef} className={`${style.resume_page} ${style.manual_upload_resume}`}>
      <div className="container">
        <div className={style.inner_page}>
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  <BackArrowIcon onClick={() => router.push(`${ROUTES.JOBS.ACTIVE_JOBS}`)} />
                  {t("manual_upload_resume")} <span>{searchParamsPromise?.title}</span>
                </h2>
                <Button
                  className="clear-btn p-0 color-primary"
                  onClick={() =>
                    router.push(
                      `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${paramsPromise.jobId}` +
                        `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`
                    )
                  }
                >
                  <HoldIcon className="me-2" PrimaryColor /> {t("view_panding_action")}
                </Button>
              </div>
            </div>
          </div>

          <form
            onSubmit={handleSubmit((data) => {
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: "smooth" });
              }, 50);
              onSubmit(data);
            })}
          >
            {fields.map((field, index) => (
              <div key={field.id} className={style.candidate_card}>
                <div className={`d-flex align-items-center justify-content-between ${style.candidate_card_header}`}>
                  <h3>
                    {t("candidate")} 0{index + 1}
                  </h3>
                  <div className="d-flex align-items-center gap-2">
                    {candidateSuccess[index] && (
                      <div className="text-success">
                        <p className="mb-0"><strong>✅ Successfully processed</strong></p>
                      </div>
                    )}
                    {candidateErrors[index] && (
                      <div className="text-danger">
                        <p className="mb-0"><strong>❌ {candidateErrors[index]}</strong></p>
                      </div>
                    )}
                    {index > 0 && (
                      <Button
                        type="button"
                        onClick={() => {
                          remove(index);
                          // Clear status for this candidate when removed
                          const newErrors = { ...candidateErrors };
                          const newSuccess = { ...candidateSuccess };
                          delete newErrors[index];
                          delete newSuccess[index];
                          setCandidateErrors(newErrors);
                          setCandidateSuccess(newSuccess);
                          setCandidatesToRemove([]); // Clear any pending removals
                        }}
                        className="clear-btn p-0"
                      >
                        <DeleteIcon className="p-1" />
                      </Button>
                    )}
                  </div>
                </div>
                <div className={style.candidate_card_body}>
                  <div className="row">
                    {/* Name Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.name`} required>
                          {t("name")}
                          <InfoIcon tooltip="Enter the candidate’s full name." id={`name-info-${index}`} place="right" />
                        </InputWrapper.Label>
                        <Controller
                          control={control}
                          name={`candidates.${index}.name`}
                          render={({ field }) => (
                            <input
                              {...field}
                              id={`candidates.${index}.name`}
                              type="text"
                              placeholder="Please enter full name of the candidate"
                              className="form-control"
                            />
                          )}
                        />

                        {errors.candidates?.[index]?.name && <div className="auth-msg error">{errors.candidates[index]?.name?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Email Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.email`} required>
                          {t("email")}
                          <InfoIcon
                            tooltip="Provide a valid email address to contact the candidate regarding job-related updates."
                            id={`email-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <Controller
                          control={control}
                          name={`candidates.${index}.email`}
                          render={({ field }) => (
                            <input
                              {...field}
                              id={`candidates.${index}.email`}
                              type="email"
                              placeholder="Please enter candidate’s email address"
                              className="form-control"
                            />
                          )}
                        />
                        {errors.candidates?.[index]?.email && <div className="auth-msg error">{errors.candidates[index]?.email?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Gender Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.gender`} required>
                          {t("gender")}
                          <InfoIcon
                            tooltip="Select the candidate’s gender to support inclusive hiring analytics and reporting."
                            id={`gender-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <Controller
                          control={control}
                          name={`candidates.${index}.gender`}
                          render={({ field }) => (
                            <select {...field} id={`candidates.${index}.gender`} className="form-control">
                              <option value="" disabled>
                                {t("select_gender")}
                              </option>
                              {GENDER_OPTIONS.map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          )}
                        />
                        {errors.candidates?.[index]?.gender && <div className="auth-msg error">{errors.candidates[index]?.gender?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Resume Upload Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.resume`} required>
                          {t("upload_resume")}
                          <InfoIcon
                            tooltip="Upload the candidate’s latest resume in PDF format for evaluation"
                            id={`resume-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <div className={style.input_type_file}>
                          <Controller
                            name={`candidates.${index}.resume`}
                            control={control}
                            render={({ field }) => (
                              <input
                                id={`candidates.${index}.resume`}
                                type="file"
                                ref={resumeFileRef}
                                accept=".pdf"
                                onChange={(e) => {
                                  const file = e.target.files?.[0] || null;
                                  console.log("file==============>", file);
                                  if (file) {
                                    if (handleFileChange(file)) {
                                      field.onChange(file);
                                    } else {
                                      e.target.value = "";
                                      field.onChange(null);
                                    }
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                              />
                            )}
                          />
                        </div>
                        {errors.candidates?.[index]?.resume && <div className="auth-msg error">{errors.candidates[index]?.resume?.message}</div>}
                      </InputWrapper>
                    </div>

                    {/* Assessment Upload Field */}
                    <div className="col-md-6">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.assessment`}>
                          {t("upload_assesment")}
                          <InfoIcon
                            tooltip="Attach any completed assessments or test results relevant to the job role."
                            id={`assessment-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <div className={style.input_type_file}>
                          <Controller
                            name={`candidates.${index}.assessment`}
                            control={control}
                            render={({ field }) => (
                              <input
                                id={`candidates.${index}.assessment`}
                                type="file"
                                accept=".pdf"
                                ref={assessmentFileRef}
                                onChange={(e) => {
                                  const file = e.target.files?.[0] || null;
                                  if (file) {
                                    if (handleFileChange(file)) {
                                      field.onChange(file);
                                    } else {
                                      e.target.value = "";
                                      field.onChange(null);
                                    }
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                              />
                            )}
                          />
                        </div>
                        {errors.candidates?.[index]?.assessment && (
                          <div className="auth-msg error">{errors.candidates[index]?.assessment?.message}</div>
                        )}
                      </InputWrapper>
                    </div>

                    {/* Additional Information Field */}
                    <div className="col-md-12">
                      <InputWrapper>
                        <InputWrapper.Label htmlFor={`candidates.${index}.additionalInfo`}>
                          {t("additional_info")}
                          <InfoIcon
                            tooltip="Add any extra details about the candidate like experience, availability, or preferences."
                            id={`additional-info-${index}`}
                            place="right"
                          />
                        </InputWrapper.Label>
                        <Controller
                          control={control}
                          name={`candidates.${index}.additionalInfo`}
                          render={({ field }) => (
                            <textarea
                              {...field}
                              id={`candidates.${index}.additionalInfo`}
                              rows={6}
                              placeholder={t("enter_additional_info_about_candidate")}
                              className="form-control"
                            />
                          )}
                        />
                        {errors.candidates?.[index]?.additionalInfo && (
                          <div className="auth-msg error">{errors.candidates[index]?.additionalInfo?.message}</div>
                        )}
                      </InputWrapper>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <div className={style.add_another_candidate_link}>
              <Button
                type="button"
                onClick={() => {
                  /**
                   * Handles adding another candidate to the form
                   *
                   * Checks if maximum limit (5) is reached before adding a new candidate entry.
                   * Shows a message if the limit is reached.
                   */
                  if (fields.length < 5) {
                    append({ name: "", email: "", gender: "", resume: null, assessment: null, additionalInfo: "" });
                  } else {
                    toastMessageSuccess("Maximum 5 candidates allowed");
                  }
                }}
                className="clear-btn p-0 color-primary"
                disabled={fields.length >= 5}
              >
                {t("add_candidates_resume")} {fields.length >= 5 ? "(Maximum 5 candidates allowed)" : ""}
              </Button>
            </div>

            <div className="button-align py-5">
              <Button type="submit" className="primary-btn rounded-md minWidth" disabled={isSubmitting}>
                {t("analyze")} {isSubmitting && <Loader />}
              </Button>
              <Button
                type="button"
                onClick={() => {
                  /**
                   * Resets the form to its default state
                   *
                   * Clears all form fields, errors, and success states, returning the form to its initial state
                   * with a single empty candidate entry.
                   */
                  reset();
                  setCandidateErrors({});
                  setCandidateSuccess({});
                  setCandidatesToRemove([]);
                }}
                className="dark-outline-btn rounded-md minWidth"
                disabled={isSubmitting}
              >
                {t("reset")}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default ManualUploadResume;

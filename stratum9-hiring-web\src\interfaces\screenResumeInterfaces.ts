// Define the type for a candidate
export interface ICandidate {
  name: string;
  email: string;
  gender: string;
  resume: File | null;
  assessment: File | null;
  additionalInfo: string;
}

// Define the form values type
export interface IFormValues {
  candidates: ICandidate[];
}

export interface IDashboardStats {
  totalJobs: number;
  activeJobs: number;
  candidatesHired: number;
  scheduledInterviews: number;
  resumeOnHold: number;
  upcomingInterviews: number;
}

export interface IUploadManualCandidate{
    name: string;
    email: string;
    gender: string;
    resume_file: string;
    assessment_file: string | null;
    additional_details: string;
}
@use "./abstracts" as *;

.header {
  background: $white;
  backdrop-filter: blur(10px);
  box-shadow:
    0px 24px 7px 0px rgba(0, 0, 0, 0),
    0px 15px 6px 0px rgba(0, 0, 0, 0.01),
    0px 9px 5px 0px rgba(0, 0, 0, 0.02),
    0px 4px 4px 0px rgba(0, 0, 0, 0.03),
    0px 1px 2px 0px rgba(0, 0, 0, 0.04),
    0px 0px 0px 0px rgba(0, 0, 0, 0.04);
  &.hidden {
    transform: translateY(-100%);
  }
  .logo {
    width: 180px;
    height: 70px;
    object-fit: contain;
  }
  nav {
    padding-block: 5px;
  }
  .navbar_content {
    gap: 3rem;
    .navbar_links {
      gap: 2rem;
      width: 100%;
      justify-content: center;

      li {
        a {
          font-size: $text-md;
          color: $dark;
          position: relative;
          text-align: center;
          font-weight: $medium;
          &.active,
          &:hover {
            color: $primary;
          }
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
    .align_header_search {
      display: flex;
      align-items: center;
      gap: 2.5rem;
      button {
        svg {
          width: 18px;
          height: 18px;
        }
      }
      .search_wrapper {
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border-radius: 8px;
        input {
          background: transparent;
          border: none;
          font-size: $text-sm;
          padding: 0 10px;
          &:focus {
            outline: none;
            box-shadow: none;
          }
          &::placeholder {
            color: rgba($dark, 0.5);
          }
        }
      }
      .header_buttons {
        gap: 2.5rem;
        button {
          font-weight: 400;
        }
      }

      .search_wrapper {
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;

        &.show {
          display: flex;
          opacity: 1;
        }
      }
    }
  }
}

.searchContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.searchBar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  overflow: hidden;
  border: 1px solid $primary;
  border-radius: 20px;
  background: transparent;
  transition: width 0.3s ease-in-out;
  button {
    padding: 10px !important;
  }
  &.open {
    width: 300px;
  }
}

.searchButton {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.searchIcon {
  width: 20px;
  height: 20px;
  color: #555;
}

.searchInput {
  width: 0;
  padding: 0;
  border: none;
  outline: none;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  background: transparent;
  color: $white;
  & > div {
    width: 100%;
  }
  input {
    background: transparent;
    border: none;
    color: $white;
    width: 100%;
    &:focus {
      outline: none;
      color: $white;
    }
  }
  .searchBar.open & {
    opacity: 1;
    width: 100%;
    padding: 8px;
  }
}
.header_right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2rem;
}
// dropdown menu style
.user_drop {
  display: flex;
  justify-content: flex-end;
  .user_drop_btn {
    display: flex;
    align-items: center;
    border: 0px;
    background-color: transparent;
    color: $dark;
    min-width: 135px;
    border-radius: 10px 10px 0px 0px;
    font-size: 14px;
    line-height: normal;
    // font-weight: $bold;
    gap: 10px;
    .admin_info {
      h5 {
        font-size: $text-md;
      }
    }

    .circle_img {
      // margin-right: 10px;
      margin-right: 0px;
      img {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 100px;
        object-fit: cover;
      }
    }
    svg {
      margin-left: 10px;
      width: 14px;
      min-width: 14px;
    }
    &:after {
      display: none;
    }
  }

  .dropdown_menu {
    border-radius: 0px 0px 8px 8px;
    background: #fff;
    box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.1);
    padding: 10px 15px;
    backdrop-filter: blur(11px);
    min-width: 170px;
    position: relative;
    z-index: 10;
    position: absolute;
    top: 50px;
    border: none;
    display: block;

    li {
      border-top: 1px solid rgba($white, 0.08);
      font-size: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;
      cursor: pointer;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

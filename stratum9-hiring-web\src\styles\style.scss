@use "./abstracts" as *;
@use "./components/buttons";
@use "./components/cards";
@use "./components/input";
@use "./components/table";
@use "./components/uploadBox";
@use "./components/commonModals";
@use "./components/editor";
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap");

* {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

html {
  font-size: 62.5%;
}
// Temp Solution for Sun Job Editor bold and italic issue fix
.sun-editor-editable {
  strong {
    font-weight: bold;
  }
  em {
    font-style: italic;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: $text-md;
  margin: 0px;
  padding: 0px;
  font-weight: 300;
  @media only screen and (max-width: 767px) {
    font-size: $text-md;
  }
}

a {
  font-size: $text-md;
  cursor: pointer;
  text-decoration: none !important;
  @media only screen and (max-width: 767px) {
    font-size: $text-md;
  }
}

//common font size style ----------
.font12 {
  font-size: $text-xs !important;
}

.font14 {
  font-size: $text-sm !important;
}

.font16 {
  font-size: $text-md !important;
}

.font18 {
  font-size: $text-lg !important;
}

//color style ----------
.color-primary {
  color: $primary !important;
}

.color-success {
  color: $green !important;
}

.color-dark {
  color: $dark !important;
}

.color-white {
  color: $white !important;
}

.color-secondary {
  color: $secondary !important;
}

.color-danger {
  color: $danger !important;
}

//interactions styles -----------
.cursor-pointer {
  cursor: pointer;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
input[type=file], /* FF, IE7+, chrome (except button) */
  input[type=file]::-webkit-file-upload-button {
  /* chromes and blink button */
  cursor: pointer;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

//common scrollbar style ----------
* {
  /* width */
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: #e8e0ff;
    border-radius: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 10px;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary;
    border-radius: 10px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $primary;
  }
}

//button align style ----------
.button-align {
  display: flex;
  align-items: center;
  gap: 2rem;
}

//section spacing style ----------
.common-section-spacing {
  padding: 55px 0;
}

//skeleton style ----------
.skeleton-shimmer {
  background-color: #ebebeb !important;
  // color: #eee !important;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: none;
  * {
    opacity: 0;
  }
}

.skeleton-shimmer::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, transparent, rgba(#f5f5f5, 0.9), transparent);
  animation: shimmer 1.5s infinite;
  z-index: 200;
}
.skeleton-shimmer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(#ebebeb, 0.9);
  z-index: 100;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

//Tooltip style
.responsive-tooltip {
  z-index: 9999;
  font-size: $text-sm;

  background-color: $primary !important;
  color: $white !important;          
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  
}
.react-tooltip {
  font-size: 10px !important;

}
.dotted-border {
  border-bottom: 1px dotted rgba($black, 0.5);
}

//common heading style ----------
.common-heading {
  h2 {
    color: $white;
    span {
      color: $secondary;
      font-size: $heading-md;
    }
  }
  p {
    font-size: $text-sm;
    color: $white;
    span {
      color: $secondary;
    }
  }
}
.section-heading {
  h2 {
    font-size: $text-xxl;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 20px;
    span {
      color: $secondary;
      &.primary {
        color: $primary;
      }
    }
  }
  p {
    font-size: $text-md;
    color: $dark;
    font-weight: $medium;
  }
}

//avatar style ----------
.avatar-desc {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: $text-sm;
  img,
  svg {
    width: 35px;
    height: 35px;
    border-radius: 100px;
    border: 1px solid #cdcdcd;
  }
  svg {
    border: none;
    border-radius: 0;
  }
  .avatar-para {
    p {
      margin: 0;
      font-size: $text-md;
      font-weight: 500;
      &:first-child {
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }
  .button-align {
    .user {
      color: #020202;
      font-weight: 400;
    }
    p {
      font-size: $text-md;
      margin: 0;
    }
    .date-share {
      display: flex;
      align-items: center;
      gap: 1rem;
      .dot {
        width: 4px;
        height: 4px;
        opacity: 0.5;
      }
      .date {
        opacity: 0.6;
      }
      button {
        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

//common box style ----------
.information-box {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1rem;
  padding: 10px;
  background: $primary-gradient;
  p {
    font-size: $text-sm;
    color: $white;
    font-weight: $medium;
    margin: 0;
  }
}
.common-box {
  display: flex;
  gap: 20px;
  .main-content {
    flex: 1;
    width: calc(100% - 70px);
  }
}

//common page header style ----------
.common-page-header {
  padding: 40px 0;
  .breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    a {
      color: $dark;
      font-size: $text-md;
      font-weight: $bold;
      margin: 0;
      &:not(:last-child) {
        color: rgba($dark, 0.7);
        font-weight: $medium;
        &::after {
          content: "/";
          margin: 0 2px;
        }
      }
    }
    a:hover {
      text-decoration: underline;
      color: $primary;
    }
  }
}
.common-page-head-section {
  .main-heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
      font-size: 2.8rem;
      font-weight: $bold;
      color: $dark;
      margin: 0;
      // display: flex;
      // align-items: center;
      // gap: 5px;
      span {
        color: $primary;
      }
      @media (width <= 767px) {
        font-size: $text-xxl;
      }
    }
    .right-action {
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: flex-end;
      .search-input {
        min-width: 400px;
      }
    }
  }
  .description {
    font-size: $text-md;
    color: $dark;
    padding-top: 15px;
    font-weight: $medium;
  }
}

//custom dropdown style
.custom-dropdown {
  position: absolute;
  top: 26px;
  right: 5px;
  background: #fff;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px;
  min-width: 180px;
  z-index: 10;
  @extend %listSpacing;
  li {
    cursor: pointer;
    padding: 8px;
    border-bottom: 1px dashed rgba(11, 11, 11, 0.3);
    text-align: left;
    &:hover {
      color: $primary;
    }
    &:last-child {
      border-bottom: none;
    }
  }
}

//permissions card style ----------
.permissions-card {
  background: rgba($secondary, 0.1);
  border-radius: 20px;
  h3 {
    margin-bottom: 25px;
    font-weight: 700;
  }
  .checbox-group {
    padding: 15px 15px 15px 20px;
    margin: 10px 0;
    // background: rgba($commonAdminPrimary, 0.02);
    border-radius: 10px;
    @extend %listSpacing;
    &:last-child {
      margin-bottom: 0;
    }
    li {
      margin-bottom: 15px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    label {
      font-size: 1.4rem;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
      span {
        width: 16px;
        height: 16px;
        &::after {
          left: 4px;
          top: 1px;
        }
      }

      .permission-item {
        display: flex;
        flex-direction: column;
        margin-right: 20px;

        .permission-name {
          font-weight: 700;
          margin-bottom: 2px;
        }

        .permission-description {
          font-size: 0.85rem;
          color: #666;
          font-weight: normal;
        }
      }
    }
  }
}

// custom checkbox style start ---------
.container-checkbox {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 1.4rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: $dark;
}

/* Hide the browser's default checkbox */
.container-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background: $white;
  border-radius: 4px;
  border: 1px solid $dark;
}

/* On mouse-over, add a grey background color */
.container-checkbox:hover input ~ .checkmark {
  background-color: $white;
}

/* When the checkbox is checked, add a blue background */
.container-checkbox input:checked ~ .checkmark {
  background-color: $primary;
  border-color: transparent;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container-checkbox input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid $white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
// custom checkbox style end ---------

//library folder style --------
.library-folder {
  @extend %listSpacing;
  margin: 50px 0 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 1rem;
  height: fit-content;
  .folder-card {
    border-radius: 10px;
    border: 2px solid rgba(0, 0, 0, 0.06);
    display: inline-block;
    padding: 10px 15px;
    width: 15.9%;
    cursor: pointer;
    text-align: center;
    & > div {
      display: flex;
      justify-content: flex-end;
    }
    button {
      svg {
        transform: rotate(90deg);
        width: 20px;
        opacity: 0.5;
      }
    }
    p {
      font-size: 1.6rem;
      text-align: center;
      font-weight: $semiBold;
      margin-top: 5px;
      margin-bottom: 15px;
    }
    .folder-icon {
      width: 145px;
      height: auto;
      object-fit: contain;
    }
  }
}
//dot css
.green-dot {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 7px;
    right: -20px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: $green;
  }
}

.red-dot {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 7px;
    right: -20px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: $danger;
  }
}

// Skeleton loading animation
@keyframes skeletonPulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-pulse {
  background-color: #e0e0e0;
  animation: skeletonPulse 1.5s ease-in-out infinite;
  border-radius: 4px;
}

// Assessment Summary Swiper style
.fas-swiper-container {
  position: relative;
  margin-bottom: 20px;
  .summary-text-card {
    margin-bottom: 10px;
  }
  .swiper-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .swiper-pagination-text {
      width: auto;
      font-size: $text-md;
      font-weight: $bold;
      .swiper-pagination-current {
        color: $primary;
      }
      .swiper-pagination-total {
        color: $dark;
      }
    }
    .custom-nav-buttons {
      gap: 15px;
      display: flex;
      align-items: center;
      button {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: none;
        cursor: pointer;
        // background: $secondary;
        background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
        // background-clip: text;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        &.swiper-button-disabled {
          background: rgba($dark, 0.1);
          cursor: not-allowed;
          svg {
            stroke: rgba($dark, 0.5);
          }
        }
        svg {
          stroke: $white;
          width: 16px;
          height: 16px;
        }
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.video_call_section {
  border: 1px solid rgba($dark, 0.1);
  border-radius: 12px;
  .interview-content {
    padding: 20px;
    .common-page-head-section {
      .main-heading {
        margin-bottom: 20px;
        h2 {
          font-size: 1.8rem;
        }
      }
    }
    .interview-question-cards-height {
      height: 60vh;
      overflow-y: auto;
    }
  }
}

.sun-editor {
  border-radius: 9px !important;
  .se-toolbar {
    background-color: $primary !important; /* Toolbar background to blue */
    border-bottom: 1px solid $primary !important; /* Slightly darker border for contrast */
    border-radius: 8px !important; /* Add border radius for rounded corners */
  }
  .se-btn-tray button {
    color: $white !important; /* Button icons/text to white for contrast */
    background-color: $primary !important; /* Ensure buttons blend with toolbar */
  }
  .se-btn-tray button:hover {
    background-color: $primary !important; /* Darker blue on hover */
  }
  .se-toolbar-separator-vertical {
    background-color: $white !important; /* Separator lines to white for visibility */
  }
}

// career skills card height styles
.career-based-skills {
  .career-skill-card {
    min-height: 150px;
    @media (max-width: 1400px) and (min-width: 1200px) {
      min-height: 170px;
    }
  }
}
// role skills card height styles
.role-based-skills {
  .career-skill-card {
    min-height: 125px;
  }
}
// culture skills card height styles
.culture-based-skills {
  .career-skill-card {
    min-height: 125px;
  }
}
// calendar styles
.calendar-container {
  .fc-header-toolbar {
    .fc-button-group {
      button {
        border: none;
        border-radius: 16px;
        font-size: 14px;
        border: solid 1px transparent;
        transition: background-color 0.2s ease;
        background-color: $white !important;
        color: rgba($dark, 0.6) !important;
        border-color: rgba($dark, 0.09) !important;
        font-weight: $semiBold;
        text-transform: capitalize;
        box-shadow:
          rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
          rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
        &:hover {
          background-color: $dark !important;
          color: $white !important;
          border-color: $dark !important;
        }
        &:focus {
          box-shadow: none !important;
        }
      }
    }
    .fc-toolbar-title {
      color: rgba($dark, 0.8) !important;
      font-weight: $bold !important;
      padding: 6px 20px;
      border-radius: 8px;
      font-size: 1.4rem !important;
      border: solid 1px transparent;
      text-transform: capitalize;
      box-shadow:
        rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
        rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    }
  }
  table {
    thead {
      tr {
        th {
          .fc-col-header-cell-cushion {
            color: rgba($dark, 0.8);
            font-weight: $medium;
            font-size: 1.4rem;
            padding: 15px 0;
          }
        }
      }
    }
    tbody {
      tr {
        td {
          .fc-timegrid-slot-label-frame {
            padding: 15px 0;
            font-size: 1.2rem;
          }
        }
      }
    }
    .fc-day-today {
      background-color: rgba(#34a853, 0.1) !important;
      .fc-daygrid-day-number {
        color: #34a853 !important;
        font-weight: $bold;
      }
    }
  }
  .fc-dayGridMonth-view,
  .fc-view-harness {
    border: 1.5px solid rgba($dark, 0.2);
    border-radius: 12px;
    box-shadow: 0px 60px 120px 0px rgba(38, 51, 77, 0.05);
    overflow: hidden;
    * {
      a {
        color: $dark;
      }
    }
    table {
      thead {
        tr {
          th {
            .fc-col-header-cell-cushion {
              color: rgba($dark, 0.8);
              font-weight: $medium;
              font-size: 1.4rem;
              padding: 15px 0;
            }
          }
        }
      }
      .fc-day-today {
        background-color: rgba(#34a853, 0.1) !important;
        .fc-daygrid-day-number {
          color: #34a853 !important;
          font-weight: $bold;
        }
      }
    }
    .fc-col-header {
      .fc-col-header-cell-cushion {
        color: rgba($dark, 0.8);
        font-weight: $medium;
        font-size: 1.4rem;
        padding: 15px 0;
      }
    }
    .fc-event {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      background: rgba(#34a853, 0.1);
      padding: 5px;
      border: solid 1px #34a853;
      border-radius: 8px;
      .fc-event-content {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        gap: 5px;
        margin-bottom: 5px;
        .fc-event-time {
          padding: 5px;
          background: green;
          display: inline;
          color: #fff;
          border-radius: 5px;
          line-height: 1;
          font-size: 10px;
        }
        .fc-event-title {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
      div {
        font-size: 1.2rem;
        font-weight: $medium;
        color: $dark;
        line-height: 1.5;
        text-wrap: auto;
        // display: inline;
      }

      .fc-daygrid-event-dot {
        border-color: #34a853;
      }
      // &.fc-event-future {
      //   background: rgba(#ffcb33, 0.1);
      //   border-color: #ffcb33;
      //   .fc-daygrid-event-dot {
      //     border-color: #ffcb33;
      //   }
      // }
      // &.fc-event-past {
      //   background: rgba(#f63, 0.1);
      //   border-color: #f63;
      //   .fc-daygrid-event-dot {
      //     border-color: #f63;
      //   }
      // }
      // &.fc-event-today {
      //   background-color: rgba(#34a853, 0.1);
      //   border-color: #34a853;
      //   .fc-daygrid-event-dot {
      //     border-color: #34a853;
      //   }
      // }
    }
  }
  .fc-daygrid-more-link {
    font-size: 1.2rem;
    background: transparent;
    padding: 5px 5px;
    border-radius: 5px;
    line-height: 1;
    color: $primary !important;
    width: 100%;
    text-align: center;
    font-weight: $bold;
    border: solid 1px $primary;
    border-radius: 5px;
    text-transform: capitalize;
    &:hover {
      background-color: $dark !important;
      color: $white !important;
      border-color: $dark !important;
    }
  }
  .fc-more-popover {
    .fc-popover-title {
      font-size: 1.2rem;
    }
    .fc-popover-body {
      .fc-daygrid-event-harness {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
        .fc-event {
          // background-color: $white;
          // border-radius: 4px;
          // border: solid 1px $dark;
          // font-size: 12px;
          // font-weight: $medium;

          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
          background: #34a8531a;
          padding: 5px;
          border: 1px solid #34a853;
          border-radius: 8px;
          .fc-daygrid-event-dot {
            border-color: $dark;
          }
          // &:hover {
          //   background-color: $primary;
          //   color: $white;
          //   border-color: $primary;
          //   .fc-daygrid-event-dot {
          //     border-color: $white;
          //   }
          // }
          // &.fc-day-future {
          //   background: rgba(#ffcb33, 0.1);
          //   border-color: #ffcb33;
          //   .fc-popover {
          //     .fc-popover-body {
          //       .fc-daygrid-event-harness {
          //         .fc-daygrid-event-dot {
          //           border-color: #ffcb33;
          //         }
          //       }
          //     }
          //   }
          // }
          // &.fc-day-past {
          //   background: rgba(#f63, 0.1);
          //   border-color: #f63;
          //   .fc-popover {
          //     .fc-popover-body {
          //       .fc-daygrid-event-harness {
          //         .fc-daygrid-event-dot {
          //           border-color: #f63;
          //         }
          //       }
          //     }
          //   }
          // }
          // &.fc-event-today {
          //   background: rgba(#34a853, 0.1);
          //   border-color: #34a853;
          //   .fc-popover {
          //     .fc-popover-body {
          //       .fc-daygrid-event-harness {
          //         .fc-daygrid-event-dot {
          //           border-color: #34a853;
          //         }
          //       }
          //     }
          //   }
          // }
        }
      }
    }
  }

  // .fc-event-future {
  //   background: rgba(#ffcb33, 0.1);
  //   border-color: #ffcb33;
  //   .fc-daygrid-event-dot {
  //     border-color: #ffcb33;
  //   }
  //   .fc-event-main {
  //     color: $dark;
  //     font-size: 1.2rem;
  //   }
  // }
  // .fc-event-past {
  //   background: rgba(#f63, 0.1);
  //   border-color: #f63;
  //   color: $dark;
  //   .fc-daygrid-event-dot {
  //     border-color: #f63;
  //   }
  //   .fc-event-main {
  //     color: $dark;
  //     font-size: 1.2rem;
  //   }
  // }
  // .fc-event-today {
  //   background-color: rgba(#34a853, 0.1);
  //   border-color: #34a853;
  //   color: $dark;
  //   .fc-daygrid-event-dot {
  //     border-color: #34a853;
  //   }
  //   .fc-event-main {
  //     color: $dark;
  //     font-size: 1.2rem;
  //   }
  // }
}
